const express = require("express");
const router = express.Router();
const userItemController = require("../controller/userItemController");
const authenticateJWT = require("../auth/auth");

/* --------------------------------------------------------------------------------- 
  Endpoint to convert items held by the user for 10 days into points.
-----------------------------------------------------------------------------------*/
router.post("/user_item_age_check", authenticateJWT, userItemController.itemAgeCheck);

/* --------------------------------------------------------------------- 
  Endpoint to get items owned by the user.
-----------------------------------------------------------------------*/
router.get("/user_get_items", authenticateJWT, userItemController.getUserItems);

/* ---------------------------------------------------------------------
  Endpoint to get top spenders from last week.
-----------------------------------------------------------------------*/
router.get("/top_spenders_last_week", userItemController.getTopSpendersLastWeek);

module.exports = router;
