const express = require("express");
const router = express.Router();
const paymentController = require("../controller/paymentController");
const authenticateJWT = require("../auth/auth");

/* --------------------------------------------------------------------- 
ユーザー課金後、レスポンスが正しければ所持ポイントを更新する
-----------------------------------------------------------------------*/
router.post("/user_run_payment", authenticateJWT, paymentController.runPayment);

/* --------------------------------------------------------------------- 
finCode決済登録を行う
-----------------------------------------------------------------------*/
router.post("/user_add_payment", authenticateJWT, paymentController.addPayment);

/* --------------------------------------------------------------------- 
finCode決済情報の取得、およびポイント付与
-----------------------------------------------------------------------*/
router.post("/user_get_payment_info", authenticateJWT, paymentController.getPaymentInfoAndAddPoint);

/* --------------------------------------------------------------------- 
finCode決済登録及び、決済実行を行い、ユーザーにバーチャル口座を提供(銀行)
-----------------------------------------------------------------------*/
router.post("/user_add_bank_payment", authenticateJWT, paymentController.addBankPayment);

/* --------------------------------------------------------------------- 
finCode決済通知受け取り(銀行)
-----------------------------------------------------------------------*/
router.post("/user_get_bank_payment_info", paymentController.getBankPaymentInfo);

/* --------------------------------------------------------------------- 
amazonPay署名生成を行う
-----------------------------------------------------------------------*/
router.post("/user_amazon_pay_create_signature", authenticateJWT, paymentController.amazonPayCreateSignature);

/* --------------------------------------------------------------------- 
amazonPayチェックアウトセッションを取得する
-----------------------------------------------------------------------*/
router.post("/user_amazon_pay_get_checkout_session", authenticateJWT, paymentController.amazonPayGetCheckoutSession);

/* --------------------------------------------------------------------- 
amazonPayチェックアウトセッションを更新する
-----------------------------------------------------------------------*/
router.post("/user_amazon_pay_update_checkout_session", authenticateJWT, paymentController.amazonPayUpdateCheckoutSession);

/* --------------------------------------------------------------------- 
amazonPayチェックアウトセッションを完了する
-----------------------------------------------------------------------*/
router.post("/user_amazon_pay_complete_checkout_session", authenticateJWT, paymentController.amazonPayCompleteCheckoutSession);

module.exports = router;
