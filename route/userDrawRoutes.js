const express = require("express");
const router = express.Router();
const userDrawController = require("../controller/userDrawController");
const authenticateJWT = require("../auth/auth");

/* --------------------------------------------------------------------- 
ユーザーがガチャを引くエンドポイント
-----------------------------------------------------------------------*/
router.post("/user_start_draw", authenticateJWT, userDrawController.userDraw);

/* --------------------------------------------------------------------- 
ユーザーのガチャ結果を取得するエンドポイント
-----------------------------------------------------------------------*/
router.get("/get_user_draw_result", authenticateJWT, userDrawController.getUserDrawResult);

module.exports = router;
