const express = require("express");
const router = express.Router();
const authController = require("../controller/authController");
const authenticateJWT = require("../auth/auth");

const rateLimit = require("express-rate-limit");

// IPアドレスごとのログイン用レートリミッターを設定
const loginLimiter = rateLimit({
  windowMs: 5 * 60 * 1000, // 5分間のウィンドウ
  max: 10, // 最大10回の試行を許可
  message: "多数のログイン試行が検出されました。5分後に再試行してください。",
  standardHeaders: true, // レート制限情報を標準ヘッダーに含める
  legacyHeaders: false, // 非推奨のX-RateLimit-*ヘッダーを無効化
});

/* --------------------------------------------------------------------- 
電話番号認証
-----------------------------------------------------------------------*/
router.post("/tel_auth", authenticateJWT, authController.telAuth);


/* --------------------------------------------------------------------- 
Google認証
-----------------------------------------------------------------------*/
router.post("/google_auth", authController.googleAuth);

/* --------------------------------------------------------------------- 
SMSに認証コード送信
-----------------------------------------------------------------------*/
router.post("/tel_auth_send_code", authController.telAuthSendCode);

/* --------------------------------------------------------------------- 
LINEログインで使うステートをセッションに登録
-----------------------------------------------------------------------*/
router.post("/line_add_state", authController.lineStateAdd);

/* ------------------------------------------------------------------------------------------------------------------------------- 
LINEステート認証後、送られてくるアクセストークンを使用して、ユーザー情報を取得、メールアドレスを抜き取りデータベースに登録
---------------------------------------------------------------------------------------------------------------------------------*/
router.post("/line_state_auth", authController.lineStatusAuth);

/* --------------------------------------------------------------------- 
メールに認証コード送信
-----------------------------------------------------------------------*/
router.post("/mail_auth_send_code", authController.mailAuthSendCode);

/* --------------------------------------------------------------------- 
メール認証
-----------------------------------------------------------------------*/
router.post("/mail_auth", authController.mailAuth);

/* --------------------------------------------------------------------- 
通常ログイン
-----------------------------------------------------------------------*/
router.post("/user_login", loginLimiter, authController.userLogin);

module.exports = router;
