const express = require("express");
const router = express.Router();
const userRankController = require("../controller/userRankController");
const authenticateJWT = require("../auth/auth");
const AWS = require("aws-sdk");
const multer = require("multer");

// 初始化 AWS S3 实例
const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || "us-east-1",
});

// Multer 配置（内存存储）
const upload = multer({
    storage: multer.memoryStorage(),
}).any(); // 接收任意数量的文件字段


/* ---------------------------------------------------------------------
ユーザーのランクを取得するルーター
-----------------------------------------------------------------------*/
router.get("/user_get_rank", authenticateJWT, userRankController.getUserRank);

/* ---------------------------------------------------------------------
ユーザーのランクに基づいた、アイテム課金時のボーナスポイント率を取得するルーター
-----------------------------------------------------------------------*/
router.get("/get_user_buy_bonus", authenticateJWT, userRankController.getBuyBonus);

router.post("/admin_create_rank", authenticateJWT, upload, userRankController.adminAddRank);

module.exports = router;
