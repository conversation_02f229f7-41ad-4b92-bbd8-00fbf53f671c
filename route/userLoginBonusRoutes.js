const express = require("express");
const router = express.Router();
const userLoginBonusController = require("../controller/userLoginBonusController");
const authenticateJWT = require("../auth/auth");

/* --------------------------------------------------------------------- 
ユーザーに該当のログインボーナスが存在するかを確認する
-----------------------------------------------------------------------*/
router.get("/search_user_login_bonus", authenticateJWT, userLoginBonusController.searchUserLoginBonus);

/* --------------------------------------------------------------------- 
ユーザーがログインボーナスを実行する
-----------------------------------------------------------------------*/
router.get("/get_user_login_bonus", authenticateJWT, userLoginBonusController.executeUserLoginBonus);

module.exports = router;
