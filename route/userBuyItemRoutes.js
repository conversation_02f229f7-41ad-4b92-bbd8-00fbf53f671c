const express = require("express");
const router = express.Router();
const userBuyItemController = require("../controller/userBuyItemController");
const authenticateJWT = require("../auth/auth");

/* --------------------------------------------------------------------- 
課金アイテム一覧を取得するエンドポイント
-----------------------------------------------------------------------*/
router.get("/user_get_buy_items", authenticateJWT, userBuyItemController.getBuyItems);

/* --------------------------------------------------------------------- 
該当ユーザーが取得している課金アイテムのクーポンを取得するエンドポイント
-----------------------------------------------------------------------*/
router.get("/user_get_buy_items_coupon", authenticateJWT, userBuyItemController.getCoupon);

module.exports = router;
