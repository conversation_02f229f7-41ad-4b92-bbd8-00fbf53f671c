const express = require("express");
const router = express.Router();
const userTransactionController = require("../controller/userTransactionController");
const authenticateJWT = require("../auth/auth");
router.get("/transaction_records", authenticateJWT, userTransactionController.getTransactionRecords);
router.get("/recharge_consume_records", authenticateJWT, userTransactionController.getRechargeAndConsumeRecords);
router.get("/latest_point_change", authenticateJWT, userTransactionController.getLatestPointChange);
module.exports = router; 