const express = require("express");
const router = express.Router();
const adminRankController = require("../controller/adminRankController");
const adminPackController = require("../controller/adminPackController");
const adminBuyItemController = require("../controller/adminBuyItemController");
const adminLoginBonusController = require("../controller/adminLoginBonusController");
const adminCouponController = require("../controller/adminCouponController");
const adminUserController = require("../controller/adminUserController");
const adminShipItemController = require("../controller/adminShipItemController");
const adminInfoController = require("../controller/adminInfoController");
const authenticateJWT = require("../auth/admin-auth");
const multer = require("multer");
const { v4: uuidv4 } = require("uuid");
const path = require("path");
const { getRechargeRecordsForAdmin } = require("../controller/userTransactionController");

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "uploads/");
  },
  filename: (req, file, cb) => {
    // 拡張子を保持
    const ext = path.extname(file.originalname);
    console.log("ext" + ext);
    // UUIDで一意のファイル名を作成
    const uniqueName = uuidv4() + ext;
    console.log("uniqueName" + uniqueName);

    cb(null, uniqueName);
  },
});

// const upload = multer({ storage });
// Multer 配置（改为内存存储）
const upload = multer({
  storage: multer.memoryStorage(), // 将文件存储在内存中，而不是磁盘
}); // 支持解析任意数量的文件字段
/* ---------------------------------------------------------------------
获取所有排名信息
-----------------------------------------------------------------------*/
router.get("/admin_get_ranks", authenticateJWT, adminRankController.getRanks);

/* ---------------------------------------------------------------------
通过ID获取指定的排名信息
-----------------------------------------------------------------------*/
router.get("/admin_get_rank/:rank_id", authenticateJWT, adminRankController.getRank);

/* ---------------------------------------------------------------------
更新通过ID指定的排名信息
-----------------------------------------------------------------------*/
router.post("/admin_update_rank/:rank_id", authenticateJWT, upload.any(), adminRankController.updateRank);

/* ---------------------------------------------------------------------
更改排名顺序
-----------------------------------------------------------------------*/
router.post("/admin_change_rank_order", authenticateJWT, adminRankController.changeOrder);

/* ---------------------------------------------------------------------
删除指定的排名
-----------------------------------------------------------------------*/
router.post("/admin_remove_rank", authenticateJWT, adminRankController.removeRank);

/* ---------------------------------------------------------------------
获取礼包信息
-----------------------------------------------------------------------*/
router.get("/admin_get_pack/:id", authenticateJWT, adminPackController.getPack);

/* ---------------------------------------------------------------------
更新礼包信息
-----------------------------------------------------------------------*/
router.post("/admin_update_pack", authenticateJWT, upload.any(), adminPackController.updatePack);
// router.post("/admin_update_pack",  upload.any(),adminPackController.updatePack);

/* ---------------------------------------------------------------------
添加登录奖励
-----------------------------------------------------------------------*/
router.post("/admin_create_login_bonus", authenticateJWT, upload.any(), adminLoginBonusController.createLoginBonus);
// router.post("/admin_create_login_bonus", upload.any(), adminLoginBonusController.createLoginBonus);

/* ---------------------------------------------------------------------
获取登录奖励
-----------------------------------------------------------------------*/
router.get("/admin_get_login_bonus", authenticateJWT, adminLoginBonusController.getLoginBonus);

/* ---------------------------------------------------------------------
通过ID获取登录奖励
-----------------------------------------------------------------------*/
router.get("/admin_get_login_bonus/:id", authenticateJWT, upload.any(), adminLoginBonusController.getIdLoginBonus);

/* ---------------------------------------------------------------------
删除登录奖励
-----------------------------------------------------------------------*/
router.post("/admin_remove_login_bonus", authenticateJWT, upload.any(), adminLoginBonusController.removeLoginBonus);

/* ---------------------------------------------------------------------
更新登录奖励
-----------------------------------------------------------------------*/
router.post("/admin_update_login_bonus/:id", authenticateJWT, upload.any(), adminLoginBonusController.updateLoginBonus);

/* ---------------------------------------------------------------------
添加付费道具
-----------------------------------------------------------------------*/
router.post("/admin_add_buy_item", authenticateJWT, upload.any(), adminBuyItemController.addBuyItem);

/* ---------------------------------------------------------------------
获取付费道具
-----------------------------------------------------------------------*/
router.get("/admin_get_buy_items", authenticateJWT, adminBuyItemController.getBuyItem);

/* ---------------------------------------------------------------------
通过ID获取付费道具
-----------------------------------------------------------------------*/
router.get("/admin_get_id_buy_items", authenticateJWT, adminBuyItemController.getIdBuyItem);

/* ---------------------------------------------------------------------
删除付费道具
-----------------------------------------------------------------------*/
router.post("/admin_remove_buy_item", authenticateJWT, adminBuyItemController.removeBuyItem);

/* ---------------------------------------------------------------------
更新付费道具
-----------------------------------------------------------------------*/
router.post("/admin_update_buy_item", authenticateJWT, upload.any(), adminBuyItemController.updateBuyItem);

/* ---------------------------------------------------------------------
创建付费优惠券
-----------------------------------------------------------------------*/
router.post("/admin_create_buy_item_discount_coupon", authenticateJWT, adminCouponController.createBuyItemDiscountCoupon);

/* ---------------------------------------------------------------------
获取付费优惠券
-----------------------------------------------------------------------*/
router.get("/admin_get_discount_coupons", authenticateJWT, adminCouponController.getDiscountCoupons);

/* ---------------------------------------------------------------------
删除付费优惠券
-----------------------------------------------------------------------*/
router.post("/admin_remove_buy_item_discount_coupon", authenticateJWT, adminCouponController.removeDiscountCoupon);

/* ---------------------------------------------------------------------
搜索用户
-----------------------------------------------------------------------*/
router.get("/admin_search_users", authenticateJWT, adminUserController.searchUsers);

/* ---------------------------------------------------------------------
获取指定ID的用户信息
-----------------------------------------------------------------------*/
router.get("/admin_get_user", authenticateJWT, adminUserController.getIdUser);

/* ---------------------------------------------------------------------
更新指定ID用户的积分信息
-----------------------------------------------------------------------*/
router.post("/admin_update_user_point", authenticateJWT, adminUserController.updateUserPoint);

/* ---------------------------------------------------------------------
将指定ID的用户从管理界面隐藏
-----------------------------------------------------------------------*/
router.post("/admin_user_remove", authenticateJWT, adminUserController.removeUser);

/* ---------------------------------------------------------------------
将配送请求的物品兑换为积分
-----------------------------------------------------------------------*/
router.post("/admin_exchange_item_to_point", authenticateJWT, adminShipItemController.exchangeItemToPoint);

/* ---------------------------------------------------------------------
在管理界面中隐藏指定ID的卡池
-----------------------------------------------------------------------*/
router.post("/admin_pack_display_remove", authenticateJWT, adminPackController.adminDisplayChange);

/* ---------------------------------------------------------------------
更改卡池的排序
-----------------------------------------------------------------------*/
router.post("/admin_change_pack_order", authenticateJWT, adminPackController.changeSortPackList);

/* ---------------------------------------------------------------------
更改类别的排序
-----------------------------------------------------------------------*/
router.post("/admin_change_category_order", authenticateJWT, adminInfoController.changeSortCategoryList);

/* ---------------------------------------------------------------------
获取管理员用户的邮箱地址
-----------------------------------------------------------------------*/
router.get("/admin_get_admin_user", authenticateJWT, adminUserController.getAdminUser);

/* ---------------------------------------------------------------------
调用已注册的物品
-----------------------------------------------------------------------*/
router.get("/admin_get_added_items", authenticateJWT, adminPackController.getAddedItems);

router.get("/users/:userId/recharge-records", authenticateJWT, getRechargeRecordsForAdmin);

module.exports = router;
