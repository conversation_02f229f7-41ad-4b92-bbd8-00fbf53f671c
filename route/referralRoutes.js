const express = require("express");
const router = express.Router();

const referralController = require("../controller/referralController");
const authenticateJWT = require("../auth/auth");

// 生成邀请码：GET /referral/code?userId=123
router.get("/referral/code", referralController.generateInviteCode);

// 获取邀请码
router.get("/referral/myInviteInfo",authenticateJWT, referralController.getMyInviteInfo);

// 新增邀请记录：POST /referral/record
router.post("/referral/record", authenticateJWT, referralController.createInviteRecord);

// 获取邀请概览信息：成功邀请人数 + 已发奖励人数
router.get("/referral/getReferralSummary", referralController.getReferralSummary);

// 获取用户的所有奖励记录
router.get("/referral/getRewardRecords", referralController.getRewardRecords);

// 新增奖励记录：POST /referral/reward
router.post("/referral/reward", authenticateJWT, referralController.createRewardRecord);

// 获取所有待审核的邀请记录
router.get("/referral/pending-review", authenticateJWT, referralController.getPendingReviewRecords);

// 审核通过邀请记录
router.post("/referral/approve-record", authenticateJWT, referralController.approveInviteRecord);

module.exports = router;
