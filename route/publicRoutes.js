const express = require("express");
const router = express.Router();
const publicController = require("../controller/publicController");

/* --------------------------------------------------------------------- 
パック取得
-----------------------------------------------------------------------*/
router.get("/get_packs", publicController.getPacks);

/* --------------------------------------------------------------------- 
パック取得(カテゴリー指定あり)
-----------------------------------------------------------------------*/
router.get("/get_packs/category/:category", publicController.getPacks);

/* --------------------------------------------------------------------- 
ジャックポット取得
-----------------------------------------------------------------------*/
router.get("/get_jackpot", publicController.getJackpot);

/* --------------------------------------------------------------------- 
ガチャの詳細情報を取得
-----------------------------------------------------------------------*/
router.get("/get_pack_details/:packId", publicController.getPackDetails);

module.exports = router;
