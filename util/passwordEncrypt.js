const bcrypt = require('bcrypt');
const saltRounds = 10; // 加密强度

// 加密函数
async function hashPassword(password) {
    try {
        const salt = await bcrypt.genSalt(saltRounds);
        const hash = await bcrypt.hash(password, salt);
        return hash;
    } catch (err) {
        console.error('加密失败:', err);
        throw err;
    }
}

// 使用示例
hashPassword('Woshinidie1314&')
    .then(hashedPassword => {
        console.log('加密结果:', hashedPassword);
        // 示例输出: $2b$10$N9qo8uLOickgx2ZMRZoMy.MHr3gIIG7aBfjRjUZMOHk3w5E4fOQ1W
    });