const MAX_RETRIES = 3;

exports.withRetryTransaction = async (task, db) => {
  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    let connection;
    try {
      // 接続取得
      connection = await new Promise((resolve, reject) => {
        db.getConnection((err, conn) => {
          if (err) return reject(err);
          resolve(conn);
        });
      });

      // トランザクション開始
      await new Promise((resolve, reject) => {
        connection.beginTransaction((err) => {
          if (err) return reject(err);
          resolve();
        });
      });

      // ユーザーが渡した処理を実行
      await task(connection);

      // コミット
      await new Promise((resolve, reject) => {
        connection.commit((err) => {
          if (err) return reject(err);
          resolve();
        });
      });

      // 正常終了したら抜ける
      return;
    } catch (error) {
      // デッドロックが発生した場合、リトライtest
      if (error.code === "ER_LOCK_DEADLOCK" && attempt < MAX_RETRIES) {
        console.warn(`デッドロックが発生しました。リトライ中... (${attempt}/${MAX_RETRIES})`);
        continue;
      }

      // 別のエラー、またはリトライ上限に達した場合
      throw error;
    } finally {
      // 接続を解放
      if (connection) connection.release();
    }
  }
};
