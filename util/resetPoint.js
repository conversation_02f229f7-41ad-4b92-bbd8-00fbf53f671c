const cron = require("node-cron");
const db = require("../db/db");

// 毎日深夜12時にday_pointをリセット
cron.schedule("0 0 * * *", () => {
  db.query("UPDATE user SET day_point = 0", (err, results) => {
    if (err) {
      console.error("Error resetting day_point:", err);
    } else {
      console.log("day_point reset successfully");
    }
  });
});

// 毎週日曜日深夜12時にweekly_pointをリセット
cron.schedule("0 0 * * 0", () => {
  db.query("UPDATE user SET weekly_point = 0", (err, results) => {
    if (err) {
      console.error("Error resetting weekly_point:", err);
    } else {
      console.log("weekly_point reset successfully");
    }
  });
});

// 毎月1日深夜12時にmonth_pointをリセット
cron.schedule("0 0 1 * *", () => {
  db.query("UPDATE user SET month_point = 0", (err, results) => {
    if (err) {
      console.error("Error resetting month_point:", err);
    } else {
      console.log("month_point reset successfully");
    }
  });
});

// 毎月1日の00:00に `use_point` を0にリセットする関数
// cron.schedule(
//   "0 0 1 * *",
//   () => {
//     // use_pointがrankテーブルのpointカラムの半分未満であるユーザーを取得するクエリ
//     const checkRankPointsQuery = `
//       SELECT u.id, u.rank, u.use_point, r.point
//       FROM user u
//       JOIN \`rank\` r ON u.rank = r.\`order\`
//       WHERE u.rank IS NOT NULL
//     `;
//     // use_pointを0にリセットするクエリ
//     const resetRankPointsQuery = "UPDATE user SET use_point = 0";

//     db.query(checkRankPointsQuery, (error, results) => {
//       if (error) {
//         console.error("Failed to check rank points: ", error);
//       } else {
//         console.log("Check rank points results:", results); // デバッグ用に結果を出力

//         results.forEach((user) => {
//           // user.use_pointは使用したポイント、user.pointはランクに到達するために必要なポイント
//           if (user.use_point < user.point / 2) {
//             const newRank = user.rank > 0 ? user.rank - 1 : 0;
//             db.query(`UPDATE user SET \`rank\` = ${newRank} WHERE id = ${user.id}`, (error, results) => {
//               if (error) {
//                 console.error("Failed to update rank: ", error);
//               } else {
//                 console.log("Rank updated successfully for user id:", user.id);
//               }
//             });
//           }
//         });

//         // use_pointを0にリセットするクエリを実行
//         db.query(resetRankPointsQuery, (error, results) => {
//           if (error) {
//             console.error("Failed to reset rank points: ", error);
//           } else {
//             console.log("Rank points reset successfully.");
//           }
//         });
//       }
//     });
//   },
//   {
//     scheduled: true,
//     timezone: "Asia/Tokyo", // 日本のタイムゾーンに設定
//   }
// );

// 毎日深夜12時に`validity`ステータスのレコードをチェックして、`status`を`invalid`に更新する
cron.schedule("0 0 * * *", () => {
  // "validity"ステータスのレコードを取得
  db.query(
    `
      SELECT id, validity_at, display_days 
      FROM pack_info 
      WHERE status = 'validity'
    `,
    (err, rows) => {
      if (err) {
        console.error("Error fetching data from database:", err);
        return;
      }

      // 現在の日付を取得
      const currentDate = new Date();

      // チェックして、statusを"invalid"に更新する必要があるレコードを探す
      rows.forEach((row) => {
        const validityAt = new Date(row.validity_at);
        const displayDays = row.display_days;

        // validity_atからdisplay_days経過したかを確認
        const expirationDate = new Date(validityAt);
        expirationDate.setDate(validityAt.getDate() + displayDays);

        if (currentDate >= expirationDate) {
          // ステータスを"invalid"に更新
          db.query(
            `
                  UPDATE pack_info 
                  SET status = 'invalid' 
                  WHERE id = ?
              `,
            [row.id],
            (err, result) => {
              if (err) {
                console.error(`Error updating record with id ${row.id}:`, err);
                return;
              }

              console.log(`Record with id ${row.id} updated to 'invalid'.`);
            }
          );
        }
      });

      console.log("Cron job executed successfully!");
    }
  );
});

// 毎日深夜12時に`sold-out`ステータスのレコードをチェックしてpack_settingsのsold_out_display_daysカラムをチェックして、`status`を`invalid`に更新する
cron.schedule("0 0 * * *", () => {
  db.query(
    `
      SELECT pi.id, pi.sold_out_at, ps.sold_out_display_days
      FROM pack_info pi
      JOIN pack_settings ps ON ps.id = 1  -- pack_settingsのidが1のレコードを使用
      WHERE pi.status = 'sold-out'
    `,
    (err, rows) => {
      if (err) {
        console.error("Error fetching data from database:", err);
        return;
      }

      // 現在の日付を取得
      const currentDate = new Date();

      // チェックして、statusを"invalid"に更新する必要があるレコードを探す
      rows.forEach((row) => {
        const soldOutAt = new Date(row.sold_out_at);
        const soldOutDisplayDays = row.sold_out_display_days;

        // sold_out_atからsold_out_display_days経過したかを確認
        const expirationDate = new Date(soldOutAt);
        expirationDate.setDate(soldOutAt.getDate() + soldOutDisplayDays);

        if (currentDate >= expirationDate) {
          // ステータスを"invalid"に更新
          db.query(
            `
              UPDATE pack_info 
              SET status = 'invalid' 
              WHERE id = ?
            `,
            [row.id],
            (err, result) => {
              if (err) {
                console.error(`Error updating record with id ${row.id}:`, err);
                return;
              }

              console.log(`Record with id ${row.id} updated to 'invalid'.`);
            }
          );
        }
      });

      console.log("Cron job executed successfully!");
    }
  );
});
