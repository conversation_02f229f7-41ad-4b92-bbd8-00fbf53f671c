const mysql = require("mysql");
require("dotenv").config();

// データベースの接続設定
const connection = mysql.createConnection({
  host: process.env.DATABASE_HOST,
  user: process.env.DATABASE_USER,
  password: process.env.DATABASE_PASSWORD,
  database: process.env.DATABASE_NAME,
  port: process.env.DATABASE_PORT,
});

// MySQLデータベースに接続
connection.connect((err) => {
  if (err) {
    console.error("Error connecting to MySQL:", err);
    return;
  }
  console.log("MySQL connected...");
  insertItems();
});

// アイテムデータを生成して挿入する関数
function insertItems() {
  const items = [];
  const category = "itemsC";
  const image = "uploads/point_change.png";

  for (let price = 10; price <= 5000; price += 10) {
    const name = `${price}P交換用`;
    const item = {
      category: category,
      name: name,
      price: price,
      image: image,
    };
    items.push(item);
  }

  // データベースに挿入
  const sql = "INSERT INTO items_loser (category, name, price, image, is_exchange_only) VALUES ?";
  const values = items.map((item) => [item.category, item.name, item.price, item.image, 1]);

  connection.query(sql, [values], (err, result) => {
    if (err) {
      console.error("Error inserting data:", err);
      return;
    }
    console.log(`Inserted ${result.affectedRows} rows`);
    connection.end();
  });
}
