// mailer.js
const nodemailer = require("nodemailer");
require("dotenv").config();

const createTransporter = () => {
  let transporterOptions;

  if (process.env.NODE_ENV === "production") {
    transporterOptions = {
      host: "smtp.gmail.com",
      port: 465,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_YOUR_PASSWORD,
      },
    };
  } else {
    transporterOptions = {
      host: "smtp.gmail.com",
      port: 465,
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_YOUR_PASSWORD,
      },
    };
  }

  return nodemailer.createTransport(transporterOptions);
};

module.exports = createTransporter;
