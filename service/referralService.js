// services/referralService.js

const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

// 工具函数：生成6位随机邀请码（大写字母+数字）
function generateInviteCode(length = 8) {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let code = '';
    for (let i = 0; i < length; i++) {
        code += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return code;
}

/**
 * 为指定用户生成邀请码并写入数据库
 * @param {number} userId 用户ID
 * @returns {Promise<string>} 生成的邀请码
 */
async function generateAndSaveInviteCode(userId) {
    return new Promise((resolve, reject) => {
        // 检查是否已有激活码
        const checkQuery = "SELECT code FROM referral_invite_codes WHERE user_id = ? AND is_active = TRUE LIMIT 1";
        db.query(checkQuery, [userId], (err, results) => {
            if (err) {
                logErr(`用户[${userId}]检查邀请码时发生数据库错误`);
                return reject("数据库错误");
            }

            if (results.length > 0) {
                const existingCode = results[0].code;
                logUserPut(`用户[${userId}]已有激活邀请码：${existingCode}`);
                return resolve(existingCode);
            }

            // 生成新邀请码
            const newCode = generateInviteCode();

            const insertQuery = "INSERT INTO referral_invite_codes (user_id, code, is_active) VALUES (?, ?, TRUE)";
            db.query(insertQuery, [userId, newCode], (err) => {
                if (err) {
                    logErr(`用户[${userId}]生成邀请码失败，错误信息：${err}`);
                    return reject("生成邀请码失败");
                }

                logUserPut(`用户[${userId}]成功生成邀请码：${newCode}`);
                resolve(newCode);
            });
        });
    });
}

/**
 * 根据邀请码获取邀请人ID
 * @param {string} referralCode 邀请码
 * @returns {Promise<number|null>} 邀请人ID 或 null（无效邀请码）
 */
async function getInviterIdByCode(referralCode) {
    return new Promise((resolve, reject) => {
        const query = "SELECT user_id FROM referral_invite_codes WHERE code = ? AND is_active = TRUE";
        db.query(query, [referralCode], (err, results) => {
            if (err) {
                logErr(`邀请码[${referralCode}]查询过程中发生数据库错误`);
                return reject(err);
            }

            if (results && results.length > 0) {
                const inviterId = results[0].user_id;
                logUserPut(`邀请码[${referralCode}]对应的邀请人ID为：${inviterId}`);
                return resolve(inviterId);
            }

            logUserPut(`邀请码[${referralCode}]无效或已失效`);
            resolve(null);
        });
    });
}

/**
 * 记录邀请关系到数据库（防止自邀 & 防止超过邀请上限）
 * @param {number} inviterId 邀请人ID
 * @param {number} inviteeId 被邀请人ID
 * @param {string} inviteCodeUsed 使用的邀请码
 * @returns {Promise<void>}
 */
async function recordInvite(inviterId, inviteeId, inviteCodeUsed) {
    return new Promise((resolve, reject) => {
        if (!inviterId || !inviteeId) {
            logErr("邀请人或被邀请人ID为空");
            return reject("inviterId 和 inviteeId 必须存在");
        }

        // 核心校验：防止邀请自己
        if (inviterId === inviteeId) {
            logUserPut(`用户[${inviteeId}]尝试使用自己的邀请码注册，已阻止`);
            return resolve(); // 直接返回成功，不抛错
        }

        // 新增校验：每个用户最多只能邀请 10 个用户
        const countQuery = "SELECT COUNT(*) AS count FROM referral_invite_records WHERE inviter_id = ?";
        db.query(countQuery, [inviterId], (err, results) => {
            if (err) {
                logErr(`邀请人[${inviterId}]查询邀请人数时发生错误，错误内容:${err}`);
                return reject(err);
            }

            const currentCount = results[0].count || 0;

            if (currentCount >= 10) {
                logUserPut(`邀请人[${inviterId}]已达最大邀请人数限制（10人）`);
                return resolve(); // 阻止邀请，但不中断注册流程
            }

            // 可以继续插入邀请记录
            const insertQuery = `
                INSERT INTO referral_invite_records 
                (inviter_id, invitee_id, invite_code_used) 
                VALUES (?, ?, ?)
            `;

            db.query(insertQuery, [inviterId, inviteeId, inviteCodeUsed], (err) => {
                if (err) {
                    logErr(`邀请人[${inviterId}]添加邀请记录失败，错误内容:${err}`);
                    return reject(err);
                }

                logUserPut(`邀请人[${inviterId}]为被邀请人[${inviteeId}]添加了邀请记录`);
                resolve();
            });
        });
    });
}

/**
 * 获取邀请概览信息：成功邀请人数 + 已发奖励人数
 * @param {number} inviterId 邀请人ID
 * @returns {Promise<{invitedCount: number, rewardedCount: number}>}
 */
async function getInviteSummary(inviterId) {
    return new Promise((resolve, reject) => {
        const query = `
            SELECT 
                COUNT(1) AS invited_count,
                COUNT(CASE WHEN is_reward_given = 1 THEN 1 END) AS rewarded_count
            FROM referral_invite_records
            WHERE inviter_id = ?
        `;

        db.query(query, [inviterId], (err, results) => {
            if (err) {
                logErr(`邀请人[${inviterId}]获取邀请概览失败，错误内容:${err}`);
                return reject(err);
            }

            const result = results[0];
            const summary = {
                invitedCount: parseInt(result.invited_count || '0', 10),
                rewardedCount: parseInt(result.rewarded_count || '0', 10)
            };

            logUserPut(`邀请人[${inviterId}]邀请概览：邀请人数[${summary.invitedCount}], 已发奖[${summary.rewardedCount}]`);
            resolve(summary);
        });
    });
}

/**
 * 获取某个邀请人的所有奖励发放记录
 * @param {number} inviterId 邀请人ID
 * @returns {Promise<Array>} 奖励记录数组
 */
async function getRewardRecordsByInviter(inviterId) {
    return new Promise((resolve, reject) => {
        const query = `
            SELECT 
                reward_type AS rewardType,
                reward_value AS rewardValue,
                given_at AS givenAt
            FROM referral_reward_records
            WHERE inviter_id = ?
            ORDER BY given_at DESC
        `;

        db.query(query, [inviterId], (err, results) => {
            if (err) {
                logErr(`邀请人[${inviterId}]获取奖励记录失败，错误内容:${err}`);
                return reject(err);
            }

            // 格式化数据
            const records = results.map(row => ({
                rewardType: row.rewardType,
                rewardValue: row.rewardValue,
                givenAt: row.givenAt
            }));

            logUserPut(`邀请人[${inviterId}]共获取 ${records.length} 条奖励记录`);
            resolve(records);
        });
    });
}

module.exports = {
    generateAndSaveInviteCode,
    getInviterIdByCode,
    recordInvite,
    getInviteSummary,
    getRewardRecordsByInviter
};
