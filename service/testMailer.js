// testMailer.js
const nodemailer = require("nodemailer");
require("dotenv").config();

const sendTestEmail = async () => {
  try {
    // トランスポーターを作成
    const createTransporter = () => {
      let transporterOptions;
      transporterOptions = {
        host: "localhost",
        port: 25,
        secure: false,
        auth: {
          user: "info",
          pass: "test123",
        },
      };
      return nodemailer.createTransport(transporterOptions);
    };

    const transporter = createTransporter();

    // メールのオプションを設定
    const mailOptions = {
      from: "<EMAIL>",
      to: "<EMAIL>",
      subject: "テストメール - プロダクション環境",
      text: "これはプロダクション環境からのテストメールです。",
    };

    // メールを送信
    await transporter.sendMail(mailOptions);
    console.log("テストメールが送信されました。");
  } catch (error) {
    console.error("メール送信エラー:", error);
  }
};

sendTestEmail();
