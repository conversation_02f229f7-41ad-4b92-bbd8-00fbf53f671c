const jwt = require("jsonwebtoken");
require("dotenv").config();

const secretKey = process.env.AUTH_JWT_SECRET_KEY;

// JWT認証ミドルウェア
const authenticateJWT = (req, res, next) => {
  // クッキーからトークンを取得
  const token = req.cookies["oripa-user-token"];
  if (token) {
    jwt.verify(token, secretKey, (err, user) => {
      if (err) {
        return res.sendStatus(403); // トークンが無効な場合
      }
      req.user = user; // トークンが有効な場合、ペイロード情報を保存
      next();
    });
  } else {
    next();
  }
};

module.exports = authenticateJWT;
