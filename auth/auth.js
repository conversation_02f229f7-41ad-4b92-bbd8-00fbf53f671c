const jwt = require("jsonwebtoken");
const Auth = require("../model/auth");
const { logErr, logAdminPut, logUserPut, logAccountBanPut } = require("../logger");
require("dotenv").config();

// 获取密钥并判断是否存在
const secretKey = process.env.AUTH_JWT_SECRET_KEY;
if (!secretKey) {
  throw new Error("❌ AUTH_JWT_SECRET_KEY 未在 .env 中设置！");
}

const authenticateJWT = async (req, res, next) => {
  const cookieToken = req.cookies["oripa-user-token"];
  const authHeader = req.headers["authorization"];
  const headerToken = authHeader && typeof authHeader === "string"
      ? authHeader.split(" ")[1]
      : undefined;

  console.log("📎 请求头 Authorization:", authHeader);
  console.log("📎 Cookie Token 存在:", !!cookieToken);
  console.log("📎 Header Token 存在:", !!headerToken);

  let user;

  try {
    // ✅ 只处理你自己的 JWT
    const token = headerToken || cookieToken;
    if (!token) {
      console.warn("🚫 拒绝访问: 无 Token 提供");
      return res.sendStatus(401);
    }

    // ✅ 使用 jsonwebtoken 解析你自己的 JWT
    user = await new Promise((resolve, reject) => {
      jwt.verify(token, secretKey, (err, decoded) => {
        if (err) {
          console.error("❌ Token 验证失败:", err.message);
          return reject(err);
        }
        console.log("✅ Token 解析成功:", decoded);
        resolve(decoded);
      });
    });

    req.user = user;

    // ✅ 检查数据库是否存在该用户（普通用户 + Google 用户统一处理）
    const userId = req.user.userId;
    const userMail = await Auth.getUserMail(userId);

    if (!userMail) {
      console.warn(`❌ 用户 [${userId}] 邮箱未找到`);
      logErr(`User [${userId}] email not found. - authenticateJWT`);
      return res.sendStatus(404);
    }

    if (userMail === "user_ban") {
      console.warn(`🔒 用户 [${userId}] 已被封禁`);

      res.clearCookie("oripa-user-token", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "Strict",
      });

      logUserPut(`User [${userId}] is banned. Token removed. - authenticateJWT`);
      logErr(`User [${userId}] is banned. Token removed. - authenticateJWT`);
      logAccountBanPut(`User [${userId}] is banned. Token removed. - authenticateJWT`);

      return res.sendStatus(403);
    }

    console.log(`🧑 用户验证成功，邮箱: ${userMail}`);
    console.log("✅ 认证成功，继续处理请求...");
    next();

  } catch (err) {
    console.error("🚫 认证过程中发生错误:", err.message);

    // 出错时清除 Cookie（如果存在）
    if (cookieToken) {
      console.warn("🧹 清除无效的 Cookie Token");

      res.clearCookie("oripa-user-token", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "Strict",
      });
    }

    res.sendStatus(403); // Access denied
  }
};

module.exports = authenticateJWT;
