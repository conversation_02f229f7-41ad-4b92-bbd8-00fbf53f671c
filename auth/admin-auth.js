const jwt = require("jsonwebtoken");
const Auth = require("../model/auth");
require("dotenv").config();
const { logUserPut, logErr, logAccountBanPut } = require("../logger");

const secretKey = process.env.AUTH_ADMIN_JWT_SECRET_KEY;

// JWT認証ミドルウェア
const authenticateJWT = async (req, res, next) => {
  const token = req.cookies["oripa-admin-token"];
  console.log("Received Token:", token);
  if (!token) {
    return res.sendStatus(401); // トークンがない場合
  }
  console.log("Secret Key for verifying:", secretKey);
  const decoded = jwt.decode(token);
  console.log("Decoded Token:", decoded);
  try {
    // jwt.verify を Promise 化して非同期処理を扱う
    const user = await new Promise((resolve, reject) => {
      jwt.verify(token, secretKey, (err, decoded) => {
        if (err) {
          if (err.name === "TokenExpiredError") {
            console.error("Token expired:", err.message);
            return reject(new Error("Token has expired."));
          }
          console.error("JWT verification failed:", err.message);
          return reject(new Error("Invalid token signature."));
        }
        resolve(decoded);
      });
    });

    req.user = user; // トークンが有効な場合、ペイロード情報を保存
    // ユーザーIDを取得してメールを確認
    const userId = req.user.userId;
    console.log("認証成功: userId", userId);
    const userMail = await Auth.getAdminUserMail(userId);

    if (userMail === undefined) {
      res.clearCookie("oripa-admin-token", {
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "Strict",
      });
      logUserPut(`ユーザー[${userId}]はアクセス禁止されていますので、トークンを削除しました。 - authenticateJWT`);
      logErr(`ユーザー[${userId}]はアクセス禁止されていますので、トークンを削除しました。 - authenticateJWT`);
      logAccountBanPut(`管理者ユーザー[${userId}]はアクセス禁止されていますので、トークンを削除しました。 - authenticateJWT`);
      return res.sendStatus(403);
    }

    console.log("認証成功: userMail", userMail);
    next();
  } catch (err) {
    // トークンが無効な場合の処理
    res.clearCookie("oripa-admin-token", {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "Strict",
    });
    res.sendStatus(403); // アクセス拒否
  }
};

module.exports = authenticateJWT;
