{"name": "original-pack", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"start": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "author": "", "license": "ISC", "dependencies": {"@amazonpay/amazon-pay-api-sdk-nodejs": "^2.3.1", "@fincode/node": "^1.0.2", "@vonage/messages": "^1.14.0", "@vonage/server-sdk": "^3.14.0", "aws-sdk": "^2.1692.0", "axios": "^1.7.3", "base-64": "^1.0.0", "bcrypt": "^5.1.1", "console": "^0.7.2", "cookie-parser": "^1.4.6", "cors": "^2.8.5", "csv-writer": "^1.6.0", "date-fns-tz": "^2.0.1", "dotenv": "^16.4.5", "express": "^4.19.2", "express-rate-limit": "^7.4.1", "express-session": "^1.18.0", "firebase-admin": "^12.0.0", "ioredis": "^5.4.2", "jsonwebtoken": "^9.0.2", "knex": "^3.1.0", "log4js": "^6.9.1", "multer": "^1.4.5-lts.1", "mysql": "^2.18.1", "nocache": "^4.0.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^6.9.13", "nodemon": "^3.1.0", "redis": "^4.7.0", "redlock": "^5.0.0-beta.2", "request": "^2.88.2", "stripe": "^15.12.0", "twilio": "^5.2.2", "uuid": "^11.1.0"}, "devDependencies": {"@eslint/js": "^9.18.0", "eslint": "^8.57.1", "globals": "^15.14.0"}}