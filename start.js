const { spawn } = require("child_process");
const fs = require("fs");
const path = require("path");

// logsディレクトリが存在しない場合は作成
const logDir = path.join(__dirname, "logs");
if (!fs.existsSync(logDir)) {
  fs.mkdirSync(logDir);
}

const nodemon = spawn("npx", ["nodemon", "index.js"], {
  stdio: ["inherit", "pipe", "pipe"], // 標準出力とエラー出力をパイプで取得
});

// `stdout`と`stderr`の両方でメッセージをキャプチャしてログファイルに記録
nodemon.stdout.on("data", (data) => {
  const message = data.toString();
  console.log(message);

  // もしクラッシュメッセージが含まれていたら、`crash.log` に記録
  if (message.includes("app crashed - waiting for file changes before starting...")) {
    const date = new Date().toLocaleString("ja-JP", { timeZone: "Asia/Tokyo" });
    const logMessage = `[${date}] ${message}\n`;

    fs.appendFile(path.join(logDir, "crash.log"), logMessage, (err) => {
      if (err) console.error("ログファイルにエラーを記録できませんでした:", err);
    });
  }
});

nodemon.stderr.on("data", (data) => {
  const errorMessage = data.toString();
  console.error(errorMessage); // 標準エラー出力をコンソールに表示

  // エラーメッセージも含めてクラッシュログとして記録
  const date = new Date().toLocaleString("ja-JP", { timeZone: "Asia/Tokyo" });
  const logMessage = `[${date}] ${errorMessage}\n`;

  fs.appendFile(path.join(logDir, "crash.log"), logMessage, (err) => {
    if (err) console.error("ログファイルにエラーを記録できませんでした:", err);
  });
});

// プロセス終了時のメッセージ
nodemon.on("close", (code) => {
  console.log(`nodemonプロセスが終了しました (code: ${code})`);
});
