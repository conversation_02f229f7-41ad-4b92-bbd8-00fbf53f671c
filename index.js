const express = require("express");
const session = require("express-session");
const cors = require("cors");
require("dotenv").config();
const app = express();
const {logServerPut} = require("./logger");
const cookieParser = require("cookie-parser");
require("./util/resetPoint");

/* ----------------------------------------------------------------------
プロキシを信頼する
-----------------------------------------------------------------------*/
app.set("trust proxy", true);

/* ---------------------------------------------------------------------
ログ出力
-----------------------------------------------------------------------*/
logServerPut("服务启动成功");

/* ---------------------------------------------------------------------
サーバーの基本設定
-----------------------------------------------------------------------*/
// CORSの設定
app.use(
    cors({
        origin: process.env.CORS_URL.split(',').map(url => url.trim()), // 分割并去除空格
        // origin: "https://frontend-dev-gacha-x.vercel.app", // 分割并去除空格
        credentials: true,
    })
);

// app.use(
//     cors({
//         origin: 'http://localhost:3000', // 允许所有来源访问
//         // methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'], // 允许的 HTTP 方法
//         // allowedHeaders: ['Content-Type', 'Authorization'], // 允许的请求头
//         credentials: true, // 如果需要携带 Cookie 或认证信息
//     })
// );


// WebhookAPIのインポート・ルーティング設定
const webhookRoute = require("./api/webhook");
app.use("/api/webhook", webhookRoute);
// アップロードディレクトリの設定
app.use(express.json({limit: "10mb"}));
app.use("/uploads", express.static("uploads"));
// セッションの設定
app.use(
    session({
        secret: process.env.SESSION_SECRET,
        name: "oripa.sid",
        resave: false, // セッションを常に保存するかどうか（推奨：false）
        saveUninitialized: true, // 初期化されていないセッションも保存するかどうか（推奨：true）
        cookie: {
            httpOnly: true, // クッキーの設定
            secure: false, // HTTPSを使用していない場合はfalseに設定
            maxAge: 1000 * 60 * 60 * 24, // クッキーの有効期限（例：1日）
        },
    })
);


// 1. 捕获所有同步错误
app.use((err, req, res, next) => {
    console.error('全局错误:', err);
    res.status(500).json({error: '服务异常'});
});

// 2. 捕获未处理的 Promise 拒绝
process.on('unhandledRejection', (err) => {
    console.error('未处理的 Promise 拒绝:', err);
});


// 3. 捕获未捕获的异常
process.on('uncaughtException', (err) => {
    console.error('未捕获的异常:', err);
    // 可选：优雅退出进程
    // process.exit(1);
});


app.use(cookieParser());
const authRoutes = require("./route/authRoutes");
app.use("/api", authRoutes);

const publicRoutes = require("./route/publicRoutes");
app.use("/api", publicRoutes);

const adminRoutes = require("./route/adminRoutes");
app.use("/api", adminRoutes);

const paymentRoutes = require("./route/paymentRoutes");
app.use("/api", paymentRoutes);

const userRankRoutes = require("./route/userRankRoutes");
app.use("/api", userRankRoutes);

const userLoginBonusRoutes = require("./route/userLoginBonusRoutes");
app.use("/api", userLoginBonusRoutes);

const userBuyItemRoutes = require("./route/userBuyItemRoutes");
app.use("/api", userBuyItemRoutes);

const userItemRoutes = require("./route/userItemRoutes");
app.use("/api", userItemRoutes);

const userDrawRoutes = require("./route/userDrawRoutes");
app.use("/api", userDrawRoutes);

const referralRoutes = require("./route/referralRoutes");
app.use("/api", referralRoutes);

const userCouponRoutes = require("./route/userCouponRoutes");
app.use("/api", userCouponRoutes);

const userTransactionRoutes = require('./route/userTransactionRoutes');
app.use("/api", userTransactionRoutes);

const levelRoutes = require("./route/levelRoutes");
app.use("/api", levelRoutes);

const dynamicConfigRoutes = require("./route/dynamicConfigRoutes");
app.use("/api/dynamic-config", dynamicConfigRoutes);

const userExtensionRoutes = require('./route/userExtensionRoutes');
app.use('/api', userExtensionRoutes);

/* ---------------------------------------------------------------------
ログイン機能
-----------------------------------------------------------------------*/
// ユーザーのログアウトAPIのインポート・ルーティング設定
const logoutRoute = require("./api/logout");
app.use("/api/logout", logoutRoute);
// ユーザーのパスワードリセットの案内APIのインポート・ルーティング設定
const passReset = require("./api/pass-reset");
app.use("/api/pass-reset", passReset);
// ユーザーのパスワードリセット機能APIのインポート・ルーティング設定
const resetPassword = require("./api/reset-password");
app.use("/api/reset-password", resetPassword);
// 管理者のログインAPIのインポート・ルーティング設定
const adminLoginRoute = require("./api/adminLogin");
app.use("/api/adminLogin", adminLoginRoute);
// 管理者のログイン認証APIのインポート・ルーティング設定
const adminCheckAuthRoute = require("./api/checkAuth");
app.use("/api/checkAuth", adminCheckAuthRoute);

/* ---------------------------------------------------------------------
管理者情報追加
-----------------------------------------------------------------------*/
// 管理者アカウントを追加するAPIのインポート・ルーティング設定
const adminCreateAccountRoute = require("./api/adminCreateAccount");
app.use("/api/adminCreateAccount", adminCreateAccountRoute);
// 管理者アカウントを取得するAPIのインポート・ルーティング設定
const adminGetAccountRoute = require("./api/admin-account-list");
app.use("/api/admin-account-list", adminGetAccountRoute);
// 管理者アカウント削除APIのインポート・ルーティング設定
const adminRemoveAccountRoute = require("./api/admin-account-remove");
app.use("/api/admin-account-remove", adminRemoveAccountRoute);

/* ---------------------------------------------------------------------
ログイン中のユーザー情報を取得
-----------------------------------------------------------------------*/
// ユーザー情報を取得するAPIのインポート・ルーティング設定
const userInfoRoute = require("./api/user");
app.use("/api/user", userInfoRoute);
// 管理者サイドで全てのユーザー情報を取得するAPIのインポート・ルーティング設定
const usersInfoRoute = require("./api/users");
app.use("/api/users", usersInfoRoute);

/* ---------------------------------------------------------------------
各種APIのインポート・ルーティング
-----------------------------------------------------------------------*/
//お知らせ情報作成APIのインポート・ルーティング設定
const createInfoRoute = require("./api/create-info");
app.use("/api/create-info", createInfoRoute);
//パック作成APIのインポート・ルーティング設定
const createPackRoute = require("./api/create-pack");
app.use("/api/create-pack", createPackRoute);
//カテゴリ作成APIのインポート・ルーティング設定
const createCatRoute = require("./api/create-cat");
app.use("/api/create-cat", createCatRoute);

const createPulledCard = require("./api/create-pulled-card");
app.use("/api/create-pulled-card", createPulledCard);

const recenPulledCards = require("./api/recenPulledCards");
app.use("/api/recent-pulled-cards", recenPulledCards);
const adminRecentPulledCardRemove = require("./api/admin-recent-pulled-card-remove");
app.use("/api/admin-recent-pulled-card-remove", adminRecentPulledCardRemove);

//カテゴリ取得APIのインポート・ルーティング設定
const createCatGetRoute = require("./api/categories");
app.use("/api/categories", createCatGetRoute);
//カテゴ削除APIのインポート・ルーティング設定
const removeCatRoute = require("./api/admin-cat-remove");
app.use("/api/admin-cat-remove", removeCatRoute);
//タイプ作成APIのインポート・ルーティング設定
const createTypeRoute = require("./api/create-type");
app.use("/api/create-type", createTypeRoute);
//タイプ取得APIのインポート・ルーティング設定
const getTypeRoute = require("./api/get-types");
app.use("/api/get-types", getTypeRoute);
//タイプ削除APIのインポート・ルーティング設定
const removeTypeRoute = require("./api/admin-type-remove");
app.use("/api/admin-type-remove", removeTypeRoute);
//c賞アイテム作成のAPIのインポート・ルーティング設定
const createCItems = require("./api/create-c-prize");
app.use("/api/create-c-prize", createCItems);
//c賞自動生成APIのインポート・ルーティング設定
const getCItems = require("./api/generate-c-prize");
app.use("/api/generate-c-prize", getCItems);
//c賞のアイテムを全て取得するAPIのインポート・ルーティング設定
const getCPrize = require("./api/get-c-prize");
app.use("/api/get-c-prize", getCPrize);
//選択されたc賞のアイテムを削除するAPIのインポート・ルーティング設定
const removeCPrize = require("./api/c-prize-remove");
app.use("/api/c-prize-remove", removeCPrize);
//詳細画面で必要なパックに格納されているアイテムを取得するAPIのインポート・ルーティング設定
const getDetailItems = require("./api/detail-items");
app.use("/api/detail-items", getDetailItems);
//パックのキリ番を取得するAPIのインポート・ルーティング設定
const getRoundNumItem = require("./api/get-round-num-item");
app.use("/api/get-round-num-item", getRoundNumItem);
//管理者用のすべてのパック情報を取得するAPIのインポート・ルーティング設定
const getAdminAllPackInfo = require("./api/admin-get-packs");
app.use("/api/admin-get-packs", getAdminAllPackInfo);
//パックの表示ステータス変更APIのインポート・ルーティング設定
const packStatusChangeRoute = require("./api/pack-status-change");
app.use("/api/pack-status-change", packStatusChangeRoute);
//お知らせ情報取得APIのインポート・ルーティング設定
const getInfoRoute = require("./api/get-info");
app.use("/api/get-info", getInfoRoute);
//お知らせ情報削除APIのインポート・ルーティング設定
const removeInfoRoute = require("./api/admin-info-remove");
app.use("/api/admin-info-remove", removeInfoRoute);
//GPアイテムの登録APIのインポート・ルーティング設定
const createGpItemRoute = require("./api/create-gp-item");
app.use("/api/create-gp-item", createGpItemRoute);
//GPアイテムの取得APIのインポート・ルーティング設定
const getGpItemRoute = require("./api/get-gp-item");
app.use("/api/get-gp-item", getGpItemRoute);
//GPアイテムの交換APIのインポート・ルーティング設定
const exchangeGpItemRoute = require("./api/exchange-gp-item");
app.use("/api/exchange-gp-item", exchangeGpItemRoute);
//管理者用のGPアイテムの一覧取得APIのインポート・ルーティング設定
const AdminGetGpItemRoute = require("./api/admin-get-gp-item");
app.use("/api/admin-get-gp-item", AdminGetGpItemRoute);
//GPアイテムの削除APIのインポート・ルーティング設定
const removeGpItemRoute = require("./api/admin-remove-gp-item");
app.use("/api/admin-remove-gp-item", removeGpItemRoute);
// User GP items API のインポート・ルーティング設定
const getUserGpItemsRoute = require("./api/get-user-gp-items");
app.use("/api/get-user-gp-items", getUserGpItemsRoute);
// クーポン作成API のインポート・ルーティング設定
const couponRoutes = require("./api/createCoupons");
app.use("/api/createCoupons", couponRoutes);
// クーポン取得、削除API のインポート・ルーティング設定
const adminCouponsRoutes = require("./api/admin-coupons");
app.use("/api/admin-coupons", adminCouponsRoutes);
// ユーザーのクーポン取得、表示API のインポート・ルーティング設定
const userCouponsRoutes = require("./api/user-coupons");
app.use("/api/user-coupons", userCouponsRoutes);

/* ---------------------------------------------------------------------
stripe
-----------------------------------------------------------------------*/

const createCheckoutSessionRoute = require("./api/create-checkout-session");
app.use("/api/create-checkout-session", createCheckoutSessionRoute);

/* ---------------------------------------------------------------------
ガチャを引くエンドポイント
-----------------------------------------------------------------------*/
// テストガチャを引くAPIのインポート・ルーティング設定
const testDrawRoute = require("./api/test-draw");
app.use("/api/test-draw", testDrawRoute);
// ガチャの排出履歴を確認することができるAPIのインポート・ルーティング設定
const drawHistoryRouter = require("./api/draw-history");
app.use("/api/draw-history", drawHistoryRouter);

/* ---------------------------------------------------------------------
動画アップロード
-----------------------------------------------------------------------*/
// 動画をアップロードしてデータベースにパスを保存するAPIのインポート・ルーティング設定
const uploadVideoRoute = require("./api/upload-video");
app.use("/api/upload-video", uploadVideoRoute);
// 動画をランダムに取得するAPIのインポート・ルーティング設定
const randomVideoRoute = require("./api/random-video");
app.use("/api/random-video", randomVideoRoute);
// 動画一覧を取得する為のAPIのインポート・ルーティング設定
const getVideoRoute = require("./api/get-video");
app.use("/api/get-video", getVideoRoute);
// 特別な動画一覧を取得する為のAPIのインポート・ルーティング設定
const getSpecialVideoRoute = require("./api/get-special-video");
app.use("/api/get-special-video", getSpecialVideoRoute);
// 動画を削除する為のAPIのインポート・ルーティング設定
const removeVideoRoute = require("./api/admin-video-remove");
app.use("/api/admin-video-remove", removeVideoRoute);

/* ---------------------------------------------------------------------
ユーザーアイテムを取得
-----------------------------------------------------------------------*/
// 配送ステータスを更新するAPIのインポート・ルーティング設定
const updateShippingStatusRoute = require("./api/update-shipping-status");
app.use("/api/update-shipping-status", updateShippingStatusRoute);

const uploadAvatarRoute = require("./api/upload-avatar");
app.use("/api/updateAvatar", uploadAvatarRoute);

// ユーザーのアイテム一覧からポイント交換するAPIのインポート・ルーティング設定
const userPointChangeRoute = require("./api/item-point-change");
app.use("/api/item-point-change", userPointChangeRoute);
// ユーザーのガチャ結果一覧からポイント交換するAPIのインポート・ルーティング設定
const userResultPointChangeRoute = require("./api/item-result-point-change");
app.use("/api/item-result-point-change", userResultPointChangeRoute);
// 住所を更新するAPIのインポート・ルーティング設定
const updateAddressRoute = require("./api/update-address");
app.use("/api/update-address", updateAddressRoute);
// 配送ステータス情報を取得するAPIのインポート・ルーティング設定
const shippingStatusesRoute = require("./api/shipping-statuses");
app.use("/api/shipping-statuses", shippingStatusesRoute);
// 配送ステータス情報が配送済のものを取得するAPIのインポート・ルーティング設定
const getShippedItemsRoute = require("./api/get-shipped-items");
app.use("/api/get-shipped-items", getShippedItemsRoute);
// 配送ステータスを更新するAPIのインポート・ルーティング設定
const updateShippingStatusesRoute = require("./api/update-shipping-statuses");
app.use("/api/update-shipping-statuses", updateShippingStatusesRoute);
// 配送アイテムを集荷用CSVで出力するためのAPIのインポート・ルーティング設定
const shipListExportCsvRoute = require("./api/ship-list-export-csv");
app.use("/api/ship-list-export-csv", shipListExportCsvRoute);
// 配送アイテムを検索するためのAPIのインポート・ルーティング設定
const searchShipListRoute = require("./api/search-shipping-statuses");
app.use("/api/search-shipping-statuses", searchShipListRoute);
// userテーブルの内容をCSV出力するAPIのインポート・ルーティング設定
const exportUsersCsvRoute = require("./api/export-users-csv");
app.use("/api/export-users-csv", exportUsersCsvRoute);
// 配送済みアイテムを検索するためのAPIのインポート・ルーティング設定
const searchShippedListRoute = require("./api/search-shipped-statuses");
app.use("/api/search-shipped-statuses", searchShippedListRoute);

/* ---------------------------------------------------------------------
パックの詳細設定
-----------------------------------------------------------------------*/
// packInfoテーブルの内容を追加、編集するAPIのインポート・ルーティング設定
const packSettingRoute = require("./api/pack-settings");
app.use("/api/pack-settings", packSettingRoute);
// packInfoテーブルの内容取得するAPIのインポート・ルーティング設定
const getPackSettingRoute = require("./api/get-pack-settings");
app.use("/api/get-pack-settings", getPackSettingRoute);


const health = require("./api/test-redis");
app.use("/health", health);

const PORT = 8080;
app.listen(PORT, () => console.log(`Server running on http port ${PORT}`));
// if (process.env.NODE_ENV === "development") {
//   const fs = require("fs");
//   const option = {
//     key: fs.readFileSync("./cert/localhost-key.pem"),
//     cert: fs.readFileSync("./cert/localhost.pem"),
//   };
//   const server = require("https").createServer(option, app);
//   server.listen(PORT, () => console.log(`Server running https on port ${PORT}`));
// } else {
//   app.listen(PORT, () => console.log(`Server running on http port ${PORT}`));
// }
