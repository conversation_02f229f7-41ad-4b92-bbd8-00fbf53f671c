name: Build and Deploy to ECS

on:
  push:
    branches:
      - main       # 生产环境
      - develop    # 开发环境
      - preview    # 预发布环境
  workflow_dispatch:  # 支持手动触发

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      # 1. 检出代码
      - name: Checkout code
        uses: actions/checkout@v4

      # 2. 配置 AWS 凭据
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}

      # 3. 登录到 Amazon ECR
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      # 4. 构建并推送 Docker 镜像
      - name: Build and push Docker image
        env:
          ECR_REGISTRY: ${{ secrets.ECR_REPOSITORY }}
          ECR_TAG: ${{ github.ref_name }}  # 使用分支名作为标签
        run: |
          docker builder prune --all --force
          docker build \
            --platform linux/amd64 \
            --no-cache \
            -t $ECR_REGISTRY:$ECR_TAG .
          docker push $ECR_REGISTRY:$ECR_TAG

      # 5. 获取最新任务定义 ARN（根据环境选择不同的任务定义）

      - name: Get latest task definition ARN for Production
        if: github.ref == 'refs/heads/main'
        id: get-latest-task-definition-prod
        run: |
          LATEST_TASK_DEFINITION=$(aws ecs describe-task-definition \
            --task-definition backend-task-defination-prod \
            --query 'taskDefinition.taskDefinitionArn' \
            --output text)
          echo "Latest Task Definition ARN: $LATEST_TASK_DEFINITION"
          echo "LATEST_TASK_DEFINITION=$LATEST_TASK_DEFINITION" >> $GITHUB_ENV

      - name: Get latest task definition ARN for Development
        if: github.ref == 'refs/heads/develop'
        id: get-latest-task-definition-dev
        run: |
          LATEST_TASK_DEFINITION=$(aws ecs describe-task-definition \
            --task-definition backend-task-defination-dev \
            --query 'taskDefinition.taskDefinitionArn' \
            --output text)
          echo "Latest Task Definition ARN: $LATEST_TASK_DEFINITION"
          echo "LATEST_TASK_DEFINITION=$LATEST_TASK_DEFINITION" >> $GITHUB_ENV

      - name: Get latest task definition ARN for Preview
        if: github.ref == 'refs/heads/preview'
        id: get-latest-task-definition-preview
        run: |
          LATEST_TASK_DEFINITION=$(aws ecs describe-task-definition \
            --task-definition backend-task-defination-preview \
            --query 'taskDefinition.taskDefinitionArn' \
            --output text)
          echo "Latest Task Definition ARN: $LATEST_TASK_DEFINITION"
          echo "LATEST_TASK_DEFINITION=$LATEST_TASK_DEFINITION" >> $GITHUB_ENV

      # 6. 强制重新部署 ECS 服务（根据环境选择不同的集群和服务）

      - name: Deploy to ECS (Production)
        if: github.ref == 'refs/heads/main'
        run: |
          echo "🔄 正在部署到生产环境..."
          echo "Cluster: backend-cluster-prod"
          echo "Service: backend_service_prod"
          echo "Task Definition ARN: ${{ env.LATEST_TASK_DEFINITION }}"
          
          aws ecs update-service \
            --cluster backend-cluster-prod \
            --service backend_service_prod \
            --task-definition ${{ env.LATEST_TASK_DEFINITION }} \
            --force-new-deployment \
            --region ${{ secrets.AWS_REGION }}

      - name: Deploy to ECS (Development)
        if: github.ref == 'refs/heads/develop'
        run: |
          echo "🔄 正在部署到开发环境..."
          echo "Cluster: backend-cluster-dev"
          echo "Service: backend_service"
          echo "Task Definition ARN: ${{ env.LATEST_TASK_DEFINITION }}"
          
          aws ecs update-service \
            --cluster backend-cluster-dev \
            --service backend_service \
            --task-definition ${{ env.LATEST_TASK_DEFINITION }} \
            --force-new-deployment \
            --region ${{ secrets.AWS_REGION }}

      - name: Deploy to ECS (Preview)
        if: github.ref == 'refs/heads/preview'
        run: |
          echo "🔄 正在部署到预发布环境..."
          echo "Cluster: backend-cluster-preview"
          echo "Service: backend-preview-service"
          echo "Task Definition ARN: ${{ env.LATEST_TASK_DEFINITION }}"
          
          aws ecs update-service \
            --cluster backend-cluster-preview \
            --service backend-preview-service \
            --task-definition ${{ env.LATEST_TASK_DEFINITION }} \
            --force-new-deployment \
            --region ${{ secrets.AWS_REGION }}
