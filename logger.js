const log4js = require("log4js");
const path = require("path");

// 构建绝对路径
const configFilePath = path.resolve(__dirname, "log4js-config.json");

try {
  // 加载配置
  log4js.configure(configFilePath);
  console.log("Log4js configuration loaded successfully.");
} catch (error) {
  console.error("Failed to load log4js configuration:", error.message);
  // 如果配置加载失败，提供一个默认的日志记录器
  const defaultLogger = log4js.getLogger();
  defaultLogger.level = "debug";
  module.exports = {
    logPut: (category, logText) => defaultLogger.debug(logText),
    logPaymentPut: (logText) => defaultLogger.debug(logText),
    logAccountBanPut: (logText) => defaultLogger.debug(logText),
    logErr: (logText) => defaultLogger.error(logText),
    logUserPut: (logText) => defaultLogger.debug(logText),
    logAdminPut: (logText) => defaultLogger.debug(logText),
    logServerPut: (logText) => defaultLogger.debug(logText),
  };
}

// 正常情况下导出日志函数
const logPut = (category, logText) => {
  const logger = log4js.getLogger(category);
  logger.debug(logText);
};

const logServerPut = (logText) => {
  const logger = log4js.getLogger("server");
  logger.debug(logText);
};

const logUserPut = (logText) => {
  const logger = log4js.getLogger("user");
  logger.debug(logText);
};

const logPaymentPut = (logText) => {
  const logger = log4js.getLogger("payment");
  logger.debug(logText);
};

const logAccountBanPut = (logText) => {
  const logger = log4js.getLogger("account_ban");
  logger.debug(logText);
};

const logAdminPut = (logText) => {
  const logger = log4js.getLogger("admin");
  logger.debug(logText);
};

const logErr = (logText) => {
  const logger = log4js.getLogger("error");
  logger.error(logText);
};

module.exports = {
  logPut,
  logPaymentPut,
  logAccountBanPut,
  logErr,
  logUserPut,
  logAdminPut,
  logServerPut,
};
