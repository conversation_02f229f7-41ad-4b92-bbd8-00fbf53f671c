// cluster-example.js
const cluster = require("cluster");
const os = require("os");

if (cluster.isPrimary) {
  // Node.js v16以前: cluster.isMaster
  // (1) マスタープロセスの処理
  const numCPUs = os.cpus().length;

  // CPUコア数分ワーカーを起動
  for (let i = 0; i < numCPUs; i++) {
    cluster.fork();
  }

  // ワーカーが死んだ場合に新たに生成
  cluster.on("exit", (worker, code, signal) => {
    console.log(`Worker ${worker.process.pid} died. Spawning a new worker...`);
    cluster.fork();
  });
} else {
  // 2025/1/17現在でいうindex.js側の処理を記載
}
