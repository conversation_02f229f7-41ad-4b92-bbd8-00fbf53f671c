/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "all_draw");
  if (!exists) {
    return knex.schema.table("pack_info", function (table) {
      table.boolean("all_draw").defaultTo(false); // 'all_draw'カラムを真偽値型で追加、デフォルト値はfalse
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("pack_info", function (table) {
    table.dropColumn("all_draw"); // ロールバック時に 'all_draw'カラムを削除
  });
};
