/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "mystery_pack");
  if (!exists) {
    return knex.schema.table("pack_info", function (table) {
      table.string("mystery_pack").defaultTo(null);
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "mystery_pack");
  if (exists) {
    return knex.schema.table("pack_info", function (table) {
      table.dropColumn("mystery_pack");
    });
  }
};
