/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
    const exists = await knex.schema.hasColumn("pack_draw_history", "round_num");
    if (!exists) {
      return knex.schema.table("pack_draw_history", function (table) {
        table.integer("round_num").defaultTo(null);
      });
    }
  };
  
  /**
   * @param { import("knex").Knex } knex
   * @returns { Promise<void> }
   */
  exports.down = async function (knex) {
    const exists = await knex.schema.hasColumn("pack_draw_history", "round_num");
    if (exists) {
      return knex.schema.table("pack_draw_history", function (table) {
        table.dropColumn("round_num");
      });
    }
  };
  