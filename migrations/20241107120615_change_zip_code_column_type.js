/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const columnInfo = await knex("shipping_statuses").columnInfo("zip_code");

  // カラムがすでにstring型である場合はalterをスキップ
  if (columnInfo && columnInfo.type !== "string") {
    return knex.schema.alterTable("shipping_statuses", function (table) {
      table.string("zip_code", 20).alter(); // 'zip_code'カラムをstring型に変更
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const columnInfo = await knex("shipping_statuses").columnInfo("zip_code");

  // カラムがすでにinteger型でない場合のみalterを実行
  if (columnInfo && columnInfo.type !== "integer") {
    return knex.schema.alterTable("shipping_statuses", function (table) {
      table.integer("zip_code").alter(); // ロールバックで'zip_code'カラムをinteger型に戻す
    });
  }
};
