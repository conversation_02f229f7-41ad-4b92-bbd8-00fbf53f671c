/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "gtm");
  if (!exists) {
    return knex.schema.alterTable("pack_info", (table) => {
      table.string("gtm").nullable().comment("Google Tag Manager ID");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "gtm");
  if (exists) {
    return knex.schema.alterTable("pack_info", (table) => {
      table.dropColumn("gtm");
    });
  }
};
