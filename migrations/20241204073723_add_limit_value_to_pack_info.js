/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "limit_value");
  if (!exists) {
    return knex.schema.alterTable("pack_info", function (table) {
      table.integer("limit_value").nullable().defaultTo(null).comment("制限値");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "limit_value");
  if (exists) {
    return knex.schema.alterTable("pack_info", function (table) {
      table.dropColumn("limit_value");
    });
  }
};
