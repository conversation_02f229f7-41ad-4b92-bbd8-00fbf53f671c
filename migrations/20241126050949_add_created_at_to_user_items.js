/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // テーブルにカラムが存在しない場合のみ追加
  const hasColumn = await knex.schema.hasColumn("user_items", "created_at");
  if (!hasColumn) {
    await knex.schema.alterTable("user_items", (table) => {
      table.timestamp("created_at").defaultTo(knex.fn.now()).notNullable().comment("作成日時");
    });
    console.log("カラム created_at を user_items テーブルに追加しました。");
  } else {
    console.log("カラム created_at は既に存在しています。");
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  // カラムが存在する場合のみ削除
  const hasColumn = await knex.schema.hasColumn("user_items", "created_at");
  if (hasColumn) {
    await knex.schema.alterTable("user_items", (table) => {
      table.dropColumn("created_at");
    });
    console.log("カラム created_at を user_items テーブルから削除しました。");
  } else {
    console.log("カラム created_at は存在しません。");
  }
};
