/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const tableChecks = [
    {
      name: "buy_items",
      create: async () => {
        return knex.schema.createTable("buy_items", function (table) {
          table.increments("id").primary();
          table.bigint("point").notNullable();
          table.string("image", 255).notNullable();
        });
      },
    },
    {
      name: "buy_discount_coupon",
      create: async () => {
        return knex.schema.createTable("buy_discount_coupon", function (table) {
          table.increments("id").primary();
          table.string("coupon_code", 255).notNullable();
          table.integer("item_id").notNullable();
          table.integer("discount").notNullable();
          table.integer("rest").notNullable();
        });
      },
    },
    {
      name: "user_discount_coupon",
      create: async () => {
        return knex.schema.createTable("user_discount_coupon", function (table) {
          table.increments("id").primary();
          table.string("coupon_code", 255).notNullable();
          table.integer("user_id").notNullable();
          table.datetime("get_day").notNullable();
          table.datetime("delete_day").notNullable();
          table.string("status", 255).notNullable();
        });
      },
    },
  ];

  for (const table of tableChecks) {
    const exists = await knex.schema.hasTable(table.name);
    if (!exists) {
      await table.create();
    }
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTableIfExists("user_discount_coupon").dropTableIfExists("buy_discount_coupon").dropTableIfExists("buy_items");
};
