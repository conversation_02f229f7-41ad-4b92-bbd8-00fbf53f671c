/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.table("info", function (table) {
    if (!knex.schema.hasColumn("info", "sort_number")) {
      table.integer("sort_number").defaultTo(0);
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("info", function (table) {
    table.dropColumn("sort_number");
  });
};
