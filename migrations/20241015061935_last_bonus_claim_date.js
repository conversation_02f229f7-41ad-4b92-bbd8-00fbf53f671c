/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("user", "last_bonus_claim_date");
  if (!exists) {
    return knex.schema.table("user", function (table) {
      table.date("last_bonus_claim_date"); // 'last_bonus_claim_date'カラムを追加
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("user", function (table) {
    table.dropColumn("last_bonus_claim_date"); // ロールバック時に 'last_bonus_claim_date'カラムを削除
  });
};
