/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "display_days");
  if (!exists) {
    return knex.schema.table("pack_info", function (table) {
      table.integer("display_days").nullable(); // 'display_days'カラムを数値型で追加
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("pack_info", function (table) {
    table.dropColumn("display_days"); // ロールバック時に 'display_days'カラムを削除
  });
};
