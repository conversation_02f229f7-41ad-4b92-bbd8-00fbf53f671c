/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
    const exists = await knex.schema.hasColumn("pack_settings", "gp_name");
    if (!exists) {
      return knex.schema.table("pack_settings", function (table) {
        table.string("gp_name").defaultTo(null);
      });
    }
  };
  
  /**
   * @param { import("knex").Knex } knex
   * @returns { Promise<void> }
   */
  
  exports.down = async function (knex) {
    const exists = await knex.schema.hasColumn("pack_settings", "gp_name");
    if (exists) {
      return knex.schema.table("pack_settings", function (table) {
        table.dropColumn("gp_name");
      });
    }
  };
  