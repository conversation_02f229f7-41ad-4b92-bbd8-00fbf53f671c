/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const hasDrawHistoryColumn = await knex.schema.hasColumn("pack_settings", "draw_history");

  if (!hasDrawHistoryColumn) {
    return knex.schema.table("pack_settings", function (table) {
      table.boolean("draw_history").defaultTo(false).notNullable().comment("Tracks draw history status");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("pack_settings", function (table) {
    table.dropColumn("draw_history");
  });
};
