/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const hasBuyCodeColumn = await knex.schema.hasColumn("userCoupons", "buy_code");

  if (!hasBuyCodeColumn) {
    return knex.schema.table("userCoupons", function (table) {
      table.string("buy_code").comment("購入コード");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("userCoupons", function (table) {
    table.dropColumn("buy_code");
  });
};
