/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const columns = [
    { name: "service_name", type: "string" },
    { name: "privacy_policy", type: "text" },
    { name: "notation_commercial", type: "text" },
    { name: "terms_conditions", type: "text" },
  ];

  for (const column of columns) {
    const exists = await knex.schema.hasColumn("pack_settings", column.name);
    if (!exists) {
      await knex.schema.alterTable("pack_settings", function (table) {
        if (column.type === "string") {
          table.string(column.name).notNullable().defaultTo(""); // 文字列カラム
        } else if (column.type === "text") {
          table.text(column.name).nullable(); // 改行を許可した大きい文字列
        }
      });
    }
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.alterTable("pack_settings", function (table) {
    table.dropColumn("service_name");
    table.dropColumn("privacy_policy");
    table.dropColumn("notation_commercial");
    table.dropColumn("terms_conditions");
  });
};
