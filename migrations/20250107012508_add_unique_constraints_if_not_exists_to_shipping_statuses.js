/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // user_item_idカラムにユニーク制約が存在しない場合、追加
  const hasUserItemIdUnique = await knex
    .raw(
      `
    SELECT COUNT(*) AS count
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'shipping_statuses'
    AND INDEX_NAME = 'unique_user_item_id'
  `
    )
    .then((result) => result[0][0].count > 0);

  const hasUserGpItemIdUnique = await knex
    .raw(
      `
    SELECT COUNT(*) AS count
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'shipping_statuses'
    AND INDEX_NAME = 'unique_user_gp_item_id'
  `
    )
    .then((result) => result[0][0].count > 0);

  await knex.schema.table("shipping_statuses", function (table) {
    if (!hasUserItemIdUnique) {
      table.unique("user_item_id", "unique_user_item_id");
    }
    if (!hasUserGpItemIdUnique) {
      table.unique("user_gp_item_id", "unique_user_gp_item_id");
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("shipping_statuses", function (table) {
    table.dropUnique("user_item_id", "unique_user_item_id");
    table.dropUnique("user_gp_item_id", "unique_user_gp_item_id");
  });
};
