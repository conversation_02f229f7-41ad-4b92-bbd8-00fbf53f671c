/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // expiration_at カラムが存在しない場合のみ追加
  const hasColumn = await knex.schema.hasColumn("user_items", "expiration_at");
  if (!hasColumn) {
    await knex.schema.alterTable("user_items", (table) => {
      table.timestamp("expiration_at").nullable().comment("アイテムの有効期限");
    });
    console.log("カラム expiration_at を user_items テーブルに追加しました。");
  } else {
    console.log("カラム expiration_at は既に存在しています。");
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  // expiration_at カラムが存在する場合のみ削除
  const hasColumn = await knex.schema.hasColumn("user_items", "expiration_at");
  if (hasColumn) {
    await knex.schema.alterTable("user_items", (table) => {
      table.dropColumn("expiration_at");
    });
    console.log("カラム expiration_at を user_items テーブルから削除しました。");
  } else {
    console.log("カラム expiration_at は存在しません。");
  }
};
