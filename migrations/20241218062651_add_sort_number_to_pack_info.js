/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "sort_number");
  if (!exists) {
    return knex.schema.alterTable("pack_info", function (table) {
      table.integer("sort_number").defaultTo(0).notNullable().comment("Sort order number");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "sort_number");
  if (exists) {
    return knex.schema.alterTable("pack_info", function (table) {
      table.dropColumn("sort_number");
    });
  }
};
