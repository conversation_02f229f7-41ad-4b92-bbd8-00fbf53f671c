exports.up = async function (knex) {
  // user_itemsテーブルにidx_user_id_item_idインデックスが存在するか確認
  const indexExists = await knex.raw(`
    SELECT COUNT(1) AS index_count
    FROM INFORMATION_SCHEMA.STATISTICS
    WHERE TABLE_SCHEMA = DATABASE()
      AND TABLE_NAME = 'user_items'
      AND INDEX_NAME = 'idx_user_id_item_id'
  `);

  if (indexExists[0][0].index_count === 0) {
    // インデックスが存在しない場合のみ追加
    await knex.schema.alterTable("user_items", (table) => {
      table.index(["user_id", "id"], "idx_user_id_item_id");
    });
    console.log("インデックス 'idx_user_id_item_id' を追加しました。");
  } else {
    console.log("インデックス 'idx_user_id_item_id' はすでに存在します。");
  }
};

exports.down = async function (knex) {
  // インデックスを削除
  await knex.schema.alterTable("user_items", (table) => {
    table.dropIndex(["user_id", "id"], "idx_user_id_item_id");
  });
  console.log("インデックス 'idx_user_id_item_id' を削除しました。");
};
