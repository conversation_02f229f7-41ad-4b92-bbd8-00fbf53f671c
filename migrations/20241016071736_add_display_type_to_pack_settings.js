/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_settings", "display_type");

  if (!exists) {
    return knex.schema.table("pack_settings", function (table) {
      table.text("display_type").nullable(); // 'display_type'カラムを追加
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("pack_settings", function (table) {
    table.dropColumn("display_type"); // ロールバック時に 'display_type'カラムを削除
  });
};
