/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  console.log("Placeholder migration for update_expiration_at_in_user_items");
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  console.log("Rollback placeholder migration for update_expiration_at_in_user_items");
};
