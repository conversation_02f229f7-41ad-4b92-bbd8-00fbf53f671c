/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "validity_at");
  if (!exists) {
    return knex.schema.table("pack_info", function (table) {
      table.timestamp("validity_at").nullable(); // 'validity_at'というカラムを追加
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("pack_info", function (table) {
    table.dropColumn("validity_at"); // ロールバック時に 'validity_at'カラムを削除
  });
};
