/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
    const columnInfo = await knex("pack_info").columnInfo("resale");
    if (columnInfo && columnInfo.type === "tinyint") {
      return knex.schema.alterTable("pack_info", function (table) {
        table.string("resale").alter();
      });
    }
  };
  
  /**
   * @param { import("knex").Knex } knex
   * @returns { Promise<void> }
   */
  exports.down = async function (knex) {
    const columnInfo = await knex("pack_info").columnInfo("resale");
    if (columnInfo && columnInfo.type === "varchar") {
      return knex.schema.alterTable("pack_info", function (table) {
        table.boolean("resale").defaultTo(false).alter();
      });
    }
  };
  