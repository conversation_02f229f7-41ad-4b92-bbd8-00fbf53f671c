/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const hasCreatedAtColumn = await knex.schema.hasColumn("buy_discount_coupon", "created_at");
  const hasUpdatedAtColumn = await knex.schema.hasColumn("buy_discount_coupon", "updated_at");

  return knex.schema.table("buy_discount_coupon", function (table) {
    if (!hasCreatedAtColumn) {
      table.timestamp("created_at").defaultTo(knex.fn.now()).notNullable().comment("作成日時");
    }
    if (!hasUpdatedAtColumn) {
      table.timestamp("updated_at").defaultTo(knex.fn.now()).notNullable().comment("更新日時");
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("buy_discount_coupon", function (table) {
    table.dropColumn("created_at");
    table.dropColumn("updated_at");
  });
};
