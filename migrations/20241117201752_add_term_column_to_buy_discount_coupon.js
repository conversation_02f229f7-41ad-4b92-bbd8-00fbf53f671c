/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const hasTermColumn = await knex.schema.hasColumn("buy_discount_coupon", "term");

  if (!hasTermColumn) {
    return knex.schema.table("buy_discount_coupon", function (table) {
      table.integer("term").notNullable().defaultTo(0).comment("クーポンの有効期間（日数）");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("buy_discount_coupon", function (table) {
    table.dropColumn("term");
  });
};
