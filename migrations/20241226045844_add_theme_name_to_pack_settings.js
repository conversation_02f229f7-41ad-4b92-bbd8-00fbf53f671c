/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // テーブル情報を取得し、カラムが存在しない場合にのみ追加
  const hasColumn = await knex.schema.hasColumn("pack_settings", "theme_name");

  if (!hasColumn) {
    return knex.schema.table("pack_settings", function (table) {
      // theme_nameカラムを文字列型で追加。デフォルト値はNULLを許容
      table.string("theme_name").nullable();
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  // テーブル情報を取得し、カラムが存在する場合にのみ削除
  const hasColumn = await knex.schema.hasColumn("pack_settings", "theme_name");

  if (hasColumn) {
    return knex.schema.table("pack_settings", function (table) {
      // ロールバック時にtheme_nameカラムを削除
      table.dropColumn("theme_name");
    });
  }
};
