/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.hasColumn("gp_items", "display").then((exists) => {
    if (!exists) {
      return knex.schema.table("gp_items", (table) => {
        table.string("display").nullable().defaultTo(null).comment("Display column");
      });
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.hasColumn("gp_items", "display").then((exists) => {
    if (exists) {
      return knex.schema.table("gp_items", (table) => {
        table.dropColumn("display");
      });
    }
  });
};
