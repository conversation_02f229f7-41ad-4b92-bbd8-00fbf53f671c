/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
    const exists = await knex.schema.hasColumn("user_items", "is_ship_only");
    if (!exists) {
      return knex.schema.table("user_items", function (table) {
        table.boolean("is_ship_only").defaultTo(false);
      });
    }
  };
  
  /**
   * @param { import("knex").Knex } knex
   * @returns { Promise<void> }
   */
  exports.down = async function (knex) {
    const exists = await knex.schema.hasColumn("user_items", "is_ship_only");
    if (exists) {
      return knex.schema.table("user_items", function (table) {
        table.dropColumn("is_ship_only");
      });
    }
  };