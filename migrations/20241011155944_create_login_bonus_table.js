/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.hasTable("loginBonus").then(function (exists) {
    if (!exists) {
      return knex.schema.createTable("loginBonus", function (table) {
        table.increments("id").primary();
        table.integer("day").notNullable();
        table.timestamp("created_at").defaultTo(knex.fn.now());
      });
    }
  });
};
/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.hasTable("loginBonus").then(function (exists) {
    if (exists) {
      return knex.schema.dropTable("loginBonus");
    }
  });
};
