/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // テーブルに `use_point` カラムが存在しない場合に追加
  const exists = await knex.schema.hasColumn("user", "use_point");
  if (!exists) {
    return knex.schema.table("user", function (table) {
      table.bigInteger("use_point").defaultTo(0).comment("Points used by the user");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  // ロールバック時に `use_point` カラムを削除
  const exists = await knex.schema.hasColumn("user", "use_point");
  if (exists) {
    return knex.schema.table("user", function (table) {
      table.dropColumn("use_point");
    });
  }
};
