/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const hasNameColumn = await knex.schema.hasColumn("buy_discount_coupon", "name");

  if (!hasNameColumn) {
    return knex.schema.table("buy_discount_coupon", function (table) {
      table.string("name").notNullable().defaultTo(""); // nameカラムを追加
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("buy_discount_coupon", function (table) {
    table.dropColumn("name"); // nameカラムを削除
  });
};
