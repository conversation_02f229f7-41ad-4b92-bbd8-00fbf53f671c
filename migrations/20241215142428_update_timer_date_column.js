/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // timer_dateカラムが存在するか確認
  const columnExists = await knex.schema.hasColumn("pack_info", "timer_date");
  if (columnExists) {
    // 現在のカラム型を確認
    const columnType = await knex.raw(`
      SELECT DATA_TYPE
      FROM INFORMATION_SCHEMA.COLUMNS
      WHERE TABLE_SCHEMA = DATABASE()
        AND TABLE_NAME = 'pack_info'
        AND COLUMN_NAME = 'timer_date'
    `);

    // カラムの型がstringの場合のみ更新
    if (columnType[0][0]?.DATA_TYPE === "varchar" || columnType[0][0]?.DATA_TYPE === "text") {
      await knex.schema.alterTable("pack_info", (table) => {
        table.datetime("timer_date").nullable().alter();
      });
      console.log("timer_dateカラムをDATETIME型に変更しました。");
    } else {
      console.log("timer_dateカラムはstring型ではありません。変更は行いません。");
    }
  } else {
    console.log("pack_infoテーブルにtimer_dateカラムは存在しません。");
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  // 元に戻す処理 (元の型がstringの場合に限る)
  await knex.schema.alterTable("pack_info", (table) => {
    table.string("timer_date").nullable().alter();
  });
  console.log("timer_dateカラムをstring型に戻しました。");
};
