/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasTable("items_lp");
  if (!exists) {
    return knex.schema.createTable("items_lp", (table) => {
      table.increments("id").primary(); // 自動インクリメントのID
      table.text("name").notNullable(); // nameカラム（テキスト型、NULL禁止）
      table.integer("price").notNullable(); // priceカラム（整数型、NULL禁止）
      table.text("image").notNullable(); // imageカラム（テキスト型、NULL禁止）
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTableIfExists("items_lp"); // ロールバックでテーブルを削除
};
