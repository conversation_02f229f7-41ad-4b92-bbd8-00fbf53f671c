/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
    const hasCity = await knex.schema.hasColumn('user', 'city');
    const hasCountry = await knex.schema.hasColumn('user', 'country');

    return knex.schema.alterTable('user', function (table) {
        if (!hasCity) {
            table.string('city').nullable();
        }
        if (!hasCountry) {
            table.string('country').nullable();
        }
    });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
    return knex.schema.alterTable('user', function (table) {
        table.dropColumn('city');
        table.dropColumn('country');
    });
};
