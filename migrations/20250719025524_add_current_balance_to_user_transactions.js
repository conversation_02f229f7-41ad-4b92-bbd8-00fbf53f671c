/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function(knex) {
  return knex.schema.table('user_transactions', function(table) {
    table.decimal('current_balance', 10, 2).nullable().after('value').comment('当前余额');
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function(knex) {
  return knex.schema.table('user_transactions', function(table) {
    table.dropColumn('current_balance');
  });
};
