/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "admin_display");

  if (!exists) {
    await knex.schema.alterTable("pack_info", (table) => {
      table.text("admin_display").nullable().comment("管理画面用の表示設定");
    });
    console.log("admin_display カラムを pack_info テーブルに追加しました。");
  } else {
    console.log("admin_display カラムは既に存在します。");
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "admin_display");

  if (exists) {
    await knex.schema.alterTable("pack_info", (table) => {
      table.dropColumn("admin_display");
    });
    console.log("admin_display カラムを pack_info テーブルから削除しました。");
  } else {
    console.log("admin_display カラムは存在しません。");
  }
};
