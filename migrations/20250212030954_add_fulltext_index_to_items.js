/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const [indexExists] = await knex.raw("SHOW INDEX FROM items WHERE Key_name = 'name_fulltext' AND Index_type = 'FULLTEXT';");
  if (indexExists.length === 0) {
    return knex.schema.alterTable("items", function (table) {
      table.index("name", "name_fulltext", "FULLTEXT");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const [indexExists] = await knex.raw("SHOW INDEX FROM items WHERE Key_name = 'name_fulltext' AND Index_type = 'FULLTEXT';");
  if (indexExists.length > 0) {
    return knex.schema.alterTable("items", function (table) {
      table.dropIndex("name", "name_fulltext");
    });
  }
};
