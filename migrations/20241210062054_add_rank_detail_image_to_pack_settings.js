/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.hasColumn("pack_settings", "rank_detail_image").then((exists) => {
    if (!exists) {
      return knex.schema.table("pack_settings", (table) => {
        table.string("rank_detail_image").nullable().comment("Rank detail image URL");
      });
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.hasColumn("pack_settings", "rank_detail_image").then((exists) => {
    if (exists) {
      return knex.schema.table("pack_settings", (table) => {
        table.dropColumn("rank_detail_image");
      });
    }
  });
};
