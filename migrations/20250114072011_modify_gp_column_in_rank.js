/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // Check the column type
  const columnInfo = await knex("rank").columnInfo();
  if (columnInfo.gp && columnInfo.gp.type !== "decimal") {
    return knex.schema.alterTable("rank", function (table) {
      table.decimal("gp", 5, 1).alter();
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  // Revert the column type back (assuming it was initially INTEGER for rollback)
  const columnInfo = await knex("rank").columnInfo();
  if (columnInfo.gp && columnInfo.gp.type === "decimal") {
    return knex.schema.alterTable("rank", function (table) {
      table.integer("gp").alter();
    });
  }
};
