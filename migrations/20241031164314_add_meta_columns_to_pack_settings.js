/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  await knex.schema.alterTable("pack_settings", async function (table) {
    const columns = [
      { name: "meta_title", type: "string", defaultTo: null },
      { name: "meta_description", type: "text", defaultTo: null },
      { name: "favicon_image", type: "string", defaultTo: null },
      { name: "meta_og_image", type: "string", defaultTo: null },
    ];

    for (const col of columns) {
      const exists = await knex.schema.hasColumn("pack_settings", col.name);
      if (!exists) {
        if (col.type === "string") {
          table.string(col.name).defaultTo(col.defaultTo);
        } else if (col.type === "text") {
          table.text(col.name).defaultTo(col.defaultTo);
        }
      }
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  await knex.schema.alterTable("pack_settings", function (table) {
    table.dropColumn("meta_title");
    table.dropColumn("meta_description");
    table.dropColumn("favicon_image");
    table.dropColumn("meta_og_image");
  });
};
