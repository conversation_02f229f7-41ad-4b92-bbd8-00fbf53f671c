/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.hasColumn("user", "display").then((exists) => {
    if (!exists) {
      return knex.schema.table("user", (table) => {
        table.string("display").nullable().defaultTo(null).comment("Display column");
      });
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.hasColumn("user", "display").then((exists) => {
    if (exists) {
      return knex.schema.table("user", (table) => {
        table.dropColumn("display");
      });
    }
  });
};
