/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_settings", "gtm");
  if (!exists) {
    return knex.schema.alterTable("pack_settings", (table) => {
      table.string("gtm").nullable().comment("Google Tag Manager ID");
    });
  }
};
/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_settings", "gtm");
  if (exists) {
    return knex.schema.alterTable("pack_settings", (table) => {
      table.dropColumn("gtm");
    });
  }
};
