/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.hasColumn("pack_info", "timer_date").then((exists) => {
    if (!exists) {
      return knex.schema.alterTable("pack_info", function (table) {
        table.string("timer_date").nullable(); // timer_dateカラムを追加
      });
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.hasColumn("pack_info", "timer_date").then((exists) => {
    if (exists) {
      return knex.schema.alterTable("pack_info", function (table) {
        table.dropColumn("timer_date"); // timer_dateカラムを削除
      });
    }
  });
};
