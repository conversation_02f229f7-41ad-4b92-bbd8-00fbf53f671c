/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("items", "order");
  if (!exists) {
    return knex.schema.alterTable("items", (table) => {
      table.integer("order").nullable().defaultTo(null); // 'order'カラムを追加
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.alterTable("items", (table) => {
    table.dropColumn("order"); // ロールバック時に 'order'カラムを削除
  });
};
