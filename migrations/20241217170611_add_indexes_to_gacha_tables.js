/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // インデックスが存在するか確認してから追加するヘルパー関数
  async function addIndexIfNotExists(knex, tableName, indexName, columnName, length = null) {
    const query = `
      SELECT COUNT(1) AS count 
      FROM INFORMATION_SCHEMA.STATISTICS 
      WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = ? 
        AND INDEX_NAME = ?
    `;

    const [result] = await knex.raw(query, [tableName, indexName]);
    const indexExists = result[0].count;

    if (indexExists === 0) {
      console.log(`インデックス ${indexName} を ${tableName} に追加します。`);
      if (length) {
        await knex.raw(`CREATE INDEX ?? ON ?? (??(?))`, [indexName, tableName, columnName, length]);
      } else {
        await knex.raw(`CREATE INDEX ?? ON ?? (??)`, [indexName, tableName, columnName]);
      }
    } else {
      console.log(`インデックス ${indexName} は既に ${tableName} に存在します。`);
    }
  }

  // pack_info テーブルのインデックス
  await addIndexIfNotExists(knex, "pack_info", "idx_pack_info_id", ["id"]);
  await addIndexIfNotExists(knex, "pack_info", "idx_pack_info_status", ["status"]);
  await addIndexIfNotExists(knex, "pack_info", "idx_pack_info_rest", "rest", 255);

  // items テーブルのインデックス
  await addIndexIfNotExists(knex, "items", "idx_items_packId", ["packId"]);
  await addIndexIfNotExists(knex, "items", "idx_items_rest", ["rest"]);
  await addIndexIfNotExists(knex, "items", "idx_items_category", ["category"]);

  // user テーブルのインデックス
  await addIndexIfNotExists(knex, "user", "idx_user_id", ["id"]);
  await addIndexIfNotExists(knex, "user", "idx_user_point", ["point"]);
  await addIndexIfNotExists(knex, "user", "idx_user_rank", ["rank"]);

  // user_items テーブルのインデックス
  await addIndexIfNotExists(knex, "user_items", "idx_user_items_user_id", ["user_id"]);
  await addIndexIfNotExists(knex, "user_items", "idx_user_items_item_id", ["item_id"]);

  // pack_draw_history テーブルのインデックス
  await addIndexIfNotExists(knex, "pack_draw_history", "idx_pack_draw_history_user_id", ["user_id"]);
  await addIndexIfNotExists(knex, "pack_draw_history", "idx_pack_draw_history_pack_info_id", ["pack_info_id"]);
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  // インデックス削除
  async function dropIndexIfExists(tableName, indexName) {
    const indexExists = await knex.raw(
      `SELECT COUNT(1) AS count FROM INFORMATION_SCHEMA.STATISTICS 
       WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? AND INDEX_NAME = ?`,
      [tableName, indexName]
    );

    if (indexExists[0][0].count > 0) {
      console.log(`インデックス ${indexName} を ${tableName} から削除します。`);
      await knex.schema.table(tableName, function (table) {
        table.dropIndex([], indexName);
      });
    }
  }

  // インデックスを削除
  await dropIndexIfExists("pack_info", "idx_pack_info_id");
  await dropIndexIfExists("pack_info", "idx_pack_info_status");
  await dropIndexIfExists("pack_info", "idx_pack_info_rest");

  await dropIndexIfExists("items", "idx_items_packId");
  await dropIndexIfExists("items", "idx_items_rest");
  await dropIndexIfExists("items", "idx_items_category");

  await dropIndexIfExists("user", "idx_user_id");
  await dropIndexIfExists("user", "idx_user_point");
  await dropIndexIfExists("user", "idx_user_rank");

  await dropIndexIfExists("user_items", "idx_user_items_user_id");
  await dropIndexIfExists("user_items", "idx_user_items_item_id");

  await dropIndexIfExists("pack_draw_history", "idx_pack_draw_history_user_id");
  await dropIndexIfExists("pack_draw_history", "idx_pack_draw_history_pack_info_id");
};
