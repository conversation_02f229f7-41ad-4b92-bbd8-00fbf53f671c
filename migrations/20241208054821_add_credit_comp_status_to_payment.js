/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const hasColumn = await knex.schema.hasColumn("payment", "credit_comp_status");
  if (!hasColumn) {
    return knex.schema.table("payment", function (table) {
      table.string("credit_comp_status").nullable().comment("クレジットカード完了ステータス");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const hasColumn = await knex.schema.hasColumn("payment", "credit_comp_status");
  if (hasColumn) {
    return knex.schema.table("payment", function (table) {
      table.dropColumn("credit_comp_status");
    });
  }
};
