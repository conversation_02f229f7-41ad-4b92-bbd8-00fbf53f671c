/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("user_items", "result_id");

  if (!exists) {
    return knex.schema.table("user_items", (table) => {
      table.string("result_id").nullable().comment("結果IDを保存するカラム");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const exists = await knex.schema.hasColumn("user_items", "result_id");

  if (exists) {
    return knex.schema.table("user_items", (table) => {
      table.dropColumn("result_id");
    });
  }
};
