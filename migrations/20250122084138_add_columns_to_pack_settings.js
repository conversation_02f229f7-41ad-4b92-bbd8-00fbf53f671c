/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
    const columns = [
      { name: "header_point_icon", type: "string", length: 255, default: null },
      { name: "header_gp_icon", type: "string", length: 255, default: null },
      { name: "category_slider_left_arrow", type: "string", length: 255, default: null },
      { name: "category_slider_right_arrow", type: "string", length: 255, default: null },
      { name: "pack_item_point_icon", type: "string", length: 255, default: null },
      { name: "pack_draw_button_icon", type: "string", length: 255, default: null },
      { name: "detail_item_background", type: "string", length: 255, default: null },
      { name: "detail_item_title_image_s", type: "string", length: 255, default: null },
      { name: "detail_item_title_image_a", type: "string", length: 255, default: null },
      { name: "detail_item_title_image_b", type: "string", length: 255, default: null },
      { name: "detail_item_title_image_c", type: "string", length: 255, default: null },
      { name: "detail_item_title_image_round_num", type: "string", length: 255, default: null },
      { name: "detail_item_title_image_last_one", type: "string", length: 255, default: null },
      { name: "header_point_border_color", type: "string", length: 7, default: "#FFFFFF" },
    ];
  
    for (const column of columns) {
      const exists = await knex.schema.hasColumn("pack_settings", column.name);
      if (!exists) {
        await knex.schema.table("pack_settings", function (table) {
          if (column.type === "string") {
            table.string(column.name, column.length).defaultTo(column.default);
          }
        });
      }
    }
  };
  
  /**
   * @param { import("knex").Knex } knex
   * @returns { Promise<void> }
   */
  exports.down = async function (knex) {
    const columns = ["header_point_icon", "header_gp_icon", "category_slider_left_arrow", "category_slider_right_arrow", "pack_item_point_icon", "pack_draw_button_icon", "detail_item_background", "detail_item_title_image_s", "detail_item_title_image_a", "detail_item_title_image_b", "detail_item_title_image_c", "detail_item_title_image_round_num", "detail_item_title_image_last_one", "header_point_border_color"];
  
    for (const column of columns) {
      const exists = await knex.schema.hasColumn("pack_settings", column);
      if (exists) {
        await knex.schema.table("pack_settings", function (table) {
          table.dropColumn(column);
        });
      }
    }
  };
  