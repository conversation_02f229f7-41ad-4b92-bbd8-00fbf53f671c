/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasTable("line_login_state");
  if (!exists) {
    return knex.schema.createTable("line_login_state", function (table) {
      table.increments("id").primary(); // AIカラム
      table.string("state").notNullable(); // 文字列型のstateカラム
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTableIfExists("line_login_state"); // テーブルが存在する場合のみ削除
};
