/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("rank", "buy_add_point_percent");

  if (!exists) {
    return knex.schema.alterTable("rank", function (table) {
      table.integer("buy_add_point_percent").defaultTo(0).notNullable();
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const exists = await knex.schema.hasColumn("rank", "buy_add_point_percent");

  if (exists) {
    return knex.schema.alterTable("rank", function (table) {
      table.dropColumn("buy_add_point_percent");
    });
  }
};
