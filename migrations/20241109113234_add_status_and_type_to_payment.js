/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const statusExists = await knex.schema.hasColumn("payment", "status");
  const typeExists = await knex.schema.hasColumn("payment", "type");

  return knex.schema.table("payment", function (table) {
    if (!statusExists) {
      table.string("status").notNullable().defaultTo("pending"); // ステータスカラム
    }
    if (!typeExists) {
      table.string("type").notNullable().defaultTo("general"); // タイプカラム
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("payment", function (table) {
    table.dropColumn("status");
    table.dropColumn("type");
  });
};
