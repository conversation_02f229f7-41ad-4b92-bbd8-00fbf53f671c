/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
    const exists = await knex.schema.hasColumn("pack_settings", "maintenance_status");
    if (!exists) {
      return knex.schema.table("pack_settings", function (table) {
        table.string("maintenance_status");
      });
    }
  };
  
  /**
   * @param { import("knex").Knex } knex
   * @returns { Promise<void> }
   */
  exports.down = async function (knex) {
    const exists = await knex.schema.hasColumn("pack_settings", "maintenance_status");
    if (exists) {
      return knex.schema.table("pack_settings", function (table) {
        table.dropColumn("maintenance_status");
      });
    }
  };
  