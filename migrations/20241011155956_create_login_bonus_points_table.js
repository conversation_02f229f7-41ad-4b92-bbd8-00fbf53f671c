/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = function (knex) {
  return knex.schema.hasTable("loginBonusPoints").then(function (exists) {
    if (!exists) {
      return knex.schema.createTable("loginBonusPoints", function (table) {
        table.increments("id").primary();
        table.integer("login_bonus_id").unsigned().notNullable().references("id").inTable("loginBonus").onDelete("CASCADE");
        table.integer("point").notNullable();
        table.string("video_path").notNullable();
        table.timestamp("created_at").defaultTo(knex.fn.now());
      });
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.hasTable("loginBonusPoints").then(function (exists) {
    if (exists) {
      return knex.schema.dropTable("loginBonusPoints");
    }
  });
};
