/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const hasStatusColumn = await knex.schema.hasColumn("payment", "status");
  const hasTypeColumn = await knex.schema.hasColumn("payment", "type");

  return knex.schema.table("payment", function (table) {
    if (!hasStatusColumn) {
      table.string("status").notNullable().defaultTo("pending");
    }
    if (!hasTypeColumn) {
      table.string("type").notNullable().defaultTo("general");
    }
  });
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("payment", function (table) {
    table.dropColumn("status");
    table.dropColumn("type");
  });
};
