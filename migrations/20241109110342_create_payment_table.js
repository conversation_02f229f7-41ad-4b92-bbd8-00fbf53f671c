/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasTable("payment");
  if (!exists) {
    return knex.schema.createTable("payment", function (table) {
      table.increments("id").primary(); // 自動インクリメントID
      table.string("order_id").notNullable(); // 注文ID
      table.integer("user_id").notNullable(); // ユーザーID
      table.timestamp("date").defaultTo(knex.fn.now()); // 登録時のタイムスタンプ
      table.decimal("amount", 10, 2).notNullable(); // 金額
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.dropTableIfExists("payment"); // テーブルが存在する場合のみ削除
};
