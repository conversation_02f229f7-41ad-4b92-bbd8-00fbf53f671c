/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const hasColumn = await knex.schema.hasColumn("userCoupons", "buy_status");
  if (!hasColumn) {
    return knex.schema.table("userCoupons", function (table) {
      table.string("buy_status").nullable();
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("userCoupons", function (table) {
    table.dropColumn("buy_status");
  });
};
