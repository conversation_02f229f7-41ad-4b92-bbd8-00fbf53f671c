/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("items", "display_rest");
  if (!exists) {
    return knex.schema.table("items", function (table) {
      table.integer("display_rest").defaultTo(null);
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  const exists = await knex.schema.hasColumn("items", "display_rest");
  if (exists) {
    return knex.schema.table("items", function (table) {
      table.dropColumn("display_rest");
    });
  }
};
