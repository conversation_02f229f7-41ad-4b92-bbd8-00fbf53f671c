/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  const exists = await knex.schema.hasColumn("pack_info", "sold_out_at");
  if (!exists) {
    return knex.schema.table("pack_info", function (table) {
      table.timestamp("sold_out_at").nullable(); // 'sold_out_at'というタイムスタンプ型のカラムを追加
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  return knex.schema.table("pack_info", function (table) {
    table.dropColumn("sold_out_at"); // ロールバック時に 'sold_out_at'カラムを削除
  });
};
