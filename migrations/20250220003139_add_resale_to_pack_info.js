/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
    const exists = await knex.schema.hasColumn("pack_info", "resale");
    if (!exists) {
      return knex.schema.table("pack_info", function (table) {
        table.boolean("resale").defaultTo(false);
      });
    }
  };
  
  /**
   * @param { import("knex").Knex } knex
   * @returns { Promise<void> }
   */
  exports.down = async function (knex) {
    const exists = await knex.schema.hasColumn("pack_info", "resale");
    if (exists) {
      return knex.schema.table("pack_info", function (table) {
        table.dropColumn("resale");
      });
    }
  };