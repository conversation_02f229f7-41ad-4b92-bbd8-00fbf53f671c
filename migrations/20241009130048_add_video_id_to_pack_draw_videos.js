/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // カラムの存在をチェック
  const columnExists = await knex.schema.hasColumn("pack_draw_videos", "video_id");

  if (!columnExists) {
    // カラムが存在しない場合に追加
    return knex.schema.alterTable("pack_draw_videos", function (table) {
      table.integer("video_id");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = function (knex) {
  // ロールバック時にカラムを削除
  return knex.schema.alterTable("pack_draw_videos", function (table) {
    table.dropColumn("video_id");
  });
};
