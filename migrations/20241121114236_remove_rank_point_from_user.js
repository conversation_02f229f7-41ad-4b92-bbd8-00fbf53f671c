/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.up = async function (knex) {
  // rank_point カラムが存在する場合に削除
  const exists = await knex.schema.hasColumn("user", "rank_point");
  if (exists) {
    return knex.schema.table("user", function (table) {
      table.dropColumn("rank_point");
    });
  }
};

/**
 * @param { import("knex").Knex } knex
 * @returns { Promise<void> }
 */
exports.down = async function (knex) {
  // ロールバック時に rank_point カラムを再作成
  return knex.schema.table("user", function (table) {
    table.bigInteger("rank_point").nullable(); // データ型は適切に変更してください
  });
};
