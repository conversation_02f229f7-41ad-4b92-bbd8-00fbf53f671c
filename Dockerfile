# 使用单阶段构建
FROM --platform=linux/amd64 node:20.12.0-slim

WORKDIR /usr/src/app

# 安装编译工具链（仅用于构建 bcrypt）
RUN apt-get update && apt-get install -y \
    make \
    gcc \
    g++ \
    python3 \
    && rm -rf /var/lib/apt/lists/*

# 仅复制 package.json 和 package-lock.json 以提高缓存利用率
COPY package*.json ./

# 安装所有依赖（包括生产依赖）
RUN npm install --build-from-source

# 复制应用代码
COPY . .

# 设置非 root 用户
RUN chown -R node:node /usr/src/app
USER node

EXPOSE 8080

# 直接运行入口文件
CMD ["node", "index.js"]
