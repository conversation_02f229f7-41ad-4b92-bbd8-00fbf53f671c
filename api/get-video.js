const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.get("/", authenticateJWT, (req, res) => {
  db.query("SELECT * FROM pack_video", (err, results) => {
    if (err) {
      console.error("Error fetching videos:", err);
      res.status(500).send("Failed to fetch videos");
      logErr(`演出動画一覧の取得ができませんでした。エラー内容:${err} - get-video`);
      return;
    }
    res.status(200).json(results);
    logAdminPut(`演出動画一覧の取得ができました。 - get-video`);
  });
});

module.exports = router;
