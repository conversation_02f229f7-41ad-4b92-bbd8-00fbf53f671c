const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.post("/", authenticateJWT, (req, res) => {
  const { removeNumber } = req.body;

  if (!removeNumber) {
    res.status(400).send("removeNumber is required");
    return;
  }

  db.query("DELETE FROM pack_type WHERE id = ?", [removeNumber], (err, results) => {
    if (err) {
      console.error("Error deleting record:", err);
      res.status(500).send("Failed to delete record");
      logErr(`該当の種類[ID:${removeNumber}]を削除できませんでした。エラー内容:${err} - admin-type-remove`);
      return;
    }

    if (results.affectedRows === 0) {
      res.status(404).send("No record found with the given ID");
      logAdminPut(`該当の種類[ID:${removeNumber}]を削除できました。 - admin-type-remove`);
      return;
    }

    res.status(200).json({ message: "Record deleted successfully" });
  });
});

module.exports = router;
