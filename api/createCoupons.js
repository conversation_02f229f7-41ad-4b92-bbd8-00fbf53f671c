const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.post("/", authenticateJWT, async (req, res) => {
  const { name, type, percentage, term, coupon_key, roundNum, rest } = req.body;

  if (!name || !type || !percentage || !term || !coupon_key) {
    return res.status(400).json({ error: "All fields are required" });
  }

  try {
    const result = await db.query("INSERT INTO coupons (name, type, percentage, term, coupon_key,roundNum,rest) VALUES (?, ?, ?, ?, ?, ?, ?)", [name, type, percentage, term, coupon_key, roundNum, rest]);
    res.status(201).json({ message: "Coupon created successfully", couponId: result.insertId });
    logAdminPut(`新しいクーポンを作成しました。`);
  } catch (error) {
    console.error("Error creating coupon:", error);
    res.status(500).json({ error: "Internal server error" });
    logErr(`新しいクーポンを作成できませんでした。エラー内容:${error}`);
  }
});

module.exports = router;
