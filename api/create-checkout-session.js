const express = require("express");
require("dotenv").config();
const Stripe = require("stripe");
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY);
const authenticateJWT = require("../auth/auth");

const router = express.Router();

// Middleware
router.use(express.json());

// POST /api/payment/create-checkout-session
// 创建 Stripe Checkout 会话
router.post("/", authenticateJWT, async (req, res) => {
    try {
        //amount单位是美分
        const {amount, userId, point} = req.body;

        // 基础校验
        if (!amount || !userId || !point) {
            return res.status(400).json({error: "Missing required fields"});
        }

        if (amount <= 0 || point <= 0) {
            return res.status(400).json({error: "Invalid amount or point"});
        }

        const session = await stripe.checkout.sessions.create({
            payment_method_types: ["card"],
            line_items: [
                {
                    price_data: {
                        currency: "usd",
                        product_data: {
                            name: "Lottery Points",
                        },
                        unit_amount: amount,
                    },
                    quantity: 1,
                },
            ],
            mode: "payment",
            success_url: `${process.env.CLIENT_URL}/user/packList?session_id={CHECKOUT_SESSION_ID}&status=success&point=${point}`,
            // cancel_url: `${process.env.CLIENT_URL}/user/Payrecord?status=cancelled`,
            metadata: {
                userId: userId,
                pointAmount: point,
                payAmount: amount
            },
            client_reference_id: userId,
        });

        res.json({id: session.id});
    } catch (err) {
        console.error("Error creating checkout session:", err);
        res.status(500).json({error: "Failed to create payment session"});
    }
});

// GET /api/payment-status
// 查询支付状态（供前端页面调用）
router.get("/payment-status", async (req, res) => {
    const {session_id} = req.query;

    if (!session_id) {
        return res.status(400).json({error: "Missing session_id"});
    }

    try {
        const session = await stripe.checkout.sessions.retrieve(session_id);

        if (session.payment_status === 'paid') {
            res.json({
                paid: true,
                pointAmount: parseInt(session.metadata.pointAmount),
                amount: session.amount_total ? session.amount_total / 100 : 0, // 转为美元
                currency: session.currency,
            });
        } else {
            res.json({paid: false});
        }
    } catch (err) {
        console.error("Error retrieving session:", err);
        res.status(500).json({error: "Failed to verify payment"});
    }
});

module.exports = router;
