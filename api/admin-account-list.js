const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.get("/", authenticateJWT, (req, res) => {
  db.query("SELECT id, mail FROM admin_accounts", (err, results) => {
    if (err) {
      console.error("Error fetching info:", err);
      logErr(`管理者アカウントリストを取得できませんでした。エラー内容:${err} - admin-account-list`);
      res.status(500).send("Failed to fetch info");
      return;
    }
    res.status(200).json(results);
    logAdminPut("管理者アカウントリストを取得しました。 - admin-account-list");
  });
});

module.exports = router;
