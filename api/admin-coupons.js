const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

// クーポンの取得
router.get("/", authenticateJWT, (req, res) => {
  db.query("SELECT * FROM coupons", (err, results) => {
    if (err) {
      console.error("Error fetching coupons:", err);
      res.status(500).send("Failed to fetch coupons");
      logErr(`クーポンの取得ができませんでした。エラー内容:${err} - admin-coupons`);
      return;
    }
    res.status(200).json(results);
    logAdminPut(`クーポンの取得が完了しました。`);
  });
});

// クーポンの削除
router.post("/remove", authenticateJWT, async (req, res) => {
  const { couponId } = req.body;

  if (!couponId) {
    logErr(`該当のクーポンが存在していませんでした。該当クーポン:${couponId} - admin-coupons`);
    return res.status(400).json({ error: "Coupon ID is required" });
  }

  try {
    await db.query("DELETE FROM coupons WHERE id = ?", [couponId]);
    res.json({ message: "Coupon deleted successfully" });
    logAdminPut(`該当クーポン[ID:${couponId}]を削除しました。 - admin-coupons`);
  } catch (error) {
    console.error("Error deleting coupon:", error);
    res.status(500).json({ error: "Internal server error" });
    logErr(`該当のクーポンが削除できませんでした。エラー内容:${error} - admin-coupons`);
  }
});

module.exports = router;
