const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.get("/", authenticateJWT, (req, res) => {
  db.query("SELECT * FROM pack_video WHERE defaultStatus = 0", (err, results) => {
    if (err) {
      console.error("Error fetching videos:", err);
      res.status(500).send("Failed to fetch videos");
      logErr(`特別な動画の取得ができませんでした。エラー内容:${err} - get-special-video`);
      return;
    }
    res.status(200).json(results);
    logAdminPut(`特別な動画の取得ができました。 - get-special-video`);
  });
});

module.exports = router;
