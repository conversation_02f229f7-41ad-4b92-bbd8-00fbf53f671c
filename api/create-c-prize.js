const express = require("express");
const router = express.Router();
const multer = require("multer");
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");
const AWS = require("aws-sdk");

// Multer設定
// const storage = multer.diskStorage({
//   destination: function (req, file, cb) {
//     cb(null, "uploads/");
//   },
//   filename: function (req, file, cb) {
//     cb(null, `${Date.now()}-${file.originalname}`);
//   },
// });
//
// // uploadにmulterのstorageプロパティを代入したオブジェクトを定義
// const upload = multer({ storage: storage });
//
// router.post("/", authenticateJWT, upload.any(), async (req, res) => {
//   try {
//     const items = req.body;
//     const files = req.files;
//
//     for (let i = 0; i < files.length; i++) {
//       const file = files[i];
//       const itemId = file.fieldname.split("_")[1]; // フィールド名からitemIdを抽出
//       const name = items[`itemsC_${itemId}_name`];
//       const price = items[`itemsC_${itemId}_price`];
//       const image = `uploads/${file.filename}`;
//       const exchangeOnly = items[`itemsC_${itemId}_exchangeOnly`] === "true"; // 'true'をbooleanに変換
//       const type = items[`itemsC_${itemId}_type`];
//       const category = items[`itemsC_${itemId}_category`];
//
//       // データベースに挿入
//       await new Promise((resolve, reject) => {
//         const query = "INSERT INTO items_loser (category, name, price, image, is_exchange_only, type, item_category) VALUES (?, ?, ?, ?, ?, ?, ?)";
//         db.query(query, ["itemsC", name, price, image, exchangeOnly, type, category], (err, result) => {
//           if (err) {
//             logErr(`ハズレアイテムをデータベースに挿入できませんでした。エラー内容:${err} - create-c-prize`);
//             reject(err);
//           } else {
//             resolve(result);
//           }
//         });
//       });
//     }
//
//     logAdminPut(`ハズレアイテムをデータベースに挿入できました。 - create-c-prize`);
//     res.status(200).json({ message: "Data was successfully inserted." });
//   } catch (error) {
//     logErr(`ハズレアイテムをデータベースに挿入できませんでした。エラー内容:${error} - create-c-prize`);
//     console.error("Error inserting data:", error);
//     res.status(500).json({ message: "Failed to insert data." });
//   }
// });


// 初始化 S3 客户端
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
  region: process.env.AWS_REGION || "us-east-1", // S3 区域
});

// Multer 配置（改为内存存储）
const upload = multer({
  storage: multer.memoryStorage(), // 将文件存储在内存中，而不是磁盘
}).any(); // 支持解析任意数量的文件字段

// 文件上传路由
router.post("/", authenticateJWT, upload, async (req, res) => {
// router.post("/", upload, async (req, res) => {
  try {
    const items = req.body; // 获取请求体中的表单数据
    const files = req.files; // 获取上传的文件列表

    for (let i = 0; i < files.length; i++) {
      const file = files[i];
      const itemId = file.fieldname.split("_")[1]; // 从字段名中提取 itemId
      const name = items[`itemsC_${itemId}_name`]; // 名称
      const price = items[`itemsC_${itemId}_price`]; // 价格
      const exchangeOnly = items[`itemsC_${itemId}_exchangeOnly`] === "true"; // 是否仅限兑换（字符串转布尔值）
      const type = items[`itemsC_${itemId}_type`]; // 类型
      const category = items[`itemsC_${itemId}_category`]; // 分类

      // 上传文件到 S3
      const params = {
        Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
        Key: `${Date.now()}-${file.originalname}`, // 文件名
        Body: file.buffer, // 文件内容（存储在内存中的 Buffer）
        ContentType: file.mimetype, // 文件类型
      };

      const result = await s3.upload(params).promise(); // 上传文件
      const image = result.Location; // 获取文件的公共访问 URL

      // 插入数据到数据库
      await new Promise((resolve, reject) => {
        const query =
            "INSERT INTO items_loser (category, name, price, image, is_exchange_only, type, item_category) VALUES (?, ?, ?, ?, ?, ?, ?)";
        db.query(
            query,
            ["itemsC", name, price, image, exchangeOnly, type, category],
            (err, result) => {
              if (err) {
                logErr(`失败插入失败物品到数据库。错误内容:${err} - create-c-prize`);
                reject(err);
              } else {
                resolve(result);
              }
            }
        );
      });
    }

    logAdminPut(`成功插入失败物品到数据库。 - create-c-prize`);
    res.status(200).json({ message: "数据已成功插入。" });
  } catch (error) {
    logErr(`失败插入失败物品到数据库。错误内容:${error} - create-c-prize`);
    console.error("插入数据时发生错误:", error);
    res.status(500).json({ message: "插入数据失败。" });
  }
});

module.exports = router;
