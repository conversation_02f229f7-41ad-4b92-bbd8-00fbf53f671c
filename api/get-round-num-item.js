const express = require("express");
const router = express.Router();
const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

router.get("/:packId", async (req, res) => {
  const packId = req.params.packId;
  try {
    const query = "SELECT round_num FROM items WHERE packId = ? ORDER BY round_num DESC";
    db.query(query, [packId], (error, results) => {
      if (error) {
        console.error("Error fetching round_num:", error);
        logErr(`キリ番アイテムを取得できませんでした。エラー内容:${error} - get-round-num-item`);
        return res.status(500).json({ error: "Internal Server Error" });
      }
      const roundNums = results.map((row) => row.round_num);
      res.json(roundNums);
    });
  } catch (error) {
    console.error("Error fetching round_num:", error);
    logErr(`キリ番アイテムを取得できませんでした。エラー内容:${error} - get-round-num-item`);
    res.status(500).json({ error: "Internal Server Error" });
  }
});

module.exports = router;
