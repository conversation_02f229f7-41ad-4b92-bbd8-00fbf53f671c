const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

router.get("/", (req, res) => {
  const rank = req.query.rank;
  const packId = req.query.packId;
  const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}`;

  // pack_idとrankに基づいて動画を取得するクエリを実行
  const queryWithPackId = "SELECT video_path FROM pack_draw_videos WHERE `pack_id` = ? AND `rank` = ? ORDER BY RAND() LIMIT 1";
  db.query(queryWithPackId, [packId, rank], (err, results) => {
    if (err) {
      logErr(`動画取得のクエリを実行できませんでした。エラー内容:${err} - random-video`);
      console.error("Database query error:", err);
      return res.status(500).json({ message: "Failed to get video." });
    }

    // 結果がある場合は、その動画パスを返す
    if (results.length > 0) {
      return res.status(200).json({ videoPath: results[0].video_path });
    }

    // 結果がない場合は、pack_idを考慮せずにrankとdefaultStatusがtrueに基づいて動画を取得
    const queryWithoutPackId = "SELECT video_path FROM pack_video WHERE `rank` = ? AND `defaultStatus` = true ORDER BY RAND() LIMIT 1";
    db.query(queryWithoutPackId, [rank], (err, results) => {
      if (err) {
        logErr(`動画取得のクエリを実行できませんでした。エラー内容:${err} - random-video`);
        console.error("Database query error:", err);
        return res.status(500).json({ message: "Failed to get video." });
      }
      if (results.length === 0) {
        return res.status(404).json({ message: "No video found for the given rank." });
      }
      res.status(200).json({ videoPath: results[0].video_path });
      logUserPut(`ガチャ動画の取得が完了しました。 - random-video`);
    });
  });
});

module.exports = router;
