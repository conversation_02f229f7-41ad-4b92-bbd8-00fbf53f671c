const express = require("express");
require("dotenv").config();
const Stripe = require("stripe");
const stripe = Stripe(process.env.STRIPE_SECRET_KEY);
const bodyParser = require("body-parser");

const app = express();
app.use(bodyParser.json());

app.post("/create-checkout-session", async (req, res) => {
  const { amount } = req.body;

  const session = await stripe.checkout.sessions.create({
    payment_method_types: ["card"],
    line_items: [
      {
        price_data: {
          currency: "jpy",
          product_data: {
            name: "Lottery Points",
          },
          unit_amount: amount, // 円単位
        },
        quantity: 1,
      },
    ],
    mode: "payment",
    success_url: "",
    cancel_url: "",
  });

  res.json({ id: session.id });
});
