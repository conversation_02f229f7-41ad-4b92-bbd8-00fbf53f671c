const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.post("/", authenticateJWT, (req, res) => {
  const { removeNumber } = req.body;

  if (!removeNumber) {
    return res.status(400).send("removeNumber is required");
  }

  db.query("UPDATE gp_items SET display = 'invalid' WHERE id = ?", [removeNumber], (err, results) => {
    if (err) {
      console.error("Error deleting record:", err);
      logErr(`該当のgpアイテム[ID:${removeNumber}]を削除できませんでした。エラー内容:${err} - admin-remove-gp-item`);
      return res.status(500).send("Failed to delete record");
    }

    if (results.affectedRows === 0) {
      logAdminPut(`該当のgpアイテム[ID:${removeNumber}]を削除しました。 - admin-remove-gp-item`);
      return res.status(404).send("No record found with the given ID");
    }

    res.status(200).json({ message: "Record deleted successfully" });
  });
});

module.exports = router;
