const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

router.get("/", (req, res) => {
  db.query("SELECT * FROM info", (err, results) => {
    if (err) {
      console.error("Error fetching infos:", err);
      res.status(500).send("Failed to fetch infos");
      logErr(`お知らせ一覧を取得できませんでした。エラー内容:${err} - get-info`);
      return;
    }
    // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
    const baseURL = ``;
    const formattedResults = results.map((item) => ({
      ...item,
      image: baseURL + item.image,
    }));
    res.status(200).json(formattedResults);
    logUserPut(`お知らせ一覧を取得できました。 - get-info`);
  });
});

module.exports = router;
