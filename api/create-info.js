const express = require("express");
const router = express.Router();
const multer = require("multer");
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const {logAdminPut, logErr} = require("../logger");
const AWS = require("aws-sdk");

// Multer設定
// const storage = multer.diskStorage({
//   destination: function (req, file, cb) {
//     cb(null, "uploads/");
//   },
//   filename: function (req, file, cb) {
//     cb(null, `${Date.now()}-${file.originalname}`);
//   },
// });
//
// //uploadにmulterのstorageプロパティを代入したオブジェクトを定義
// const upload = multer({ storage: storage });

const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
    region: process.env.AWS_REGION || "us-east-1", // S3 区域
});

// Multer 配置（改为内存存储）
const upload = multer({
    storage: multer.memoryStorage(), // 将文件存储在内存中，而不是磁盘
}).any(); // 支持解析任意数量的文件字段


router.post("/", authenticateJWT, upload, async (req, res) => {
    const infoName = req.body["info_name"];
    const infoLink = req.body["info_link"];
    const infoImg = req.files.find((file) => file.fieldname === "info_img");

    try {
        let infoImgUrl = null;

        // 如果有上传的图片，则上传到 S3
        if (infoImg) {
            const params = {
                Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
                Key: `${Date.now()}-${infoImg.originalname}`, // 文件名
                Body: infoImg.buffer, // 文件内容（存储在内存中的 Buffer）
                ContentType: infoImg.mimetype, // 文件类型
            };

            const s3Result = await s3.upload(params).promise();
            infoImgUrl = s3Result.Location; // 获取文件的公共访问 URL
        }

        // 插入数据库记录
        const packInfoResult = await new Promise((resolve, reject) => {
            db.query(
                "INSERT INTO info (name, link, image) VALUES (?,?,?)",
                [infoName, infoLink, infoImgUrl],
                (err, result) => {
                    if (err) {
                        console.error("插入公告失败:", err);
                        logErr(`创建公告失败。错误内容: ${err} - create-info`);
                        return reject(err);
                    }
                    resolve(result.insertId);
                }
            );
        });

        res.status(200).json({message: "公告成功插入", id: packInfoResult});
        logAdminPut(`公告创建成功。 - create-info`);
    } catch (error) {
        console.error("处理公告时发生错误:", error);
        logErr(`公告创建失败。错误内容: ${error.message} - create-info`);
        res.status(500).json({message: "插入公告失败", error: error.message});
    }
});

module.exports = router;
