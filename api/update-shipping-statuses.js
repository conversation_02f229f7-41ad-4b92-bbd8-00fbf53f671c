const express = require("express");
const router = express.Router();
const db = require("../db/db");
const createTransporter = require("../service/mailService");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.post("/", authenticateJWT, (req, res) => {
  const { items } = req.body;

  if (!items || items.length === 0) {
    return res.status(400).json({ message: "No items provided" });
  }

  const queries = items.map((item) => {
    const isGpItem = item.user_gp_item_id !== undefined && item.user_gp_item_id !== null;

    const query = isGpItem
      ? `
        UPDATE shipping_statuses
        SET status = 'shipped', tracking_number = ?
        WHERE user_gp_item_id = ?`
      : `
        UPDATE shipping_statuses
        SET status = 'shipped', tracking_number = ?
        WHERE user_item_id = ?`;

    const itemId = isGpItem ? item.user_gp_item_id : item.user_item_id;

    return new Promise((resolve, reject) => {
      db.query(query, [item.tracking_number, itemId], (err, results) => {
        if (err) {
          logErr(`配送ステータスの変更ができませんでした。エラー内容:${err} - update-shipping-statuses`);
          console.error("Error updating shipping status for item:", itemId, err);
          return reject(err);
        }

        resolve({ itemId, userMail: item.user_mail, itemName: item.item_name, trackingNumber: item.tracking_number });
      });
    });
  });

  Promise.all(queries)
    .then(async (results) => {
      const transport = createTransporter();

      const groupedEmails = results.reduce((acc, { itemId, userMail, itemName, trackingNumber }) => {
        if (!acc[userMail]) {
          acc[userMail] = [];
        }
        acc[userMail].push({ itemName, trackingNumber });
        return acc;
      }, {});

      const emailPromises = Object.keys(groupedEmails).map((userMail) => {
        const itemsInfo = groupedEmails[userMail].map(({ itemName, trackingNumber }) => `- ${itemName}: ${trackingNumber}`).join("\n");

        const mail = {
          from: process.env.SMTP_USER,
          to: userMail,
          subject: "依頼いただいたアイテムを配送致しました。",
          text: `依頼いただいたアイテムを配送いたしました。\n配送内容は下記のとおりです。\n\n${itemsInfo}`,
        };

        return transport.sendMail(mail);
      });

      try {
        await Promise.all(emailPromises);
        console.log("+++ Emails Sent +++");
      } catch (err) {
        logErr(`配送ステータスの変更のメールが送信できませんでした。エラー内容:${err} - update-shipping-statuses`);
        console.log("--- Email Sending Error ---");
        console.log(err);
      }
      logAdminPut(`配送ステータスの変更ができました。 - update-shipping-statuses`);
      res.status(200).json({ message: "Shipping statuses updated to 'shipped' and emails sent" });
    })
    .catch((err) => {
      logErr(`配送ステータスの変更ができませんでした。エラー内容:${err} - update-shipping-statuses`);
      console.error("Error updating shipping statuses:", err);
      res.status(500).json({ message: "Internal Server Error" });
    });
});

router.post("/reverse", authenticateJWT, (req, res) => {
  const { itemId } = req.body;

  const query = `
        UPDATE shipping_statuses
        SET status = 'accepted' 
        WHERE id = ?`;

  db.query(query, [itemId], (err, results) => {
    if (err) {
      console.error("Error updating shipping status:", err);
      logErr(`配送依頼のステータスを更新できませんでした。エラー内容:${err} - shipping-statuses`);
      return res.status(500).json({ message: "Failed to update the delivery request status. Error details" });
    }

    res.status(200).json({ message: "The item update has been completed successfully." });
    logAdminPut(`配送依頼のステータスを更新しました。`);
  });
});

module.exports = router;
