const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const { logAdminPut, logErr, logUserPut } = require("../logger");
const authenticateJWT = require("../auth/rankCategoryAuth");

router.get("/", authenticateJWT, (req, res) => {
  const userId = req.user ? req.user.userId : null;

  console.log(userId);

  // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
  const baseURL = ``;

  // pack_categoryとrankテーブルのデータを取得
  const packCategoryQuery = "SELECT * FROM pack_category";

  db.query(packCategoryQuery, (err, packResults) => {
    if (err) {
      console.error("Error fetching pack categories:", err);
      res.status(500).send("Failed to fetch pack categories");
      logErr(`パックカテゴリー一覧を取得できませんでした。エラー内容:${err} - categories`);
      return;
    }

    // 画像パスを絶対URLに変換
    let categories = packResults.map((category) => {
      return {
        ...category,
        image_path: category.image_path ? baseURL + category.image_path : null,
      };
    });

    if (userId) {
      // ユーザーのrankを取得
      const userRankQuery = "SELECT `rank` FROM user WHERE id = ?";
      db.query(userRankQuery, [userId], (err, userResults) => {
        if (err) {
          console.error("Error fetching user rank:", err);
          res.status(500).send("Failed to fetch user rank");
          logErr(`ユーザーID[${userId}]のランクを取得できませんでした。エラー内容:${err} - categories`);
          return;
        }

        if (userResults.length > 0) {
          const userRank = userResults[0].rank;

          // rankテーブルからorderカラムが一致するレコードを取得
          const rankOrderQuery = "SELECT * FROM `rank` WHERE `order` <= ?";
          db.query(rankOrderQuery, [userRank], (err, rankResults) => {
            if (err) {
              console.error("Error fetching rank data:", err);
              res.status(500).send("Failed to fetch rank data");
              logErr(`ランクデータを取得できませんでした。エラー内容:${err} - categories`);
              return;
            }

            // rankテーブルのデータをcategoriesに追加
            rankResults.reverse().forEach((rank) => {
              categories.push({
                id: categories.length + 1,
                pack_category: rank.name,
                image_path: `${baseURL}${rank.back_img}`,
              });
            });

            res.status(200).json(categories);
            logAdminPut(`カテゴリー一覧を取得できました。 - categories`);
            logUserPut(`カテゴリー一覧を取得できました。 - categories`);
          });
        } else {
          res.status(200).json(categories);
          logAdminPut(`カテゴリー一覧を取得できました。 - categories`);
          logUserPut(`カテゴリー一覧を取得できました。 - categories`);
        }
      });
    } else {
      res.status(200).json(categories);
      logAdminPut(`カテゴリー一覧を取得できました。 - categories`);
      logUserPut(`カテゴリー一覧を取得できました。 - categories`);
    }
  });
});

module.exports = router;
