const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.get("/", authenticateJWT, async (req, res) => {
  const query = "SELECT * FROM pack_info"; // 查询所有抽卡包信息

  db.query(query, async (err, results) => {
    if (err) {
      console.error("获取抽卡包时出错:", err); // 如果查询失败，记录错误日志
      res.status(500).send("获取抽卡包失败"); // 返回服务器错误响应
      return;
    }

    // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
    const baseURL = ``; // 定义基础 URL（当前为空）

    try {
      const updatedResults = await Promise.all(
          results.map(async (pack) => {
            const itemsQuery = "SELECT * FROM items WHERE packId = ? AND (category = 'itemsS' OR category = 'itemsA')"; // 查询与抽卡包关联的 S 奖和 A 奖物品
            const specialItems = await new Promise((resolve, reject) => {
              db.query(itemsQuery, [pack.id], (err, items) => {
                if (err) {
                  console.error("获取抽卡包的物品失败:", pack.id, err); // 如果查询失败，记录错误日志
                  logErr(`无法获取抽卡包。错误内容:${err} - admin-get-packs`);
                  reject(err); // 抛出错误
                } else {
                  resolve(items || []); // 返回查询结果或空数组
                }
              });
            });

            // 将物品的图片路径拼接完整的 URL
            const specialItemImage = specialItems.map((item) => ({
              ...item,
              image: baseURL + item.image,
            }));

            // 返回更新后的抽卡包信息，包括图片路径和特殊物品列表
            return { ...pack, image: baseURL + pack.image, specialItems: specialItemImage };
          })
      );

      res.status(200).json(updatedResults); // 返回成功响应，包含更新后的抽卡包数据
      logAdminPut("已获取抽卡包列表。 - admin-get-packs"); // 记录日志：成功获取抽卡包列表
    } catch (error) {
      console.error("处理抽卡包和物品时出错:", error); // 捕获并记录处理过程中的错误
      logErr(`无法获取抽卡包。错误内容:${error} - admin-get-packs`); // 记录错误日志
      res.status(500).send("处理抽卡包和物品失败"); // 返回服务器错误响应
    }
  });
});

router.get("/category/:category", async (req, res) => {
  const { category } = req.params;
  const query = "SELECT * FROM pack_info WHERE pack_category LIKE ?";

  db.query(query, [`%${category}%`], async (err, results) => {
    if (err) {
      console.error("Error fetching packs by category:", err);
      res.status(500).send("Failed to fetch packs by category");
      return;
    }

    const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;

    try {
      const updatedResults = await Promise.all(
        results.map(async (pack) => {
          const itemsQuery = "SELECT * FROM items WHERE packId = ? AND (category = 'itemsS' OR category = 'itemsA')";
          const specialItems = await new Promise((resolve, reject) => {
            db.query(itemsQuery, [pack.id], (err, items) => {
              if (err) {
                console.error("Failed to fetch items for pack:", pack.id, err);
                reject(err);
              } else {
                resolve(items || []);
              }
            });
          });

          const specialItemImage = specialItems.map((item) => ({
            ...item,
            image: baseURL + item.image,
          }));

          return { ...pack, image: baseURL + pack.image, specialItems: specialItemImage };
        })
      );

      logAdminPut("パック一覧を取得しました。 - admin-get-packs");
      res.status(200).json(updatedResults);
    } catch (error) {
      console.error("Error processing packs and items:", error);
      logErr(`パックを取得できませんでした。エラー内容:${error} - admin-get-packs`);
      res.status(500).send("Failed to process packs and items");
    }
  });
});

module.exports = router;
