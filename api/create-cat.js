// const express = require("express");
// const router = express.Router();
// const multer = require("multer");
// const db = require("../db/db");
// const authenticateJWT = require("../auth/admin-auth");
// const { logAdminPut, logErr } = require("../logger");
//
// // Multer设置
// const storage = multer.diskStorage({
//   destination: function (req, file, cb) {
//     // 图片的保存目录
//     cb(null, "uploads/");
//   },
//   filename: function (req, file, cb) {
//     // 图片保存时的文件名
//     cb(null, `${Date.now()}-${file.originalname}`);
//   },
// });
//
// // 将 multer 的 storage 属性赋值给 upload 对象
// const upload = multer({ storage: storage });
//
// router.post("/", authenticateJWT, upload.single("categoryFile"), async (req, res) => {
//   const packCat = req.body["pack_cat"]; // 类别名称
//   const imagePath = req.file ? req.file.path : null; // 图片路径
//
//   db.query("INSERT INTO pack_category (pack_category, image_path) VALUES (?, ?)", [packCat, imagePath], (err, result) => {
//     if (err) {
//       console.error("数据库插入错误:", err);
//       res.status(500).send("插入 pack_category 失败");
//       logErr(`无法创建类别。错误内容:${err} - create-cat`);
//       return;
//     }
//
//     logAdminPut(`类别已成功创建。 - create-cat`);
//     res.status(201).send("类别添加成功");
//   });
// });
//
// module.exports = router;

const express = require("express");
const router = express.Router();
const multer = require("multer");
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");
const AWS = require("aws-sdk");

// 配置 AWS S3
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
  region: process.env.AWS_REGION || "us-east-1", // S3 区域
});

// const s3 = new AWS.S3({
//   accessKeyId: "********************", // 从环境变量中读取 Access Key
//   secretAccessKey: "JTlaqRiZiZyDcr3rNdFPMqCwpI9OMSl5isK2gOPd", // 从环境变量中读取 Secret Key
//   region: "us-east-1", // S3 区域
// });

// Multer 配置（仅用于内存存储）
const upload = multer({
  storage: multer.memoryStorage(), // 将文件存储在内存中，而不是磁盘
  limits: { fileSize: 5 * 1024 * 1024 }, // 限制文件大小为 5MB
  fileFilter: (req, file, cb) => {
    const allowedTypes = ["image/jpeg", "image/png", "image/gif"];
    if (allowedTypes.includes(file.mimetype)) {
      cb(null, true); // 允许上传
    } else {
      cb(new Error("Unsupported file type")); // 拒绝上传
    }
  },
});

// 定义路由
router.post("/", authenticateJWT, upload.single("categoryFile"), async (req, res) => {
  const packCat = req.body["pack_cat"]; // 类别名称
  const file = req.file; // 上传的文件

  try {
    let fileUrl = null;

    if (file) {
      // 上传文件到 S3
      const params = {
        // Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
        // Bucket: "pokemonbucket01", // S3 存储桶名称
        Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
        Key: `${Date.now()}-${file.originalname}`, // 文件名
        Body: file.buffer, // 文件内容
        ContentType: file.mimetype, // 文件类型
      };

      const result = await s3.upload(params).promise(); // 上传文件
      fileUrl = result.Location; // 获取文件的公共访问 URL
    }

    // 插入数据库记录
    db.query(
        "INSERT INTO pack_category (pack_category, image_path) VALUES (?, ?)",
        [packCat, fileUrl],
        (err, result) => {
          if (err) {
            console.error("Database insertion error:", err);
            res.status(500).send("Failed to insert pack_category");
            logErr(`无法创建类别。错误内容:${err} - create-cat`);
            return;
          }

          logAdminPut(`类别已成功创建。 - create-cat`);
          res.status(201).send("Category added successfully");
        }
    );
  } catch (error) {
    console.error("Failed to upload file or insert data:", error);
    logErr(`无法创建类别。错误内容:${error} - create-cat`);
    res.status(500).send("Server error");
  }
});

module.exports = router;
