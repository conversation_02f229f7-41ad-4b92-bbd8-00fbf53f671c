const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/auth");
const { logAdminPut, logErr, logUserPut } = require("../logger");

router.get("/", authenticateJWT, (req, res) => {
  const userId = req.user.userId;
  const { status } = req.query;

  let query = `
    SELECT 
      ui.*, 
      (
        SELECT ss.status 
        FROM shipping_statuses ss 
        WHERE ss.user_item_id = ui.id 
        ORDER BY ss.updated_at DESC 
        LIMIT 1
      ) AS shipping_status, 
      u.address AS user_address 
    FROM 
      user_items ui 
    LEFT JOIN 
      user u ON ui.user_id = u.id 
    WHERE 
      ui.user_id = ?`;

  // 配送依頼も配送もしていないアイテムについてはquantityが0より大きいアイテムのみ取得
  if (status === "pending") {
    query += ` AND ui.quantity > 0`;
  }

  if (status && status !== "all") {
    if (status === "pending") {
      query += ` AND (
        SELECT ss.status 
        FROM shipping_statuses ss 
        WHERE ss.user_item_id = ui.id 
        ORDER BY ss.updated_at DESC 
        LIMIT 1
      ) IS NULL`;
    } else if (status === "accepted") {
      query += ` AND (
        SELECT ss.status 
        FROM shipping_statuses ss 
        WHERE ss.user_item_id = ui.id 
        ORDER BY ss.updated_at DESC 
        LIMIT 1
      ) = '${status}'`;
    } else {
      query += ` AND (
        SELECT ss.status 
        FROM shipping_statuses ss 
        WHERE ss.user_item_id = ui.id 
        ORDER BY ss.updated_at DESC 
        LIMIT 1
      ) = '${status}'`;
    }
  }

  db.query(query, [userId], (err, results) => {
    if (err) {
      logErr(`ユーザー[${userId}]の所持アイテムが取得できませんでした。エラー内容:${err} - user-items`);
      return res.status(500).json({ message: "A database error has occurred." });
    }

    res.status(200).json(results);
    logUserPut(`ユーザー[${userId}]の所持アイテムが取得できました。 - user-items`);
  });
});

module.exports = router;
