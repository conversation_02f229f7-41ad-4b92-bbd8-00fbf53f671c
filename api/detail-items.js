const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

router.get("/:packId", (req, res) => {
  const { packId } = req.params;
  const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}`;

  console.log(`Fetching items for packId: ${packId}`);

  const query = "SELECT * FROM items WHERE packId = ?";
  db.query(query, [packId], (err, results) => {
    if (err) {
      console.error("Error fetching items:", err);
      res.status(500).send("Server error");
      logErr(`該当のアイテム詳細[${packId}]を取得できませんでした。エラー内容:${err} - detail-items`);
      return;
    }

    console.log("Query result:", results);

    const items = results.map((item) => {
      let imageUrl = item.image;
      if (item.category === "itemsS" || item.category === "itemsA" || item.category === "itemsB" || item.category === "last_one" || item.category === "roundNum") {
        imageUrl = `${baseURL}/${item.image}`;
      }
      return {
        ...item,
        image: imageUrl,
      };
    });

    logUserPut(`該当のアイテム詳細[${packId}]を取得できました。 - detail-items`);
    res.json(items);
  });
});

module.exports = router;
