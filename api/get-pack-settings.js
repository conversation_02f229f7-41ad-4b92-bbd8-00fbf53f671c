const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const { logErr, logUserPut } = require("../logger");

// 設定を取得するエンドポイント
router.get("/", async (req, res) => {
  try {
    const settings = await new Promise((resolve, reject) => {
      db.query("SELECT * FROM pack_settings WHERE id = 1", (err, results) => {
        if (err) {
          logErr(`ガチャの設定を取得できませんでした。エラー内容:${err} - get-pack-settings`);
          return reject(err);
        }
        resolve(results[0]);
      });
    });

    if (!settings) {
      return res.status(404).json({ message: "Settings not found" });
    }

    const baseURL = ``;
    // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;

    const settingsWithAbsoluteURLs = {
      ...settings,
      one_draw_button_image: settings.one_draw_button_image ? baseURL + settings.one_draw_button_image : null,
      ten_draw_button_image: settings.ten_draw_button_image ? baseURL + settings.ten_draw_button_image : null,
      hundred_draw_button_image: settings.hundred_draw_button_image ? baseURL + settings.hundred_draw_button_image : null,
      logo_image: settings.logo_image ? baseURL + settings.logo_image : null,
      pack_list_background: settings.pack_list_background ? baseURL + settings.pack_list_background : null,
      favicon_image: settings.favicon_image ? baseURL + settings.favicon_image : null,
      meta_og_image: settings.meta_og_image ? baseURL + settings.meta_og_image : null,
      rank_detail_image: settings.rank_detail_image ? baseURL + settings.rank_detail_image : null,
      header_point_icon: settings.header_point_icon ? baseURL + settings.header_point_icon : null,
      header_gp_icon: settings.header_gp_icon ? baseURL + settings.header_gp_icon : null,
      category_slider_left_arrow: settings.category_slider_left_arrow ? baseURL + settings.category_slider_left_arrow : null,
      category_slider_right_arrow: settings.category_slider_right_arrow ? baseURL + settings.category_slider_right_arrow : null,
      pack_item_point_icon: settings.pack_item_point_icon ? baseURL + settings.pack_item_point_icon : null,
      pack_draw_button_icon: settings.pack_draw_button_icon ? baseURL + settings.pack_draw_button_icon : null,
      detail_item_background: settings.detail_item_background ? baseURL + settings.detail_item_background : null,
      detail_item_title_image_s: settings.detail_item_title_image_s ? baseURL + settings.detail_item_title_image_s : null,
      detail_item_title_image_a: settings.detail_item_title_image_a ? baseURL + settings.detail_item_title_image_a : null,
      detail_item_title_image_b: settings.detail_item_title_image_b ? baseURL + settings.detail_item_title_image_b : null,
      detail_item_title_image_c: settings.detail_item_title_image_c ? baseURL + settings.detail_item_title_image_c : null,
      detail_item_title_image_round_num: settings.detail_item_title_image_round_num ? baseURL + settings.detail_item_title_image_round_num : null,
      detail_item_title_image_last_one: settings.detail_item_title_image_last_one ? baseURL + settings.detail_item_title_image_last_one : null,
    };

    res.json(settingsWithAbsoluteURLs);
    logUserPut(`ガチャの設定を取得できました。 - get-pack-settings`);
  } catch (error) {
    console.error("Failed to fetch settings:", error);
    res.status(500).json({ message: "Failed to fetch settings" });
    logErr(`ガチャの設定を取得できませんでした。エラー内容:${error} - get-pack-settings`);
  }
});

module.exports = router;
