const express = require("express");
const router = express.Router();
const multer = require("multer");
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");
const AWS = require("aws-sdk");

// 配置 AWS S3
const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || "us-east-1",
});

// Multer 配置（仅用于内存存储）
const upload = multer({
    storage: multer.memoryStorage(), // 将文件存储在内存中，而不是磁盘
    limits: { fileSize: 5 * 1024 * 1024 }, // 限制文件大小为 5MB
    fileFilter: (req, file, cb) => {
        const allowedTypes = ["image/jpeg", "image/png", "image/gif"];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true); // 允许上传
        } else {
            cb(new Error("Unsupported file type")); // 拒绝上传
        }
    },
});

// 定义路由
router.post("/", authenticateJWT, upload.single("card_image"), async (req, res) => {
    const value = req.body.value; // 卡牌价值
    const drawnAt = req.body.drawn_at; // 抽取时间
    const file = req.file; // 上传的文件

    try {
        let fileUrl = null;

        // 如果有文件上传，则上传到 S3
        if (file) {
            const params = {
                Bucket: process.env.AWS_BUCKET_NAME,
                Key: `${Date.now()}-${file.originalname}`,
                Body: file.buffer,
                ContentType: file.mimetype,
            };

            const result = await s3.upload(params).promise();
            fileUrl = result.Location; // 获取文件的公共访问 URL
        }

        // 插入数据库记录
        db.query(
            "INSERT INTO recent_pulled_cards_config (card_image, value, drawn_at) VALUES (?, ?, ?)",
            [fileUrl, value, drawnAt],
            (err, result) => {
                if (err) {
                    console.error("Database insertion error:", err);
                    logErr(`无法创建最新抽出卡牌记录。错误内容:${err} - create-pulled-card`);
                    return res.status(500).send("Failed to insert pulled card record");
                }

                logAdminPut(`最新抽出卡牌记录已成功创建。 - create-pulled-card`);
                res.status(201).send("Pulled card added successfully");
            }
        );
    } catch (error) {
        console.error("Failed to upload file or insert data:", error);
        logErr(`无法创建最新抽出卡牌记录。错误内容:${error} - create-pulled-card`);
        res.status(500).send("Server error");
    }
});

module.exports = router;
