const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.post("/", authenticateJWT, (req, res) => {
  const { removeNumber } = req.body;

  if (!removeNumber) {
    res.status(400).send("removeNumber is required");
    return;
  }

  db.query("DELETE FROM pack_video WHERE id = ?", [removeNumber], (err, results) => {
    if (err) {
      console.error("Error deleting record:", err);
      res.status(500).send("Failed to delete record");
      logErr(`該当の演出動画[ID:${removeNumber}]を削除できませんでした。エラー内容:${err} - admin-video-remove`);
      return;
    }

    if (results.affectedRows === 0) {
      res.status(404).send("No record found with the given ID");
      logAdminPut(`該当の演出動画[ID:${removeNumber}]を削除できました。 - admin-video-remove`);
      return;
    }

    res.status(200).json({ message: "Record deleted successfully" });
  });
});

module.exports = router;
