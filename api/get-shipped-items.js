const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const {logAdminPut, logErr} = require("../logger");

router.get("/", authenticateJWT, (req, res) => {
    const query = `
        SELECT ss.*,
               COALESCE(ui.name, ge.name)         AS item_name,
               COALESCE(ui.category, ge.category) AS category,
               COALESCE(ui.price, ge.gp)          AS price,
               COALESCE(ui.image, ge.image)       AS image,
               u.mail
        FROM shipping_statuses ss
                 LEFT JOIN
             user_items ui ON ss.user_item_id = ui.id
                 LEFT JOIN
             gp_exchanges ge ON ss.user_gp_item_id = ge.id
                 JOIN
             user u ON ss.user_id = u.id
        WHERE ss.status = 'shipped'`;

    db.query(query, (err, results) => {
        if (err) {
            console.error("Error fetching shipping statuses:", err);
            logErr(`无法获取已配送物品的信息。错误内容: ${err} - get-shipped-items`);
            return res.status(500).json({message: "Database error"});
        }

        res.status(200).json(results);
        logAdminPut(`已成功获取已配送物品的信息。 - get-shipped-items`);
    });
});

module.exports = router;
