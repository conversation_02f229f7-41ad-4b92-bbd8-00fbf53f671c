const express = require("express");
const router = express.Router();
const db = require("../db/db");
const { logAdminPut, logErr } = require("../logger");

router.get("/", (req, res) => {
  db.query("SELECT * FROM pack_type", (err, results) => {
    if (err) {
      console.error("Error fetching categories:", err);
      res.status(500).send("Failed to fetch categories");
      logErr(`種別の取得ができませんでした。エラー内容:${err} - get-types`);
      return;
    }

    res.status(200).json(results);
    logAdminPut(`種別の取得ができました。 - get-types`);
  });
});

module.exports = router;
