const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const { logErr, logUserPut } = require("../logger");

router.get("/:packId", async (req, res) => {
  const { packId } = req.params;

  try {
    const history = await new Promise((resolve, reject) => {
      db.query(
        `
        SELECT 
          pack_draw_history.id,
          pack_draw_history.pack_info_id,
          pack_draw_history.item_id,
          pack_draw_history.draw_time,
          items.category,
          items.name,
          items.price,
          items.image
        FROM pack_draw_history
        JOIN items ON pack_draw_history.item_id = items.id
        JOIN pack_info ON pack_draw_history.pack_info_id = pack_info.id
        WHERE pack_draw_history.pack_info_id = ?
        AND pack_info.status = 'sold-out'
      `,
        [packId],
        (err, results) => {
          if (err) {
            logErr(`排出履歴を取得できませんでした。エラー内容:${err} - draw-history`);
            return reject(err);
          }
          resolve(results);
        }
      );
    });

    const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
    const modifiedHistory = history.map((item) => {
      return {
        ...item,
        image: item.category === "C" ? item.image : `${baseURL}${item.image}`,
      };
    });

    res.json(modifiedHistory);
    logUserPut(`排出履歴を取得できました。 - draw-history`);
  } catch (error) {
    console.error("Error fetching draw history:", error);
    res.status(500).json({ message: "Failed to fetch draw history" });
    logErr(`排出履歴を取得できませんでした。エラー内容:${error} - draw-history`);
  }
});

module.exports = router;
