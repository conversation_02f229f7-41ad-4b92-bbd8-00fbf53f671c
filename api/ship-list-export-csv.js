const express = require("express");
const fs = require("fs");
const path = require("path");
const router = express.Router();
const authenticateJWT = require("../auth/admin-auth");
const { createObjectCsvStringifier } = require("csv-writer");
const formatDayFn = require("../util/formatDay");
const createTransporter = require("../service/mailService");
const AWS = require("aws-sdk");
const multer = require("multer");


const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
  region: process.env.AWS_REGION || "us-east-1", // S3 区域
});


router.post("/", authenticateJWT, async (req, res) => {
  const { items, email } = req.body;
  console.log("被调用了！");

  if (!items || items.length === 0) {
    console.log("未提供物品信息");
    return res.status(400).json({ message: "未提供物品信息" });
  }

  // 创建 CSV 字符串化器
  const csvStringifier = createObjectCsvStringifier({
    header: [
      { id: "update_date", title: "Update Date" },
      { id: "user_id", title: "User ID" },
      { id: "item_name", title: "Item Name" },
      { id: "rarely", title: "Rarity" },
      { id: "item_image", title: "Image URL" },
      { id: "item_point", title: "Points" },
      { id: "user_name_kanji", title: "Name" },
      // { id: "user_name_kana", title: "Name (Kana)" },
      { id: "zip_code", title: "Postal Code" },
      { id: "address", title: "Address" },
      { id: "user_tel", title: "Phone Number" },
    ],
  });

  // 将物品数据映射为 CSV 格式
  const records = items.map((item) => ({
    update_date: formatDayFn.formatDayFn(item.updated_at) || "",
    user_id: item.user_id || "",
    item_name: item.item_name || "",
    rarely: item.category || "",
    item_image: item.image || "",
    item_point: item.price || "",
    user_name_kanji: item.name || "",
    // user_name_kana: item.kana || "",
    zip_code: item.zip_code || "",
    address: item.pref + item.address || "",
    user_tel: item.tel || "",
  }));

  // 生成 CSV 数据
  const csvData = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(records);

  try {
    // 文件上传到 S3
    const params = {
      Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
      Key: `export-${Date.now()}.csv`, // 文件名（带时间戳避免重复）
      Body: csvData, // CSV 数据内容
      ContentType: "text/csv", // 文件类型
    };

    const s3Result = await s3.upload(params).promise();
    const csvFileUrl = s3Result.Location; // 获取文件的公共访问 URL

    // 邮件发送配置
    const transporter = createTransporter();

    const mailOptions = {
      from: process.env.SMTP_USER,
      to: process.env.SMTP_ADMIN_SEND_MAIL,
      subject: "配送任务的 CSV 文件已发送。",
      text: "配送任务的 CSV 文件已作为附件发送。",
      attachments: [
        {
          filename: "export.csv",
          content: csvData, // 直接使用 CSV 数据作为附件内容
        },
      ],
    };

    // 发送邮件
    await transporter.sendMail(mailOptions);

    res.status(200).json({ message: "CSV 文件已通过邮件发送并上传至 S3。", csvFileUrl });
    console.log("+++ 邮件发送成功 +++");
  } catch (error) {
    // 错误处理
    res.status(500).json({ message: error.message });
    console.log("--- 邮件发送失败 ---");
  }
});

module.exports = router;
