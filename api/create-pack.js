const express = require("express");
const router = express.Router();
const multer = require("multer");
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

// Multer設定
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, "uploads/");
  },
  filename: function (req, file, cb) {
    cb(null, `${Date.now()}-${file.originalname}`);
  },
});

// uploadにmulterのstorageプロパティを代入したオブジェクトを定義
const upload = multer({ storage: storage });

// パック作成のルーティング
router.post("/", authenticateJWT, upload.any(), async (req, res) => {
  console.log("Files:", req.files);
  console.log("Body:", req.body);

  const cItems = JSON.parse(req.body.cItems);
  const videoIds = JSON.parse(req.body.videos);

  const packThumbnailFile = req.files.find((file) => file.fieldname === "pack_thumbnail");
  const packThumbnailFilePath = packThumbnailFile ? packThumbnailFile.path : null;

  const packName = req.body["pack_name"];
  const packPoint = req.body["pack_point"];
  const packReduction = req.body["pack_reduction"];
  const packRest = req.body["pack_rest"];
  const packAllRest = req.body["pack_all_rest"];
  const packCat = req.body["pack_cat"];
  const dayOnly = req.body["day_only"] === "true";

  const rankOrder = req.body["pack_rank"];

  try {
    // パックの基本情報登録
    const packResult = await new Promise((resolve, reject) => {
      db.query("INSERT INTO pack_info (name, point, image, reduction, rest, pack_category, allRest, status, day_only,`rank`) VALUES (?,?,?,?,?,?,?,?,?,?)", [packName, packPoint, packThumbnailFilePath, packReduction, packRest, packCat, packAllRest, "invalid", dayOnly, rankOrder === "" ? null : rankOrder], (err, result) => {
        if (err) {
          reject(err);
          logErr(`パックの基本情報の作成ができませんでした。エラー内容:${err} - create-pack`);
        } else {
          resolve(result.insertId);
        }
      });
    });

    // 通常のアイテム処理
    const itemInsertions = req.files
      // フィールド名が itemsS, itemsA, itemsB で始まるファイルのみ処理
      .filter((file) => /^items[ASB]_\d+_imgFile$/.test(file.fieldname))
      .map((file) => {
        // "imgFile"は無視
        const [itemPrefix, itemId, _] = file.fieldname.split("_");
        const itemName = req.body[`${itemPrefix}_${itemId}_name`];
        const itemPrice = req.body[`${itemPrefix}_${itemId}_price`];
        const imagePath = file.path;
        const itemRest = req.body[`${itemPrefix}_${itemId}_rest`];
        const itemType = req.body[`${itemPrefix}_${itemId}_type`];

        return db.query("INSERT INTO items (category, name, price, image, rest, packId, type) VALUES (?,?,?,?,?,?,?)", [itemPrefix, itemName, itemPrice, imagePath, itemRest, packResult, itemType]);
      });

    // C賞アイテム処理
    const cItemInsertions = cItems.map((item) => {
      // fieldnameを分割して、C賞アイテム用のファイルがアップロードされているか確認する
      const uploadedCItemFile = req.files.find((file) => {
        const [itemPrefix, itemId, _] = file.fieldname.split("_");
        return itemPrefix === "itemsC" && itemId === String(item.id);
      });

      const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;

      // ファイルがアップロードされていればそのパスを、なければ元のimgFileを使用する
      const itemImagePath = uploadedCItemFile ? baseURL + uploadedCItemFile.path : item.imgFile;

      return db.query("INSERT INTO items (category, name, price, image, rest, packId, is_exchange_only) VALUES (?, ?, ?, ?, ?, ?, ?)", ["C", item.nameValue, item.priceValue, itemImagePath, item.restValue, packResult, item.exchange_only]);
    });

    // ラストワン賞の処理
    const lastOneItemName = req.body["last_one_name"];
    const lastOneItemThumbnailFile = req.files.find((file) => file.fieldname === "last_one_img");
    const lastOneItemThumbnailFilePath = lastOneItemThumbnailFile ? lastOneItemThumbnailFile.path : null;
    const lastOneItemPrice = req.body["last_one_price"];
    const lastOneItemType = req.body["last_one_type"];

    if (lastOneItemName && lastOneItemThumbnailFilePath && lastOneItemPrice) {
      const addLastOneItem = await new Promise((resolve, reject) => {
        db.query("INSERT INTO items (category, name, price, image, rest, packId, type) VALUES (?, ?, ?, ?, ?, ?, ?)", ["last_one", lastOneItemName, lastOneItemPrice, lastOneItemThumbnailFilePath, 1, packResult, lastOneItemType], (err, result) => {
          if (err) {
            reject(err);
            logErr(`ラストワン賞の登録ができませんでした。エラー内容:${err} - create-pack`);
          } else {
            resolve(result);
          }
        });
      });
    }

    // バンキリ賞アイテム処理
    const roundNumItemInsertions = req.files
      .filter((file) => /^itemsRoundNum_\d+_imgFile$/.test(file.fieldname))
      .map((file) => {
        const [ignore, itemId, anotherIgnore] = file.fieldname.split("_"); // "imgFile"は無視
        const itemName = req.body[`itemsRoundNum_${itemId}_name`];
        const itemPrice = req.body[`itemsRoundNum_${itemId}_price`];
        const itemRoundNum = req.body[`itemsRoundNum_${itemId}_roundNum`];
        const itemRest = req.body[`itemsRoundNum_${itemId}_rest`];
        const itemType = req.body[`itemsRoundNum_${itemId}_type`];
        const roundNumFilePath = file.path;

        return db.query("INSERT INTO items (category, name, price, image, rest, packId, round_num, type) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", ["roundNum", itemName, itemPrice, roundNumFilePath, itemRest, packResult, itemRoundNum, itemType]);
      });

    // ビデオ情報の処理
    const videoInsertions = Object.keys(videoIds).flatMap((rank) => {
      return videoIds[rank].map((id) => {
        console.log("Video ID:", id);
        return new Promise((resolve, reject) => {
          db.query("SELECT * FROM pack_video WHERE id = ?", [id], (err, results) => {
            if (err) {
              logErr(`ビデオ情報の登録ができませんでした。エラー内容:${err} - create-pack`);
              return reject(err);
            }
            if (results.length > 0) {
              const packVideo = results[0];
              db.query("INSERT INTO pack_draw_videos (video_path, pack_id, `rank`, video_id) VALUES (?, ?, ?, ?)", [packVideo.video_path, packResult, rank, id], (err, result) => {
                if (err) {
                  reject(err);
                  logErr(`ビデオ情報の登録ができませんでした。エラー内容:${err} - create-pack`);
                } else {
                  resolve(result);
                }
              });
            } else {
              resolve();
            }
          });
        });
      });
    });

    await Promise.all([...itemInsertions, ...cItemInsertions, ...roundNumItemInsertions, ...videoInsertions]);
    res.send("All items, videos, and pack info added successfully");
    logAdminPut(`新しいガチャを作成しました。 - create-pack`);
  } catch (error) {
    console.error("Failed to insert pack and items:", error);
    res.status(500).send("Failed to insert pack and items");
    logErr(`新しいガチャの登録ができませんでした。エラー内容:${error} - create-pack`);
  }
});

module.exports = router;
