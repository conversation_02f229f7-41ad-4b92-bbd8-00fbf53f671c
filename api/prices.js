const express = require("express");
const router = express.Router();
const Stripe = require("stripe");
const stripe = Stripe("sk_test_51PGxnhDcuh7cCxa5GC7akawBC62iSqhLQcfyEMnikLgaW0sj6sCtAxkv5kcTRLJ23idSXLNppXgFq2xKQd0VaVfo00JqN7cD7R");
const { logUserPut, logErr } = require("../logger");

router.get("/", async (req, res) => {
  try {
    const prices = await stripe.prices.list({
      expand: ["data.product"],
    });

    // アーカイブされていない価格のみをフィルタリング
    const activePrices = prices.data.filter((price) => price.product.active);

    res.json(activePrices);
    logUserPut(`ストライプのアイテムを取得できました。 - prices`);
  } catch (error) {
    res.status(500).json({ error: error.message });
    logErr(`ストライプのアイテムを取得できませんでした。エラー内容:${error} - prices`);
  }
});

module.exports = router;
