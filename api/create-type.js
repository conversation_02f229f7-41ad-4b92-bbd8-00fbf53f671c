const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.post("/", authenticateJWT, async (req, res) => {
  const packType = req.body["pack_type"];

  db.query("INSERT INTO pack_type (pack_type) VALUES (?)", [packType], (err, result) => {
    if (err) {
      console.error("Database insertion error:", err);
      res.status(500).send("Failed to insert pack_category");
      logErr(`種別について作成できませんでした。エラー内容:${err} - create-type`);
      return;
    }

    logAdminPut(`種別について作成できました。 - create-type`);
    res.status(201).send("Category added successfully");
  });
});

module.exports = router;
