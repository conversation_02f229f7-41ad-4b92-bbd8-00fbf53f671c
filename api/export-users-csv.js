const express = require("express");
const router = express.Router();
const db = require("../db/db");
const csvWriter = require("csv-writer").createObjectCsvStringifier;
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.get("/", authenticateJWT, (req, res) => {
  const query = "SELECT * FROM user";

  db.query(query, (err, results) => {
    if (err) {
      logErr(`ユーザー一覧のCSV出力ができませんでした。エラー内容:${err} - export-users-csv`);
      return res.status(500).json({ message: "データベースエラーが発生しました。" });
    }

    const csvStringifier = csvWriter({
      header: [
        { id: "id", title: "ID" },
        { id: "name", title: "Name" },
        { id: "kana", title: "Name (Kana)" },
        { id: "tel", title: "Phone Number" },
        { id: "mail", title: "Email Address" },
        { id: "zip_code", title: "Postal Code" },
        { id: "pref", title: "Prefecture" },
        { id: "address", title: "Address" },
        { id: "point", title: "Owned Points" },
        { id: "day_point", title: "Daily Points" },
        { id: "weekly_point", title: "Weekly Points" },
        { id: "month_point", title: "Monthly Points" },
        { id: "allPoint", title: "Total Points" },
        { id: "gp", title: "Owned GP" },
      ],
    });

    const csvData = csvStringifier.getHeaderString() + csvStringifier.stringifyRecords(results);

    res.setHeader("Content-Type", "text/csv");
    res.setHeader("Content-Disposition", "attachment; filename=users.csv");
    res.send(csvData);
    logAdminPut(`ユーザー一覧のCSV出力ができました。 - export-users-csv`);
  });
});

module.exports = router;
