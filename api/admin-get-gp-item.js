const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.get("/", authenticateJWT, async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = 10;
  const offset = (page - 1) * limit;
  // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}`;
  const baseURL = ``;

  try {
    const results = await new Promise((resolve, reject) => {
      db.query("SELECT * FROM gp_items LIMIT ? OFFSET ?", [limit, offset], (err, results) => {
        if (err) {
          logErr(`无法获取 GP 项目。错误内容: ${err} - admin-get-gp-item`);
          return reject(err);
        }
        resolve(results);
        logAdminPut(`已成功获取 GP 项目。 - admin-get-gp-item`);
      });
    });

    const itemsWithAbsoluteImageURL = results.map((item) => ({
      ...item,
      image: baseURL + item.image,
    }));

    const totalItems = await new Promise((resolve, reject) => {
      db.query("SELECT COUNT(*) AS count FROM gp_items", (err, results) => {
        if (err) {
          logErr(`无法获取 GP 项目的总数。错误内容: ${err} - admin-get-gp-item`);
          return reject(err);
        }
        resolve(results[0].count);
        logAdminPut(`已成功获取 GP 项目的总数。 - admin-get-gp-item`);
      });
    });

    res.json({
      items: itemsWithAbsoluteImageURL,
      totalItems,
      totalPages: Math.ceil(totalItems / limit),
      currentPage: page,
    });
    logAdminPut(`已成功获取 GP 项目。 - admin-get-gp-item`);
  } catch (error) {
    console.error("Error fetching GP items:", error);
    logErr(`无法获取 GP 项目。错误内容: ${error} - admin-get-gp-item`);
    res.status(500).json({ message: "Failed to fetch GP items" });
  }
});

module.exports = router;
