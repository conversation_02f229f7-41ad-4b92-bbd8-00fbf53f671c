const express = require("express");
const router = express.Router();
const db = require("../db/db");
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
require("dotenv").config();
const { logAdminPut, logErr } = require("../logger");
const app = express();
const cookieParser = require("cookie-parser");
app.use(cookieParser());
router.use(cookieParser());

const secretKey = process.env.AUTH_ADMIN_JWT_SECRET_KEY;

router.post("/", (req, res) => {
  const { mail, pass } = req.body;

  // ユーザーの存在を確認
  const query = "SELECT * FROM admin_accounts WHERE mail = ?";

  db.query(query, [mail], (err, results) => {
    if (err) {
      logErr("データベースエラーが発生しました。管理者アカウントがログインできませんでした。 - adminLogin");
      return res.status(500).json({ message: "A database error has occurred." });
    }

    if (results.length === 0) {
      logErr("無効なメールアドレスまたはパスワードです。管理者アカウントがログインできませんでした。 - adminLogin");
      return res.status(401).json({ message: "Invalid email address or password." });
    }

    const user = results[0];

    // パスワードの確認
    bcrypt.compare(pass, user.pass, (err, isMatch) => {
      if (err) {
        logErr("パスワード検証中にエラーが発生しました。管理者アカウントがログインできませんでした。 - adminLogin");
        return res.status(500).json({ message: "An error occurred during password verification." });
      }

      if (!isMatch) {
        logErr("無効なメールアドレスまたはパスワードです。管理者アカウントがログインできませんでした。 - adminLogin");
        return res.status(401).json({ message: "Invalid email address or password." });
      }

      // JWTトークンの生成
      const token = jwt.sign({ userId: user.id, mail: user.mail }, secretKey, { expiresIn: "30d" });
      console.log("Generated Token:", token);

      // クッキーにトークンをセットしてレスポンスを返す
      // res.cookie("oripa-admin-token", token, {
      //   httpOnly: true,
      //   secure: process.env.NODE_ENV === "production",
      //   maxAge: 2592000000,
      // });

      res.cookie("oripa-admin-token", token, {
        httpOnly: true,  // 防 XSS
        secure: true,    // 仅 HTTPS
        sameSite: "None", // 跨域必需
        maxAge: 86400000 // 1天
      });

      res.status(200).json({ token });
      logAdminPut("管理员登录成功。 - adminLogin");
    });
  });
});

module.exports = router;
