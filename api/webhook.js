const express = require("express");
const router = express.Router();
const db = require("../db/db");
const Stripe = require("stripe");
require("dotenv").config();
const stripe = Stripe(process.env.STRIPE_SECRET_KEY);
const { logUserPut, logErr } = require("../logger");
const UserTransaction = require("../model/userTransaction");
const UserExtension = require("../model/userExtension");
// Stripe Webhook Secret
const endpointSecret = process.env.STRIPE_WEB_HOOK_SECRET_KEY;

router.post("/", express.raw({ type: "application/json" }), (req, res) => {
  const sig = req.headers["stripe-signature"];
  let event;
    console.log("Webhook event received");
    try {
    event = stripe.webhooks.constructEvent(req.body, sig, endpointSecret);
    console.log("Webhook event received:", event);
  } catch (err) {
    console.error("Webhook Error:", err.message);
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  if (event.type === "checkout.session.completed") {
    const session = event.data.object;
    const sessionId = event.data.object.id;
    const userId = session.client_reference_id;
    const pointAmount = session.metadata.pointAmount;
    const payAmount = session.metadata.payAmount;

    console.log("Processing payment for user", userId, "with points", pointAmount);

    // Ensure pointAmount is a number
    const pointsToAdd = Number(pointAmount) || 0;

    db.query(
      "SELECT point, day_point, weekly_point, month_point, allPoint FROM user WHERE id = ?",
      [userId],
      (err, results) => {
        if (err) {
          console.error("Error fetching user points:", err);
          return res.status(500).send("Error fetching user points");
        }

        if (results.length === 0) {
          console.error("User not found");
          return res.status(404).send("User not found");
        }

        const { point, day_point, weekly_point, month_point, allPoint } = results[0];

        const newPoints = point + pointsToAdd;
        const newDayPoints = day_point + pointsToAdd;
        const newWeeklyPoints = weekly_point + pointsToAdd;
        const newMonthPoints = month_point + pointsToAdd;
        const newAllPoints = allPoint + pointsToAdd;

        db.query(
          "UPDATE user SET point = ?, day_point = ?, weekly_point = ?, month_point = ?, allPoint = ? WHERE id = ?",
          [newPoints, newDayPoints, newWeeklyPoints, newMonthPoints, newAllPoints, userId],
          (err, updateResults) => {
            if (err) {
              console.error("Error updating user points:", err);
              return res.status(500).send("Error updating user points");
            }
            console.log(`User ${userId} points updated successfully.`);
            // 添加充值记录
            UserTransaction.add(
                null,
                userId,
                "recharge",
                payAmount,
                sessionId,
                "recharge success",
                newPoints // 将更新后的积分作为 currentBalance
            );
            res.status(200).send("User points updated");
          }
        );

        // 更新用户总充值金额和VIP等级
        // UserExtension.updateRechargeAndVipLevel(userId, payAmount);
      }
    );
  } else {
    res.status(400).send("Unhandled event type");
  }
});

module.exports = router;
