const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const multer = require("multer");
const authenticateJWT = require("../auth/auth");
const { logAdminPut, logErr } = require("../logger");
const AWS = require("aws-sdk");

const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION || "us-east-1",
});

// Multer 配置：内存存储
const upload = multer({
    storage: multer.memoryStorage(),
}).single("avatar"); // 只接受一个名为 "avatar" 的文件字段

// 上传头像接口
router.post("/", upload, async (req, res) => {
    // 后端接收
    const userId = req.body.userId;

    if (!userId) {
        return res.status(400).json({ message: "用户 ID 不存在，请确认登录状态。" });
    }

    const file = req.file;
    if (!file) {
        return res.status(400).json({ message: "未上传头像文件。" });
    }

    try {
        // 上传到 S3
        const params = {
            Bucket: process.env.AWS_BUCKET_NAME,
            Key: `avatars/${userId}-${Date.now()}-${file.originalname}`,
            Body: file.buffer,
            ContentType: file.mimetype,
        };

        const s3Result = await s3.upload(params).promise();
        const avatarUrl = s3Result.Location; // 获取头像 URL

        // 更新数据库中用户的头像路径
        const query = "UPDATE user SET user_image = ? WHERE id = ?";
        await new Promise((resolve, reject) => {
            db.query(query, [avatarUrl, userId], (err, results) => {
                if (err) {
                    logErr(`头像上传失败，数据库更新错误: ${err.message} - upload-avatar`);
                    console.error("数据库更新错误:", err);
                    return reject(err);
                }
                resolve(results);
            });
        });

        res.status(200).json({ message: "头像上传成功！", avatarUrl });
        logAdminPut(`用户 ID: ${userId} 头像上传成功。 - upload-avatar`);
    } catch (error) {
        console.error("头像上传失败:", error);
        logErr(`头像上传失败: ${error.message} - upload-avatar`);
        res.status(500).json({ message: "头像上传失败。", error: error.message });
    }
});

module.exports = router;
