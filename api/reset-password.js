const express = require("express");
const router = express.Router();
const bcrypt = require("bcrypt");
const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

router.post("/", async (req, res) => {
  const { token, newPassword } = req.body;

  try {
    const user = await new Promise((resolve, reject) => {
      db.query("SELECT * FROM user WHERE reset_password_token = ? AND reset_password_expires > ?", [token, Date.now()], (err, results) => {
        if (err) {
          logErr(`ユーザーのパスリトークンが選択できませんでした。エラー内容:${err} - reset-password`);
          return reject(err);
        }
        resolve(results[0]);
      });
    });

    if (!user) {
      return res.status(400).json({ message: "Invalid or expired token." });
    }

    const hashedPassword = await bcrypt.hash(newPassword, 10);

    await new Promise((resolve, reject) => {
      db.query("UPDATE user SET pass = ?, reset_password_token = NULL, reset_password_expires = NULL WHERE id = ?", [hashedPassword, user.id], (err, result) => {
        if (err) {
          logErr(`ユーザーのパスワードリセットが完了しませんでした。エラー内容:${err} - reset-password`);
          return reject(err);
        }
        resolve(result);
      });
    });

    // 該当のuserテーブルのidが同じのuserのreset_password_expiresを削除する処理
    await new Promise((resolve, reject) => {
      db.query("UPDATE user SET reset_password_expires = NULL WHERE id = ?", [user.id], (err, result) => {
        if (err) {
          logErr(`ユーザーのパスワードリセットのトークン削除ができませんでした。エラー内容:${err} - reset-password`);
          return reject(err);
        }
        resolve(result);
      });
    });

    res.status(200).json({ message: "Password has been reset." });
    logUserPut(`ユーザーのパスワードリセットが完了しました。 - reset-password`);
  } catch (error) {
    console.error("Error resetting password:", error);
    res.status(500).json({ message: "An error occurred while resetting the password." });
    logErr(`ユーザーのパスワードリセットが完了しませんでした。エラー内容:${error} - reset-password`);
  }
});

module.exports = router;
