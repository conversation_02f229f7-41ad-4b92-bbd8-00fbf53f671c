const express = require("express");
const router = express.Router();
const db = require("../db/db"); // 接続プール
const authenticateJWT = require("../auth/auth");
const { logUserPut, logErr, logPaymentPut } = require("../logger");
const { formatNumber } = require("../util/formatNumber");
const { withRetryTransaction } = require("../util/retry");
const userTransaction = require("../model/userTransaction");

router.post("/", authenticateJWT, async (req, res) => {
  const { items } = req.body;
  const userId = req.user.userId;
  let connection; // 接続を保持する変数

  try {
    // 接続プールから接続を取得
    connection = await new Promise((resolve, reject) => {
      db.getConnection((err, conn) => {
        if (err) return reject(err);
        resolve(conn);
      });
    });

    // トランザクションを開始
    await new Promise((resolve, reject) => {
      connection.beginTransaction((err) => {
        if (err) return reject(err);
        resolve();
      });
    });

    for (const item of items) {
      // アイテムの数量をチェック
      const checkQuantity = await new Promise((resolve, reject) => {
        connection.query("SELECT quantity FROM user_items WHERE id = ? AND user_id = ?", [item.userItemId, userId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]のアイテム[${item.userItemId}]の残数を確認中にエラーが発生しました。エラー内容:${err} - check-quantity`);
            return reject(err);
          }
          if (results.length === 0) {
            logErr(`ユーザー[${userId}]のアイテム[${item.userItemId}]は存在しません。`);
            return reject(new Error(`Item not found for user ${userId} and item ${item.userItemId}`));
          }
          resolve(results[0].quantity);
        });
      });

      if (checkQuantity <= 0) {
        logErr(`ユーザー[${userId}]のアイテム[${item.userItemId}]は既に交換済みです。`);
        // エラーレスポンスを返して処理を終了
        return res.status(400).json({
          message: `Item [${item.userItemId}] for user [${userId}] has already been exchanged. - item-result-point-change`,
        });
      }

      console.log("itemid" + item.userItemId);

      // user_itemsテーブルのuser_idカラムと一致するレコードの中に、item.idと一致するitem_idが存在するか確認
      const checkItem = await new Promise((resolve, reject) => {
        connection.query("SELECT * FROM user_items WHERE user_id = ? AND id = ? FOR UPDATE", [userId, item.userItemId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]のアイテム[${item.userItemId}]の交換処理中にエラーが発生しました。エラー内容:${err} - check-item`);
            return reject(err);
          }
          resolve(results);
        });
      });

      if (checkItem.length === 0) {
        logErr(`ユーザー[${userId}]のアイテム[${item.userItemId}]は存在しません。`);
        throw new Error(`Item not found for user ${userId} and item ${item.userItemId}`);
      }

      const checkShipOnly = checkItem.filter((item) => item.is_ship_only === 1);

      if (checkShipOnly.length > 0) {
        logErr(`ユーザー[${userId}]のアイテム[${item.id}]は配送専用アイテムです。 - item-result-point-change`);
        return res.status(400).json({
          message: `${item.name} is a shipping-only item.`,
        });
      }

      // ユーザーのポイントを確認
      const userPointPast = await new Promise((resolve, reject) => {
        connection.query("SELECT point FROM user WHERE id = ?", [userId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]のポイントを取得できませんでした。エラー内容:${err} - get-user-point`);
            return reject(err);
          }
          resolve(results[0].point);
        });
      });

      // アイテムのポイントをユーザーのポイントに追加
      await new Promise((resolve, reject) => {
        connection.query("UPDATE user SET point = point + ? WHERE id = ?", [item.price, userId], async (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]が交換したアイテムのポイントを交換できませんでした。エラー内容:${err} - item-point-change`);
            return reject(err);
          }
          logUserPut(`ユーザー[${userId}]がアイテム[${item.userItemId}]をポイント${item.price}に交換しました。`);
          try {
            const newUserPoint = userPointPast + item.price;
            await userTransaction.add(connection, userId, "exchange_points", item.price, item.userItemId, "exchange points success", newUserPoint);
            resolve(results);
          } catch (ex) {
            reject(ex);
          }
        });
      });

      // ユーザーのポイントを確認
      const userPointNew = await new Promise((resolve, reject) => {
        connection.query("SELECT point FROM user WHERE id = ?", [userId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]のポイントを取得できませんでした。エラー内容:${err} - get-user-point`);
            return reject(err);
          }
          resolve(results[0].point);
        });
      });

      // アイテムの数量を1つ減らす
      await new Promise((resolve, reject) => {
        connection.query("UPDATE user_items SET quantity = quantity - 1 WHERE id = ? AND user_id = ?", [item.userItemId, userId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]が交換したアイテムの残数を変更できませんでした。エラー内容:${err} - item-point-change`);
            return reject(err);
          }
          resolve(results);
        });
      });

      // アイテムの数量をチェック
      const checkQuantityNew = await new Promise((resolve, reject) => {
        connection.query("SELECT quantity FROM user_items WHERE id = ? AND user_id = ?", [item.userItemId, userId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]のアイテム[${item.userItemId}]の残数を確認中にエラーが発生しました。エラー内容:${err} - item-result-point-change`);
            return reject(err);
          }
          if (results.length === 0) {
            logErr(`ユーザー[${userId}]のアイテム[${item.userItemId}]は存在しません。`);
            return reject(new Error(`Item not found for user ${userId} and item ${item.userItemId}`));
          }
          resolve(results[0].quantity);
        });
      });

      // checkQuantityNew負の値じゃないかを確認
      if (checkQuantityNew < 0) {
        logErr(`ユーザー[${userId}]のアイテム[${item.userItemId}]の残数が負の値[${checkQuantityNew}]になりました。`);
        // エラーレスポンスを返して処理を終了
        return res.status(400).json({
          message: `Remaining quantity of item [${item.userItemId}] for user [${userId}] became negative. - item-result-point-change`,
        });
      }

      logPaymentPut(`結果発表画面でユーザー[${userId}]がアイテム[${item.userItemId}]をポイント[${formatNumber(item.price)}]に交換し、ユーザーのポイントは[${formatNumber(userPointPast)}]から[${formatNumber(userPointNew)}]になりました。アイテムの残りの数は[${checkQuantity}]から[${checkQuantityNew}]になりました。 - item-result-point-change`);
    }

    // トランザクションをコミット
    await new Promise((resolve, reject) => {
      connection.commit((err) => {
        if (err) {
          return connection.rollback(() => {
            reject(err);
            logPaymentPut(`ユーザー[${userId}]のアイテムポイント交換がロールバックされました。 - item-result-point-change`);
          });
        }
        resolve();
      });
    });

    res.status(200).json({ message: "Point exchange completed successfully." });
    logUserPut(`ユーザー[${userId}]のアイテムポイント交換が完了しました。 - item-result-point-change`);
  } catch (err) {
    if (connection) {
      // エラーが発生した場合はロールバック
      await new Promise((resolve, reject) => {
        connection.rollback(() => {
          reject(err);
        });
      });
    }
    res.status(500).json({ message: "Point exchange failed." });
    logErr(`ユーザー[${userId}]が交換したアイテムポイント交換に失敗しました。エラー内容:${err} - item-result-point-change`);
  } finally {
    if (connection) connection.release(); // 接続を解放
  }
});

module.exports = router;