const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logErr } = require("../logger");

router.get("/", authenticateJWT, (req, res) => {
  const searchTerm = req.query.term || "";
  const searchValue = `%${searchTerm}%`;

  const query = `
    SELECT 
      ss.*, 
      COALESCE(ui.name, ge.name) AS item_name, 
      COALESCE(ui.category, ge.category) AS category, 
      COALESCE(ui.price, ge.gp) AS price, 
      COALESCE(ui.image, ge.image) AS image, 
      u.mail 
    FROM 
      shipping_statuses ss
    LEFT JOIN 
      user_items ui ON ss.user_item_id = ui.id
    LEFT JOIN 
      gp_exchanges ge ON ss.user_gp_item_id = ge.id
    JOIN 
      user u ON ss.user_id = u.id
    WHERE 
      ss.status = 'accepted' AND (
        ss.id LIKE ? OR 
        ss.user_item_id LIKE ? OR 
        ss.user_gp_item_id LIKE ? OR 
        ss.user_id LIKE ? OR 
        ss.address LIKE ? OR 
        ss.updated_at LIKE ? OR 
        ss.zip_code LIKE ? OR 
        ss.name LIKE ? OR 
        ss.kana LIKE ? OR 
        ss.pref LIKE ? OR 
        ss.tracking_number LIKE ? OR 
        ss.tel LIKE ? OR 
        ui.name LIKE ? OR 
        ui.category LIKE ? OR 
        ui.price LIKE ? OR 
        ui.image LIKE ? OR 
        ge.name LIKE ? OR 
        ge.category LIKE ? OR 
        ge.gp LIKE ? OR 
        ge.image LIKE ? OR
        u.mail LIKE ?
      )`;

  db.query(query, [searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue, searchValue], (err, results) => {
    if (err) {
      console.error("Error fetching shipping statuses:", err);
      logErr(`配送依頼のあるアイテムを検索できませんでした。エラー内容:${err} - search-shipping-statuses`);
      return res.status(500).json({ message: "Database error" });
    }

    res.status(200).json(results);
  });
});

module.exports = router;
