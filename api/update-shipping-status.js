const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/auth");
const {logAdminPut, logErr, logUserPut} = require("../logger");
const createTransporter = require("../service/mailService");
const User = require("../model/user");

router.post("/", authenticateJWT, (req, res) => {
    const {items, userMail, userTel,userName} = req.body;
    const userId = req.user.userId;
    const validStatuses = ["pending", "accepted", "shipped"];
    let connection;
    // 从连接池中获取连接，并检查是否满足100积分条件
    db.getConnection((err, conn) => {
        if (err) {
            logErr(`数据库连接失败。错误内容:${err} - update-shipping-status`);
            return res.status(500).json({message: "Database connection failed"});
        }
        connection = conn;

        connection.query("SELECT allPoint FROM user WHERE id = ?", [userId], (error, results) => {
            if (error) {
                logErr(`无法获取用户[${userId}]的积分。错误内容:${error} - update-shipping-status`);
                connection.release();
                return res.status(500).json({message: "Failed to retrieve points."});
            }

            // 检查积分是否大于等于 100
            // if (results[0].allPoint < 100) {
            //     logErr(`用户[${userId}]的积分不足 100。`);
            //     connection.release();
            //     return res.status(403).json({message: "A purchase of at least 100 paid points is required to request delivery."});
            // }

            // 如果积分充足，则继续执行配送处理
            const queries = items.map((item) => {
                return new Promise((resolve, reject) => {
                    if (!validStatuses.includes(item.status)) {
                        reject(new Error("Invalid status"));
                        return;
                    }

                    const userTelCheckQuery = "SELECT tel FROM user WHERE id = ?";
                    connection.query(userTelCheckQuery, [userId], (error, results) => {
                        if (error) {
                            reject(new Error("Database query failed"));
                            return;
                        }

                        if (results.length === 0 || results[0].tel === null || results[0].tel === "") {
                            const error = new Error("Phone number not verified");
                            error.statusCode = 400;
                            reject(error);
                            return;
                        }

                        // 插入或更新 shipping_statuses 表
                        const query = `
                            INSERT INTO shipping_statuses (user_item_id,
                                                           user_gp_item_id,
                                                           user_id,
                                                           status,
                                                           address,
                                                           address2,
                                                           zip_code,
                                                           name,
                                                           kana,
                                                           pref,
                                                           tel)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY
                            UPDATE
                                status =
                            VALUES (status), address =
                            VALUES (address), address2 =
                            VALUES (address2), zip_code =
                            VALUES (zip_code), name =
                            VALUES (name), pref =
                            VALUES (pref), kana =
                            VALUES (kana), tel =
                            VALUES (tel), updated_at = CURRENT_TIMESTAMP`;

                        const userItemId = item.id || null;
                        const userGpItemId = item.user_gp_item_id || null;

                        connection.query(
                            query,
                            [userItemId, userGpItemId, userId, item.status, item.address, item.address2, item.zip_code, item.userName, item.userKana, item.pref, userTel],
                            (err, results) => {
                                if (err) {
                                    logErr(`配送状态修改失败。错误内容:${err} - update-shipping-status`);
                                    reject(err);
                                    return;
                                }

                                // 减少 user_items 表中的 quantity 字段
                                const decrementQuery = `
                                    UPDATE user_items
                                    SET quantity = quantity - 1
                                    WHERE id = ?
                                      AND quantity > 0`;

                                connection.query(decrementQuery, [userItemId], (decrementErr, decrementResults) => {
                                    if (decrementErr) {
                                        logErr(`user_items 表的 quantity 字段减少失败。错误内容:${decrementErr} - update-shipping-status`);
                                        reject(decrementErr);
                                        return;
                                    }

                                    resolve(results);
                                });
                            }
                        );
                    });
                });
            });

            Promise.all(queries)
                .then(async () => {
                    const itemsInfo = items.map((item) => `- ${item.userItemName}`).join("\n");
                    // 提取所有 item 的 id
                    const itemIds = items.map(item => item.id);
                    // 计算 items 的总价
                    const totalPrice = items.reduce((sum, item) => sum + (item.price || 0), 0);

                    if (totalPrice <= 1000) {
                        console.log("用户请求发货扣除运费,userId:",userId);
                        await User.deductUserPoints(userId,100);
                        await User.recordShippingPointDeduction(userId, itemIds, 100, totalPrice);
                    }


                    // 邮件发送逻辑
                    const userMailOptions = {
                        from: process.env.SMTP_USER,
                        to: userMail,
                        subject: "Thank you for your shipping request",
                        text: `Thank you for submitting the shipping request.\nWe are now preparing the following items. We will notify you once shipped.\n\n${itemsInfo}`,
                    };

                    const adminMailOptions = {
                        from: process.env.SMTP_USER,
                        to: process.env.SMTP_ADMIN_SEND_MAIL,
                        subject: "New Shipping Request Received",
                        text: `There is a new shipping request. User ID: ${userId}, User Email: ${userMail}. Please check the admin page for details.`,
                    };

                    try {
                        const transport = createTransporter();
                        await transport.sendMail(userMailOptions);
                        await transport.sendMail(adminMailOptions);
                        console.log("+++ Emails Sent +++");
                    } catch (err) {
                        logErr(`Failed to send email for shipping status update. Error: ${err.message} - update-shipping-status`);
                        console.log("--- Email Sending Error ---");
                        console.log(err);
                    }

                    res.status(200).json({message: "Shipping status updated and emails sent"});
                    logUserPut("Shipping status updated and emails sent - update-shipping-status");
                    logAdminPut("Shipping status updated and emails sent - update-shipping-status");
                })
                .catch((err) => {
                    const statusCode = err.statusCode || 500;
                    res.status(statusCode).json({message: err.message});
                    logErr(`Failed to update shipping status. Error: ${err.message} - update-shipping-status`);
                })
                .finally(() => {
                    connection.release();
                });
        });
    });
});

module.exports = router;
