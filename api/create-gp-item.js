const express = require("express");
const router = express.Router();
const multer = require("multer");
const path = require("path");
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const {logAdminPut, logErr} = require("../logger");
const AWS = require("aws-sdk");

// // Multer設定
// const storage = multer.diskStorage({
//   destination: function (req, file, cb) {
//     cb(null, "uploads/");
//   },
//   filename: function (req, file, cb) {
//     cb(null, `${Date.now()}-${file.originalname}`);
//   },
// });
//
// //uploadにmulterのstorageプロパティを代入したオブジェクトを定義
// const upload = multer({ storage: storage });

// 配置 AWS S3 客户端
const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
    region: process.env.AWS_REGION || "us-east-1", // S3 区域
});

// 使用内存存储（不保存到本地磁盘）
const upload = multer({
    storage: multer.memoryStorage(), // 将文件存储在内存中
    limits: {fileSize: 5 * 1024 * 1024}, // 限制文件大小为 5MB
    fileFilter: (req, file, cb) => {
        const allowedTypes = ["image/jpeg", "image/png", "image/gif"];
        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true); // 允许上传
        } else {
            cb(new Error("Unsupported file type")); // 拒绝上传
        }
    },
});


// エンドポイントの作成
// router.post("/", authenticateJWT, upload.any(), (req, res) => {
// router.post("/", upload.any(), (req, res) => {
//   const items = req.body;
//
//   // SQLクエリの生成
//   const queries = [];
//   req.files.forEach((file, index) => {
//     const id = items[`itemsGp_${index + 1}_id`];
//     const name = items[`itemsGp_${index + 1}_name`];
//     const image = `/uploads/${file.filename}`;
//     const gp = items[`itemsGp_${index + 1}_gp`];
//     const category = items[`itemsGp_${index + 1}_category`];
//     const tag = items[`itemsGp_${index + 1}_tag`];
//     const rarity = items[`itemsGp_${index + 1}_rarity`];
//     const allRest = items[`itemsGp_${index + 1}_allRest`];
//     const rest = items[`itemsGp_${index + 1}_rest`];
//
//     const query = `INSERT INTO gp_items (name, image, gp, category, tag, rarity, rest, allRest) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`;
//     const values = [name, image, gp, category, tag, rarity, rest, allRest];
//     queries.push({ query, values });
//   });
//
//   // クエリの実行
//   queries.forEach((q) => {
//     db.query(q.query, q.values, (err, results) => {
//       if (err) {
//         logErr(`gpアイテムを作成できませんでした。エラー内容:${err} - create-gp-item`);
//         console.error("Error inserting gp_item:", err);
//         return res.status(500).json({ message: "Database error" });
//       }
//     });
//   });
//
//   logAdminPut(`gpアイテムを作成できした。 - create-gp-item`);
//   res.status(200).json({ message: "GP items added successfully" });
// });


// エンドポイントの作成
router.post("/", authenticateJWT, upload.any(), async (req, res) => {
// router.post("/", upload.any(), async (req, res) => {
    const items = req.body;

    try {
        // 存储 SQL 查询和参数
        const queries = [];

        for (let index = 0; index < req.files.length; index++) {
            const file = req.files[index];

            // 上传文件到 S3
            const params = {
                Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
                Key: `${Date.now()}-${file.originalname}`, // 文件名
                Body: file.buffer, // 文件内容
                ContentType: file.mimetype, // 文件类型
            };

            const result = await s3.upload(params).promise(); // 上传文件
            const fileUrl = result.Location; // 获取文件的公共访问 URL

            // 获取表单数据
            const id = items[`itemsGp_${index + 1}_id`];
            const name = items[`itemsGp_${index + 1}_name`];
            const image = fileUrl; // 使用 S3 文件的公共 URL
            const gp = items[`itemsGp_${index + 1}_gp`];
            const category = items[`itemsGp_${index + 1}_category`];
            const tag = items[`itemsGp_${index + 1}_tag`];
            const rarity = items[`itemsGp_${index + 1}_rarity`];
            const allRest = items[`itemsGp_${index + 1}_allRest`];
            const rest = items[`itemsGp_${index + 1}_rest`];

            // 构造 SQL 查询
            const query = `
                INSERT INTO gp_items (name, image, gp, category, tag, rarity, rest, allRest)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            `;
            const values = [name, image, gp, category, tag, rarity, rest, allRest];
            queries.push({query, values});
        }

        // 执行所有查询
        for (const q of queries) {
            await new Promise((resolve, reject) => {
                db.query(q.query, q.values, (err, results) => {
                    if (err) {
                        console.error("Error inserting gp_item:", err);
                        logErr(`无法创建 GP 项目。错误内容: ${err} - create-gp-item`);
                        return reject(err);
                    }
                    resolve(results);
                });
            });
        }

        // 记录日志并返回成功响应
        logAdminPut(`GP 项目已成功创建。 - create-gp-item`);
        res.status(200).json({message: "GP items added successfully"});
    } catch (error) {
        console.error("Error processing GP items:", error);
        logErr(`无法创建 GP 项目。错误内容: ${error} - create-gp-item`);
        res.status(500).json({message: "Server error"});
    }
});


module.exports = router;
