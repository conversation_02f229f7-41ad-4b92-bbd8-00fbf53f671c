const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/auth");
const { logAdminPut, logErr } = require("../logger");

router.post("/", authenticateJWT, (req, res) => {
    // 从 JWT 中获取用户 ID
    const userId = req.user.userId;

    // 解构前端传递的参数
    const { address, zip_code, pref, city, country } = req.body;

    // 检查必要参数是否存在
    if (!userId || !address || !zip_code || !pref || !city || !country) {
        logErr(`用户[${userId}]地址变更失败。缺少必要参数 - update-address`);
        return res.status(400).json({ message: "Missing required fields" });
    }

    // 定义 SQL 更新语句
    const query = `
        UPDATE user
        SET
            address = IF(? IS NOT NULL, ?, address),
            zip_code = IF(? IS NOT NULL, ?, zip_code),
            pref = IF(? IS NOT NULL, ?, pref),
            city = IF(? IS NOT NULL, ?, city),
            country = IF(? IS NOT NULL, ?, country)
        WHERE id = ?
    `;

    // 执行 SQL 查询
    db.query(
        query,
        [
            address, address,
            zip_code, zip_code,
            pref, pref,
            city, city,
            country, country,
            userId
        ],
        (err, results) => {
            if (err) {
                logErr(`用户[${userId}]地址变更失败。错误内容:${err} - update-address`);
                return res.status(500).json({ message: "Database error" });
            }

            // 如果更新成功，记录日志并返回成功响应
            logAdminPut(`ユーザー[${userId}]の住所変更しました。 - update-address`);
            res.status(200).json({ message: "Address updated successfully" });
        }
    );
});

module.exports = router;
