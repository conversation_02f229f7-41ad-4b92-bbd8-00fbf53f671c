const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const { logAdminPut, logErr } = require("../logger");
const authenticateJWT = require("../auth/admin-auth"); // 管理员身份验证

router.get("/", authenticateJWT, (req, res) => {
    const userId = req.user ? req.user.userId : null;

    console.log(`请求用户ID: ${userId}`);

    // 查询 recent_pulled_cards_config 表
    const query = "SELECT * FROM recent_pulled_cards_config ORDER BY id DESC";

    db.query(query, (err, results) => {
        if (err) {
            console.error("获取头奖卡牌配置失败:", err);
            logErr(`最新抽出卡牌列表获取失败。错误内容:${err} - recentPulledCards`);
            return res.status(500).send("获取数据失败");
        }

        try {
            const baseURL = "";

            const cards = results.map((item) => ({
                ...item,
                card_image: item.card_image ? `${baseURL}${item.card_image}` : null,
            }));

            res.status(200).json(cards);
            logAdminPut(`最新抽出卡牌列表已获取。 - recentPulledCards`);
        } catch (mapError) {
            console.error("数据映射失败:", mapError);
            logErr(`数据映射失败。错误内容:${mapError} - recentPulledCards`);
            res.status(500).send("服务器内部错误");
        }
    });
});

module.exports = router;
