const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.get("/", authenticateJWT, async (req, res) => {
  const { targetPoints, cItemPoint, remainingCapacity, cItemsReal, cItemMinPoint, packCat } = req.query;

  if (!targetPoints || !cItemPoint || !remainingCapacity || !cItemsReal || !cItemMinPoint || !packCat) {
    logErr(`ハズレアイテム生成に必要な情報を取得できませんでした。 - generate-c-prize`);
    return res.status(400).json({ error: "Missing required parameters" });
  }

  // ["ポケカ"]などの配列を取得
  const categories = JSON.parse(packCat);
  console.log("Categories:", categories);

  try {
    const categoryCondition = categories.map(() => `FIND_IN_SET(?, item_category) > 0`).join(" OR ");

    const query = `
      SELECT id, name, price, image, is_exchange_only 
      FROM items_loser 
      WHERE price BETWEEN ? AND ? 
      AND (${categoryCondition})
      ORDER BY ABS(price - ?) ASC
    `;

    const queryParams = [parseInt(cItemMinPoint, 10), parseInt(cItemPoint, 10), ...categories, parseInt(cItemPoint, 10)];

    console.log("Executing query:", query);
    console.log("With parameters:", queryParams);

    // データベースから条件に合致するアイテムを検索
    db.query(query, queryParams, (err, results) => {
      if (err) {
        console.error(err);
        logErr(`ハズレアイテムの生成ができませんでした。エラー内容:${err} - generate-c-prize`);
        return res.status(500).json({ error: "Database query failed" });
      }

      const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
      let totalAssigned = 0;

      const formattedResults = results.reduce((acc, item) => {
        if (totalAssigned < parseInt(cItemsReal, 10)) {
          const remaining = Math.min(parseInt(remainingCapacity, 10), parseInt(cItemsReal, 10) - totalAssigned);
          acc.push({
            id: item.id,
            name: item.name,
            image: baseURL + item.image,
            price: item.price,
            rest: remaining,
            is_exchange_only: item.is_exchange_only,
          });
          totalAssigned += remaining;
        }
        return acc;
      }, []);

      res.json(formattedResults);
      logAdminPut(`ハズレアイテムの生成が完了しました。 - generate-c-prize`);
    });
  } catch (error) {
    console.error("Server error:", error);
    res.status(500).json({ error: "Internal server error" });
    logErr(`ハズレアイテムの生成ができませんでした。エラー内容:${error} - generate-c-prize`);
  }
});

module.exports = router;
