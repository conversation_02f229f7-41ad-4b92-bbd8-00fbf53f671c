const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db"); // 接続プールを使用
const { logUserPut, logErr } = require("../logger");

router.post("/", async (req, res) => {
  const { packInfoId } = req.body;
  let connection;

  try {
    // 接続プールから接続を取得
    connection = await new Promise((resolve, reject) => {
      db.getConnection((err, conn) => {
        if (err) return reject(err);
        resolve(conn);
      });
    });

    // トランザクションの開始
    await new Promise((resolve, reject) => {
      connection.beginTransaction((err) => {
        if (err) return reject(err);
        resolve();
      });
    });

    // パック情報の取得
    const packInfo = await new Promise((resolve, reject) => {
      connection.query("SELECT * FROM pack_info WHERE id = ?", [packInfoId], (err, results) => {
        if (err) {
          logErr(`パックの情報取得ができませんでした。パックID[${packInfoId}]エラー内容:${err} - draw`);
          return reject(err);
        }
        resolve(results[0]);
      });
    });

    // ガチャのランクを取得
    const packRank = packInfo.rank;

    // ランクが存在する、もしくはガチャが存在しない、もしくはガチャが売り切れの場合はエラーを返す
    if (packRank !== null || !packInfo || packInfo.status === "sold-out") {
      throw new Error("This gacha cannot be test-opened.");
    }

    // 該当ガチャの取得
    const items = await new Promise((resolve, reject) => {
      connection.query("SELECT * FROM items WHERE packId = ?", [packInfoId], (err, results) => {
        if (err) {
          logErr(`テストパックが実行できませんでした。パックID[${packInfoId}]エラー内容:${err} - draw`);
          return reject(err);
        }
        resolve(results);
      });
    });

    if (items.length === 0) {
      throw new Error("There are no items registered in this gacha.");
    }

    //選ばれたアイテムを格納する配列
    const selectedItems = [];

    // 通常アイテムのリストからキリ番賞とラストワン賞を除外
    let normalItems = items.filter((item) => item.category !== "last_one" && item.category !== "roundNum");

    // 通常のアイテム取得処理
    for (let i = 0; i < 1; i++) {
      let selectedItem = null;
      let selectedItemIndex = -1;

      // 重み付きランダム選択を行う関数
      function weightedRandom(items) {
        // restの合計値を計算
        const totalWeight = items.reduce((sum, item) => sum + item.rest, 0);
        // 0からtotalWeightの間の乱数を生成
        let randomValue = Math.random() * totalWeight;

        for (let i = 0; i < items.length; i++) {
          // restがランダム値以内に収まる場合該当のアイテムを返す
          if (randomValue < items[i].rest) {
            // 選ばれたアイテムインデックスを1個返す
            return i;
          }

          // 選ばれなかった場合、ランダム値からrestを引区ことで、次のアイテムが取得される確率を上げる
          randomValue -= items[i].rest;
        }
        return -1;
      }

      // アイテムを1つ選択する処理。選ばれたアイテム残数が0の場合、nullを返し再選択を実施
      while (!selectedItem) {
        // nonOrderItemsから重み付きランダムで選択
        selectedItemIndex = weightedRandom(normalItems);
        selectedItem = normalItems[selectedItemIndex];

        if (selectedItemIndex !== -1) {
          selectedItem = normalItems[selectedItemIndex];
        }
      }

      // 選ばれたアイテムのrestをデクリメントする
      normalItems[selectedItemIndex].rest -= 1;

      const baseURL = ``;
      const imageUrl = selectedItem.category === "C" ? selectedItem.image : baseURL + selectedItem.image;

      // 選ばれたアイテムを配列に追加
      selectedItems.push({
        id: selectedItem.id,
        category: selectedItem.category,
        name: selectedItem.name,
        price: selectedItem.price,
        image: imageUrl,
      });

      // 選ばれたアイテムをnormalItemsから除外
      normalItems = normalItems.filter((item) => item.id !== selectedItem.id || item.rest > 0);
    }

    // トランザクションのコミット
    await new Promise((resolve, reject) => {
      connection.commit((err) => {
        if (err) return reject(err);
        resolve();
      });
    });

    res.json({
      message: "Test draw executed successfully.",
      items: selectedItems,
    });    
    logUserPut("テスト開封が実行されました - test - draw");
  } catch (error) {
    console.log(error);
    logErr(`テスト開封が実行されたガチャ[${packInfoId}]が挙動しませんでした。エラー内容:${error} - test-draw`);

    if (connection) {
      await new Promise((resolve, reject) => {
        connection.rollback((err) => {
          if (err) return reject(err);
          resolve();
        });
      });
    }

    res.status(500).json({ message: error.message });
  } finally {
    if (connection) connection.release(); // 接続を解放
  }
});

module.exports = router;
