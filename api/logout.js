const express = require("express");
const router = express.Router();
const app = express();

router.get("/", (req, res) => {
  res.cookie("oripa-user-token", "", {
    httpOnly: true,
    secure: true, // 必须与登录时一致（直接写 true，不要动态判断）
    sameSite: "None", // 必须与登录时一致
    path: "/", // 必须与登录时一致
    expires: new Date(0), // 立即过期（替代 maxAge）
  });
  res.send("User logged out");
});

module.exports = router;
