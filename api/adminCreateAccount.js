const express = require("express");
const router = express.Router();
const bcrypt = require("bcrypt");
const authenticateJWT = require("../auth/admin-auth");
const db = require("../db/db");
const saltRounds = 10; // bcrypt の salt ラウンド数を定義
const { logAdminPut, logErr } = require("../logger");

// ルート設定
router.post("/", authenticateJWT, async (req, res) => {
  const { mail, pass } = req.body;

  try {
    // メールアドレスの重複チェック
    const checkEmailQuery = "SELECT mail FROM admin_accounts WHERE mail = ?";
    const emailExists = await new Promise((resolve, reject) => {
      db.query(checkEmailQuery, [mail], (err, results) => {
        if (err) return reject(err);
        resolve(results.length > 0);
        logErr(`メールアドレスが重複していたので、アカウントを追加できませんでした。エラー内容:${err}`);
      });
    });

    if (emailExists) {
      return res.status(400).json({ message: "Email already exists." });
    }

    // パスワードをハッシュ化
    const hashedPassword = await bcrypt.hash(pass, saltRounds);

    // ユーザー情報をデータベースに登録
    const insertUserQuery = "INSERT INTO admin_accounts (mail, pass) VALUES (?,?)";
    db.query(insertUserQuery, [mail, hashedPassword], (err, results) => {
      if (err) {
        console.error("Database insertion error:", err);
        logErr(`管理者アカウントを追加できませんでした。エラー内容:${err} - adminCreateAccount`);
        return res.status(500).json({ message: "Failed to register user." });
      }
      console.log("User registered:", results.insertId);
      logAdminPut(`管理者アカウントを追加できました。 - adminCreateAccount`);
      res.json({ message: "Verification successful and user registered!" });
    });
  } catch (err) {
    logErr(`管理者アカウントを追加できませんでした。エラー内容:${err} - adminCreateAccount`);
    console.error("Error during registration process:", err);
    res.status(500).json({ message: "Server error during registration." });
  }
});

module.exports = router;
