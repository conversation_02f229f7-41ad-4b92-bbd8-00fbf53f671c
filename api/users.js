const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.get("/", authenticateJWT, (req, res) => {
  const query = "SELECT id, name, mail, address,address2, point, gp, tel, zip_code, pref, kana, day_point, weekly_point, month_point, allPoint, display FROM user";

  db.query(query, (err, results) => {
    if (err) {
      logErr(`ユーザー情報一覧を取得できませんでした。エラー内容:${err} - users`);
      return res.status(500).json({ message: "A database error has occurred." });
    }

    res.status(200).json(results);
    logAdminPut(`ユーザー情報一覧を取得できました。 - users`);
  });
});

module.exports = router;
