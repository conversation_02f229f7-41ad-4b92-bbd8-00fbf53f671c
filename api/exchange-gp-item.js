const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/auth");
const { logUserPut, logErr } = require("../logger");

router.post("/", authenticateJWT, async (req, res) => {
  const userId = req.user.userId;
  const itemId = req.body.item_id;

  // 交換対象のアイテムを取得
  db.query("SELECT * FROM gp_items WHERE id = ?", [itemId], (err, results) => {
    if (err) {
      console.error("Error fetching gp item:", err);
      logErr(`GPアイテムの取得ができませんでした。エラー内容:${err} - exchange-gp-item`);
      return res.status(500).json({ message: "Database error" });
    }

    if (results.length === 0) {
      return res.status(404).json({ message: "GP item not found" });
    }

    const item = results[0];

    // ユーザー情報を取得
    db.query("SELECT * FROM user WHERE id = ?", [userId], (err, userResults) => {
      if (err) {
        console.error("Error fetching user:", err);
        logErr(`GPアイテム交換用のユーザー[${userId}]の情報取得ができませんでした。エラー内容:${err} - exchange-gp-item`);
        return res.status(500).json({ message: "Database error" });
      }

      if (userResults.length === 0) {
        return res.status(404).json({ message: "User not found" });
      }

      const user = userResults[0];

      // GPのチェック
      if (user.gp < item.gp) {
        return res.status(400).json({ message: "Insufficient GP" });
      }

      // アイテムの残数チェック
      if (item.rest <= 0) {
        return res.status(400).json({ message: "Item out of stock" });
      }

      // 交換処理
      const newUserGp = user.gp - item.gp;
      const newItemRest = item.rest - 1;

      db.query("UPDATE user SET gp = ? WHERE id = ?", [newUserGp, userId], (err) => {
        if (err) {
          logErr(`ユーザーのGPポイントの取得ができませんでした。エラー内容:${err} - exchange-gp-item`);
          console.error("Error updating user GP:", err);
          return res.status(500).json({ message: "Database error" });
        }

        db.query("UPDATE gp_items SET rest = ? WHERE id = ?", [newItemRest, itemId], (err) => {
          if (err) {
            logErr(`GPアイテムの残数を更新できませんでした。エラー内容:${err} - exchange-gp-item`);
            console.error("Error updating item rest:", err);
            return res.status(500).json({ message: "Database error" });
          }

          const insertQuery = "INSERT INTO gp_exchanges (user_id, item_id, category, name, gp, image, quantity) VALUES (?, ?, ?, ?, ?, ?, ?)";
          const values = [userId, item.id, item.category, item.name, item.gp, item.image, 1];

          db.query(insertQuery, values, (err) => {
            if (err) {
              logErr(`ユーザー[${userId}]にGPアイテムを割り付けられませんでした。エラー内容:${err} - exchange-gp-item`);
              console.error("Error inserting user item:", err);
              return res.status(500).json({ message: "Database error" });
            }

            logUserPut(`ユーザー[${userId}]にGPアイテムを割り付けられました。 - exchange-gp-item`);
            res.status(200).json({ message: "Item exchanged successfully" });
          });
        });
      });
    });
  });
});

module.exports = router;
