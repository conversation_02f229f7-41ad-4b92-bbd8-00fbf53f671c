const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const multer = require("multer");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");
const AWS = require("aws-sdk");

const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
  region: process.env.AWS_REGION || "us-east-1", // S3 区域
});

// Multer 配置（改为内存存储）
const upload = multer({
  storage: multer.memoryStorage(), // 将文件存储在内存中，而不是磁盘
}).any(); // 支持解析任意数量的文件字段

// 路由处理器
router.post("/", authenticateJWT, upload, async (req, res) => {
  const files = req.files; // 获取上传的文件
  if (!files || files.length === 0) {
    return res.status(400).json({ message: "未上传视频文件。" });
  }

  const videoData = JSON.parse(req.body.videoData); // 解析请求体中的视频数据

  try {
    for (const video of videoData) {
      const { rank, name, fileId, defaultStatus } = video; // 提取每个视频的数据
      const file = files.find((f) => f.fieldname === fileId); // 查找对应的文件

      if (!file) {
        console.error(`未找到字段名为 ${fileId} 的文件`);
        return res.status(400).json({ message: `未上传字段名为 ${fileId} 的视频文件。` });
      }

      // 文件上传到 S3
      const params = {
        Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
        Key: `${Date.now()}-${file.originalname}`, // 文件名（带时间戳避免重复）
        Body: file.buffer, // 文件内容（存储在内存中的 Buffer）
        ContentType: file.mimetype, // 文件类型
      };

      const s3Result = await s3.upload(params).promise();
      const videoPath = s3Result.Location; // 获取文件的公共访问 URL

      // 插入视频数据到数据库
      const query = "INSERT INTO pack_video (`rank`, `video_path`, `name`, `defaultStatus`) VALUES (?, ?, ?, ?)";
      await new Promise((resolve, reject) => {
        db.query(query, [rank, videoPath, name, defaultStatus ? 1 : 0], (err, results) => {
          if (err) {
            logErr(`视频添加失败。错误内容: ${err} - upload-video`);
            console.error("数据库插入错误:", err);
            return reject(err);
          }
          resolve(results);
        });
      });
    }

    res.status(200).json({ message: "视频上传成功！" });
    logAdminPut(`视频添加成功。 - upload-video`);
  } catch (error) {
    console.error("处理视频上传时发生错误:", error);
    logErr(`视频添加失败。错误内容: ${error.message} - upload-video`);
    res.status(500).json({ message: "视频上传失败。", error: error.message });
  }
});
module.exports = router;
