const express = require("express");
const router = express.Router();
const nodemailer = require("nodemailer");
const crypto = require("crypto");
const db = require("../db/db");
const createTransporter = require("../service/mailService");
require("dotenv").config();
const { logUserPut, logErr, logAdminPut } = require("../logger");

const transporter = createTransporter();

// 密码重置请求端点
router.post("/", async (req, res) => {
  const { mail } = req.body;

  try {
    // 从 user 表中查询用户
    const user = await new Promise((resolve, reject) => {
      db.query("SELECT * FROM user WHERE mail = ?", [mail], (err, results) => {
        if (err) {
          logErr(`无法从 user 表中查询邮箱地址。错误内容:${err} - pass-reset`);
          return reject(err);
        }
        resolve(results[0]);
      });
    });

    if (!user) {
      return res.status(404).json({ message: "User not found." });
    }

    const token = crypto.randomBytes(20).toString("hex"); // 生成随机令牌
    const resetPasswordExpires = Date.now() + 3600000; // 设置过期时间为1小时后

    await new Promise((resolve, reject) => {
      db.query(
          "UPDATE user SET reset_password_token = ?, reset_password_expires = ? WHERE id = ?",
          [token, resetPasswordExpires, user.id],
          (err, result) => {
            if (err) {
              logErr(`无法设置密码重置令牌。错误内容:${err} - pass-reset`);
              return reject(err);
            }
            resolve(result);
          }
      );
    });

    const resetURL = `${process.env.PASS_RESET_HOST}/user/${token}`; // 构造密码重置链接

    const mailOptions = {
      to: mail,
      from: process.env.SMTP_USER,
      subject: "Password Reset", // 邮件主题改为英文
      text: `You have requested a password reset. Please click the following link to reset your password:\n\n${resetURL}\n\nThis link will expire in 1 hour.`, // 邮件正文改为英文
    };

    transporter.sendMail(mailOptions, (err) => {
      if (err) {
        console.error("Error sending password reset email:", err);
        logErr(`发送密码重置邮件失败。错误内容:${err} - pass-reset`);
        return res.status(500).json({ message: "Failed to send password reset email." });
      }
      res.status(200).json({ message: "Password reset email sent successfully." });
      logUserPut(`密码重置邮件发送成功。 - pass-reset`);
    });
  } catch (error) {
    console.error("Error processing password reset request:", error);
    res.status(500).json({ message: "Failed to process password reset request." });
    logErr(`处理密码重置请求失败。错误内容:${error} - pass-reset`);
  }
});

module.exports = router;