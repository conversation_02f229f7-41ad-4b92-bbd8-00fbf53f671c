const express = require("express");
const router = express.Router();
const db = require("../db/db"); // 接続プール
const authenticateJWT = require("../auth/auth");
const { logUserPut, logErr, logPaymentPut } = require("../logger");
const { formatNumber } = require("../util/formatNumber");
const userTransaction = require("../model/userTransaction");

router.post("/", authenticateJWT, async (req, res) => {
  const { items } = req.body;
  const userId = req.user.userId;
  let connection;

  try {
    // 接続プールから接続を取得
    connection = await new Promise((resolve, reject) => {
      db.getConnection((err, conn) => {
        if (err) return reject(err);
        resolve(conn);
      });
    });

    // トランザクションを開始
    await new Promise((resolve, reject) => {
      connection.beginTransaction((err) => {
        if (err) return reject(err);
        resolve();
      });
    });

    // userテーブルのallPointを取得し、100ポイント未満であれば処理を終了
    const allPoint = await new Promise((resolve, reject) => {
      connection.query("SELECT allPoint FROM user WHERE id = ?", [userId], (err, results) => {
        if (err) {
          logErr(`ユーザー[${userId}]のポイントを取得できませんでした。エラー内容:${err} - get-all-point`);
          return reject(err);
        }
        if (results[0].allPoint < 100) {
          logErr(`ユーザー[${userId}]のポイントが100未満です。`);
          return reject(new Error("To exchange points, you must purchase at least 100 paid points."));
        }
        resolve(results[0].allPoint);
      });
    });

    // allPointが100ポイント以上であれば、ポイント交換処理を続行
    for (const item of items) {
      // アイテムの数量をチェック
      const checkQuantity = await new Promise((resolve, reject) => {
        connection.query("SELECT quantity FROM user_items WHERE id = ? AND user_id = ? FOR UPDATE", [item.id, userId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]のアイテム[${item.id}]をロック中にエラーが発生しました。エラー内容:${err} - lock-item`);
            return reject(err);
          }
          if (results.length === 0) {
            logErr(`ユーザー[${userId}]のアイテム[${item.id}]は存在しません。`);
            return reject(new Error(`Item not found for user ${userId} and item ${item.id}`));
          }
          resolve(results[0].quantity);
        });
      });

      if (checkQuantity <= 0) {
        logErr(`ユーザー[${userId}]のアイテム[${item.userItemId}]は既に交換済みです。`);
        // エラーレスポンスを返して処理を終了
        return res.status(400).json({
          message: `Item [${item.userItemId}] for user [${userId}] has already been exchanged. - item-point-change`,
        });
      }

      // user_itemsテーブルのuser_idカラムと一致するレコードの中に、item.idと一致するitem_idが存在するか確認
      const checkItem = await new Promise((resolve, reject) => {
        connection.query("SELECT * FROM user_items WHERE user_id = ? AND id = ?", [userId, item.id], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]のアイテム[${item.id}]の交換処理中にエラーが発生しました。エラー内容:${err} - check-item`);
            return reject(err);
          }
          resolve(results);
        });
      });

      const checkShipOnly = checkItem.filter((item) => item.is_ship_only === 1);

      if (checkShipOnly.length > 0) {
        logErr(`ユーザー[${userId}]のアイテム[${item.id}]は配送専用アイテムです。 - item-point-change`);
        return res.status(400).json({
          message: `${item.name} は配送専用アイテムです。`,
        });
      }

      if (checkItem.length === 0) {
        logErr(`ユーザー[${userId}]のアイテム[${item.id}]は存在しません。`);
        throw new Error(`Item not found for user ${userId} and item ${item.id}`);
      }

      // ユーザーのポイントを確認
      const userPointPast = await new Promise((resolve, reject) => {
        connection.query("SELECT point FROM user WHERE id = ?", [userId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]のポイントを取得できませんでした。エラー内容:${err} - get-user-point`);
            return reject(err);
          }
          resolve(results[0].point);
        });
      });

      // アイテムのポイントをユーザーのポイントに追加
      await new Promise((resolve, reject) => {
        connection.query("UPDATE user SET point = point + ? WHERE id = ?", [item.price, userId], async (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]が交換したアイテムのポイントを交換できませんでした。エラー内容:${err} - item-point-change`);
            return reject(err);
          }
          try {
            const newUserPoint = userPointPast + item.price;
            await userTransaction.add(connection, userId, "exchange_points", item.price, item.id, "exchange points success", newUserPoint);
            resolve(results);
          } catch (ex) {
            reject(ex);
          }
        });
      });

      // ユーザーのポイントを確認
      const userPointNew = await new Promise((resolve, reject) => {
        connection.query("SELECT point FROM user WHERE id = ?", [userId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]のポイントを取得できませんでした。エラー内容:${err} - get-user-point`);
            return reject(err);
          }
          resolve(results[0].point);
        });
      });

      // アイテムの数量を1つ減らす
      await new Promise((resolve, reject) => {
        connection.query("UPDATE user_items SET quantity = quantity - 1 WHERE id = ? AND user_id = ?", [item.id, userId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]が交換したアイテムの残数を変更できませんでした。エラー内容:${err} - item-point-change`);
            return reject(err);
          }
          resolve(results);
        });
      });

      // アイテムの数量をチェック
      const checkQuantityNew = await new Promise((resolve, reject) => {
        connection.query("SELECT quantity FROM user_items WHERE id = ? AND user_id = ?", [item.id, userId], (err, results) => {
          if (err) {
            logErr(`ユーザー[${userId}]のアイテム[${item.id}]の残数を確認中にエラーが発生しました。エラー内容:${err} - check-quantity`);
            return reject(err);
          }
          if (results.length === 0) {
            logErr(`ユーザー[${userId}]のアイテム[${item.id}]は存在しません。`);
            return reject(new Error(`Item not found for user ${userId} and item ${item.id}`));
          }
          resolve(results[0].quantity);
        });
      });

      logPaymentPut(`持ち物一覧からユーザー[${userId}]がアイテム[${item.id}]をポイント[${formatNumber(item.price)}]に交換し、ユーザーのポイントは[${formatNumber(userPointPast)}]から[${formatNumber(userPointNew)}]になりました。アイテムの残りの数は[${checkQuantity}]から[${checkQuantityNew}]になりました。 - item-point-change`);
    }

    // トランザクションをコミット
    await new Promise((resolve, reject) => {
      connection.commit((err) => {
        if (err) {
          return connection.rollback(() => {
            reject(err);
          });
        }
        resolve();
      });
    });

    res.status(200).json({ message: "Point exchange completed successfully." }); 
    logUserPut(`ユーザー[${userId}]のアイテムポイント交換が完了しました。 - item-point-change`);
  } catch (err) {
    if (connection) {
      // エラーが発生した場合はロールバック
      await new Promise((resolve, reject) => {
        connection.rollback(() => {
          resolve();
          logPaymentPut(`ユーザー[${userId}]のアイテムポイント交換がロールバックされました。 - item-point-change`);
        });
      });
    }
    res.status(500).json({ message: err.message || "Point exchange failed." });
    logErr(`ユーザー[${userId}]が交換したアイテムポイント交換に失敗しました。エラー内容:${err} - item-point-change`);
  } finally {
    if (connection) connection.release(); // 接続を解放
  }
});

module.exports = router;