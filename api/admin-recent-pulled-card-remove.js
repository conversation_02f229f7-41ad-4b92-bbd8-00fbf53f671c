const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.post("/", authenticateJWT, (req, res) => {
    const { removeNumber } = req.body;

    // 请求参数校验
    if (!removeNumber) {
        res.status(400).send("removeNumber is required");
        return;
    }

    // 执行数据库删除操作
    db.query(
        "DELETE FROM recent_pulled_cards_config WHERE id = ?",
        [removeNumber],
        (err, results) => {
            if (err) {
                console.error("Error deleting record:", err);
                res.status(500).send("Failed to delete record");
                logErr(`最新抽出カード[ID:${removeNumber}]を削除できませんでした。エラー内容:${err} - admin-recent-pulled-card-remove`);
                return;
            }

            if (results.affectedRows === 0) {
                res.status(404).send("No record found with the given ID");
                logAdminPut(`該当の最新抽出カード[ID:${removeNumber}]は存在しませんでした。 - admin-recent-pulled-card-remove`);
                return;
            }

            res.status(200).json({ message: "Record deleted successfully" });
            logAdminPut(`最新抽出カード[ID:${removeNumber}]を削除しました。 - admin-recent-pulled-card-remove`);
        }
    );
});

module.exports = router;
