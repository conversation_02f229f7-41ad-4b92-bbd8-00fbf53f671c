const express = require("express");
const router = express.Router();
const multer = require("multer");
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");
const AWS = require("aws-sdk");

// Multer設定
// const storage = multer.diskStorage({
//   destination: function (req, file, cb) {
//     cb(null, "uploads/");
//   },
//   filename: function (req, file, cb) {
//     cb(null, `${Date.now()}-${file.originalname}`);
//   },
// });
//
// const upload = multer({ storage: storage });


// 初始化 S3 客户端
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
  region: process.env.AWS_REGION || "us-east-1", // S3 区域
});

// Multer 配置（改为内存存储）
const upload = multer({
  storage: multer.memoryStorage(), // 将文件存储在内存中，而不是磁盘
}).any(); // 支持解析任意数量的文件字段

router.post("/", authenticateJWT, upload, async (req, res) => {
  const {
    point_background_color,
    sold_out_display_days,
    random_order_days,
    display_type,
    meta_title,
    meta_description,
    service_name,
    privacy_policy,
    notation_commercial,
    terms_conditions,
    gtm,
    theme_name,
    header_point_border_color,
    gpName,
    maintenance_status,
    gp_name,
  } = req.body;

  let draw_history = req.body.draw_history;

  try {
    // 文件上传到 S3 的函数
    const uploadToS3 = async (file) => {
      if (!file) return null;
      const params = {
        Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
        Key: `${Date.now()}-${file.originalname}`, // 文件名（带时间戳避免重复）
        Body: file.buffer, // 文件内容（存储在内存中的 Buffer）
        ContentType: file.mimetype, // 文件类型
      };
      const s3Result = await s3.upload(params).promise();
      return s3Result.Location; // 返回文件的公共访问 URL
    };

    // 上传所有文件到 S3
    const oneDrawButtonImage = await uploadToS3(req.files.find((f) => f.fieldname === "one_draw_button_image"));
    const tenDrawButtonImage = await uploadToS3(req.files.find((f) => f.fieldname === "ten_draw_button_image"));
    const hundredDrawButtonImage = await uploadToS3(req.files.find((f) => f.fieldname === "hundred_draw_button_image"));
    const logoImage = await uploadToS3(req.files.find((f) => f.fieldname === "logo_image"));
    const packListBackground = await uploadToS3(req.files.find((f) => f.fieldname === "pack_list_background"));
    const faviconImage = await uploadToS3(req.files.find((f) => f.fieldname === "favicon_image"));
    const ogImage = await uploadToS3(req.files.find((f) => f.fieldname === "meta_og_image"));
    const rankDetailImage = await uploadToS3(req.files.find((f) => f.fieldname === "rank_detail_image"));
    const headerPointIcon = await uploadToS3(req.files.find((f) => f.fieldname === "header_point_icon"));
    const headerGpIcon = await uploadToS3(req.files.find((f) => f.fieldname === "header_gp_icon"));
    const categorySliderLeftArrow = await uploadToS3(req.files.find((f) => f.fieldname === "category_slider_left_arrow"));
    const categorySliderRightArrow = await uploadToS3(req.files.find((f) => f.fieldname === "category_slider_right_arrow"));
    const packItemPointIcon = await uploadToS3(req.files.find((f) => f.fieldname === "pack_item_point_icon"));
    const packDrawButtonIcon = await uploadToS3(req.files.find((f) => f.fieldname === "pack_draw_button_icon"));
    const detailItemBackground = await uploadToS3(req.files.find((f) => f.fieldname === "detail_item_background"));
    const detailItemTitleImageS = await uploadToS3(req.files.find((f) => f.fieldname === "detail_item_title_image_s"));
    const detailItemTitleImageA = await uploadToS3(req.files.find((f) => f.fieldname === "detail_item_title_image_a"));
    const detailItemTitleImageB = await uploadToS3(req.files.find((f) => f.fieldname === "detail_item_title_image_b"));
    const detailItemTitleImageC = await uploadToS3(req.files.find((f) => f.fieldname === "detail_item_title_image_c"));
    const detailItemTitleImageRoundNum = await uploadToS3(req.files.find((f) => f.fieldname === "detail_item_title_image_round_num"));
    const detailItemTitleImageLastOne = await uploadToS3(req.files.find((f) => f.fieldname === "detail_item_title_image_last_one"));

    console.log("gp" + gpName);

    try {
      const existingSettings = await new Promise((resolve, reject) => {
        db.query("SELECT * FROM pack_settings WHERE id = ?", [1], (err, results) => {
          if (err) {
            logErr(`无法获取包信息。错误内容:${err} - pack-settings`);
            return reject(err);
          }
          resolve(results[0]);
        });
      });

      if (existingSettings) {
        const updateFields = [];
        const updateValues = [];

        // 更新字段逻辑
        if (point_background_color) {
          updateFields.push("point_background_color = ?");
          updateValues.push(point_background_color);
        }
        if (oneDrawButtonImage) {
          updateFields.push("one_draw_button_image = ?");
          updateValues.push(oneDrawButtonImage);
        }
        if (tenDrawButtonImage) {
          updateFields.push("ten_draw_button_image = ?");
          updateValues.push(tenDrawButtonImage);
        }
        if (hundredDrawButtonImage) {
          updateFields.push("hundred_draw_button_image = ?");
          updateValues.push(hundredDrawButtonImage);
        }
        if (logoImage) {
          updateFields.push("logo_image = ?");
          updateValues.push(logoImage);
        }
        if (sold_out_display_days) {
          updateFields.push("sold_out_display_days = ?");
          updateValues.push(sold_out_display_days);
        }
        if (random_order_days) {
          updateFields.push("random_order_days = ?");
          updateValues.push(random_order_days);
        }
        if (packListBackground) {
          updateFields.push("pack_list_background = ?");
          updateValues.push(packListBackground);
        }
        if (display_type) {
          updateFields.push("display_type = ?");
          updateValues.push(display_type);
        }
        if (meta_title) {
          updateFields.push("meta_title = ?");
          updateValues.push(meta_title);
        }
        if (meta_description) {
          updateFields.push("meta_description = ?");
          updateValues.push(meta_description);
        }
        if (faviconImage) {
          updateFields.push("favicon_image = ?");
          updateValues.push(faviconImage);
        }
        if (ogImage) {
          updateFields.push("meta_og_image = ?");
          updateValues.push(ogImage);
        }
        if (draw_history) {
          updateFields.push("draw_history = ?");
          updateValues.push(draw_history === "true" ? 1 : 0);
        }
        if (service_name) {
          updateFields.push("service_name = ?");
          updateValues.push(service_name);
        }
        if (privacy_policy) {
          updateFields.push("privacy_policy = ?");
          updateValues.push(privacy_policy);
        }
        if (notation_commercial) {
          updateFields.push("notation_commercial = ?");
          updateValues.push(notation_commercial);
        }
        if (terms_conditions) {
          updateFields.push("terms_conditions = ?");
          updateValues.push(terms_conditions);
        }
        if (gtm) {
          updateFields.push("gtm = ?");
          updateValues.push(gtm);
        }
        if (rankDetailImage) {
          updateFields.push("rank_detail_image = ?");
          updateValues.push(rankDetailImage);
        }
        if (theme_name) {
          updateFields.push("theme_name = ?");
          updateValues.push(theme_name);
        }
        if (header_point_border_color) {
          updateFields.push("header_point_border_color = ?");
          updateValues.push(header_point_border_color);
        }
        if (headerPointIcon) {
          updateFields.push("header_point_icon = ?");
          updateValues.push(headerPointIcon);
        }
        if (headerGpIcon) {
          updateFields.push("header_gp_icon = ?");
          updateValues.push(headerGpIcon);
        }
        if (categorySliderLeftArrow) {
          updateFields.push("category_slider_left_arrow = ?");
          updateValues.push(categorySliderLeftArrow);
        }
        if (categorySliderRightArrow) {
          updateFields.push("category_slider_right_arrow = ?");
          updateValues.push(categorySliderRightArrow);
        }
        if (packItemPointIcon) {
          updateFields.push("pack_item_point_icon = ?");
          updateValues.push(packItemPointIcon);
        }
        if (packDrawButtonIcon) {
          updateFields.push("pack_draw_button_icon = ?");
          updateValues.push(packDrawButtonIcon);
        }
        if (detailItemBackground) {
          updateFields.push("detail_item_background = ?");
          updateValues.push(detailItemBackground);
        }
        if (detailItemTitleImageS) {
          updateFields.push("detail_item_title_image_s = ?");
          updateValues.push(detailItemTitleImageS);
        }
        if (detailItemTitleImageA) {
          updateFields.push("detail_item_title_image_a = ?");
          updateValues.push(detailItemTitleImageA);
        }
        if (detailItemTitleImageB) {
          updateFields.push("detail_item_title_image_b = ?");
          updateValues.push(detailItemTitleImageB);
        }
        if (detailItemTitleImageC) {
          updateFields.push("detail_item_title_image_c = ?");
          updateValues.push(detailItemTitleImageC);
        }
        if (detailItemTitleImageRoundNum) {
          updateFields.push("detail_item_title_image_round_num = ?");
          updateValues.push(detailItemTitleImageRoundNum);
        }
        if (detailItemTitleImageLastOne) {
          updateFields.push("detail_item_title_image_last_one = ?");
          updateValues.push(detailItemTitleImageLastOne);
        }
        if (gpName) {
          updateFields.push("gp_name = ?");
          updateValues.push(gpName);
        }
        if (maintenance_status) {
          updateFields.push("maintenance_status = ?");
          updateValues.push(maintenance_status);
        }

        console.log("维护状态：" + maintenance_status);

        // 添加 ID 到更新值
        updateValues.push(1);

        const updateQuery = `
          UPDATE pack_settings 
          SET ${updateFields.join(", ")} 
          WHERE id = ?
        `;

        await new Promise((resolve, reject) => {
          db.query(updateQuery, updateValues, (err, result) => {
            if (err) {
              logErr(`包设置更新失败。错误内容:${err} - pack-settings`);
              return reject(err);
            }
            resolve(result);
          });
        });

        res.send("设置已成功更新！");
        logAdminPut(`包设置已成功更新。 - pack-settings`);
      } else {
        if (!draw_history) {
          draw_history = "false"; // 设置默认值
        }
        const drawHistoryValue = draw_history === "true" ? 1 : draw_history === "false" ? 0 : 0;

        const insertQuery = `
          INSERT INTO pack_settings 
          (point_background_color, one_draw_button_image, ten_draw_button_image, hundred_draw_button_image, logo_image, sold_out_display_days, random_order_days, pack_list_background, display_type, meta_title, meta_description, favicon_image, meta_og_image, draw_history, service_name, privacy_policy, notation_commercial, terms_conditions, gtm, rank_detail_image, theme_name, header_point_icon, header_gp_icon, category_slider_left_arrow, category_slider_right_arrow, pack_item_point_icon, pack_draw_button_icon, detail_item_background, detail_item_title_image_s, detail_item_title_image_a, detail_item_title_image_b, detail_item_title_image_c, detail_item_title_image_round_num, detail_item_title_image_last_one, header_point_border_color, gp_name, maintenance_status) 
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `;

        const insertValues = [
          point_background_color || "#FFFFFF",
          oneDrawButtonImage || null,
          tenDrawButtonImage || null,
          hundredDrawButtonImage || null,
          logoImage || null,
          sold_out_display_days || 0,
          random_order_days || 0,
          packListBackground || null,
          display_type || null,
          meta_title || null,
          meta_description || null,
          faviconImage || null,
          ogImage || null,
          drawHistoryValue, // 已转换为数值
          service_name ? service_name : "", // 必填字段已检查
          privacy_policy || null,
          notation_commercial || null,
          terms_conditions || null,
          gtm || null,
          rankDetailImage || null,
          theme_name || null,
          headerPointIcon || null,
          headerGpIcon || null,
          categorySliderLeftArrow || null,
          categorySliderRightArrow || null,
          packItemPointIcon || null,
          packDrawButtonIcon || null,
          detailItemBackground || null,
          detailItemTitleImageS || null,
          detailItemTitleImageA || null,
          detailItemTitleImageB || null,
          detailItemTitleImageC || null,
          detailItemTitleImageRoundNum || null,
          detailItemTitleImageLastOne || null,
          header_point_border_color || "#FFFFFF",
          gp_name || null,
          maintenance_status || 0,
        ];

        await new Promise((resolve, reject) => {
          db.query(insertQuery, insertValues, (err, result) => {
            if (err) return reject(err);
            resolve(result);
          });
        });

        res.send("包设置已成功更新。");
        logAdminPut(`包设置已成功更新。 - pack-settings`);
      }
    } catch (error) {
      console.error("保存设置时发生错误:", error);
      res.status(500).send("保存设置失败");
      logErr(`包设置更新失败。错误内容:${error} - pack-settings`);
    }
  } catch (error) {
    console.error("文件上传到 S3 时发生错误:", error);
    res.status(500).send("文件上传失败");
    logErr(`文件上传到 S3 失败。错误内容:${error}`);
  }
});

module.exports = router;
