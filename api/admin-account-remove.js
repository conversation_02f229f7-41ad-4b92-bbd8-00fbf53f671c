const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.post("/", authenticateJWT, (req, res) => {
  const { removeNumber } = req.body; // リクエストのボディからremoveNumberを取得

  if (!removeNumber) {
    res.status(400).send("removeNumber is required");
    return;
  }

  db.query("DELETE FROM admin_accounts WHERE id = ?", [removeNumber], (err, results) => {
    if (err) {
      console.error("Error deleting record:", err);
      logErr(`管理者アカウントを削除できませんでした。エラー内容:${err} - admin-account-remove`);
      res.status(500).send("Failed to delete record");
      return;
    }

    if (results.affectedRows === 0) {
      res.status(404).send("No record found with the given ID");
      return;
    }

    res.status(200).json({ message: "Record deleted successfully" });
    logAdminPut(`管理者アカウント[ID:${removeNumber}]を削除しました。 - admin-account-remove`);
  });
});

module.exports = router;
