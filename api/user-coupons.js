const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/auth");
const { logErr, logUserPut, logPaymentPut } = require("../logger");
// クーポンコードをユーザーに割り当てる
router.post("/assign", authenticateJWT, async (req, res) => {
  const { couponCode } = req.body;
  const userId = req.user.userId;

  if (!couponCode || !userId) {
    return res.status(400).json({ error: "Coupon code and user ID are required" });
  }

  const buyDiscountCouponCheckFn = async () => {
    return new Promise((resolve, reject) => {
      db.query("SELECT * FROM buy_discount_coupon WHERE coupon_code = ?", [couponCode], (err, couponResults) => {
        if (err) {
          console.error("Error fetching buy discount coupon:", err);
          logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
          reject(err);
        }
        resolve(couponResults);
      });
    });
  };

  const buyDiscountCouponCheck = await buyDiscountCouponCheckFn();

  console.log("buyDiscountCouponCheck", buyDiscountCouponCheck);

  if (buyDiscountCouponCheck.length > 0) {
    // buy_discount_couponテーブルから該当コードのクーポンからrestを取得
    const rest = buyDiscountCouponCheck[0].rest;

    // 有効期限チェック
    const currentDate = new Date();
    const couponDate = new Date(buyDiscountCouponCheck[0].created_at);
    const termDays = buyDiscountCouponCheck[0].term;
    const expiryDate = new Date(couponDate);
    expiryDate.setDate(couponDate.getDate() + termDays);

    console.log("今日の日付", currentDate, "有効期限", expiryDate, "残数", rest);

    if (currentDate > expiryDate) {
      return res.status(409).json({ error: "The expiration date has passed." });
    }

    if (rest <= 0) {
      return res.status(409).json({ error: "This coupon is out of stock." });
    }

    // userCouponsテーブルに該当クーポンが存在するかをbuy_codeを用いて確認
    const userCouponCheckFn = async () => {
      return new Promise((resolve, reject) => {
        db.query("SELECT * FROM userCoupons WHERE buy_code = ? AND userId = ?", [couponCode, userId], (err, userCouponResults) => {
          if (err) {
            console.error("Error checking user coupons:", err);
            logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
            reject(err);
          }
          resolve(userCouponResults);
        });
      });
    };

    const userCouponCheck = await userCouponCheckFn();

    console.log("取得しているか確認", userCouponCheck);

    if (userCouponCheck.length > 0) {
      return res.status(409).json({ error: "You have already claimed this coupon." });
    }

    // 該当クーポンが存在する場合、restをデクリメントする
    const couponDecrementFn = async () => {
      return new Promise((resolve, reject) => {
        db.query("UPDATE buy_discount_coupon SET rest = rest - 1 WHERE coupon_code = ?", [couponCode], (err) => {
          if (err) {
            console.error("Error decrementing buy discount coupon rest:", err);
            logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
            reject(err);
          }
          resolve();
        });
      });
    };

    await couponDecrementFn();

    // userCouponsテーブルのuserIdにはreq.user.userIdを、nameにはbuy_discount_couponテーブルのnameを、typeには”購入割引”を、termにはbuy_discount_couponテーブルのtermを、dateにはインサートする際の日付を、percentageにはbuy_discount_couponテーブルのdiscountを、statusには”expired”を、couponIdにはnullを、buy_codeにはreq.body.couponCodeをインサートする
    const userCouponInsertFn = async () => {
      return new Promise((resolve, reject) => {
        db.query("INSERT INTO userCoupons (userId, name, type, term, date, percentage, status, couponId, buy_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)", [userId, buyDiscountCouponCheck[0].name, "buyItemDiscount", buyDiscountCouponCheck[0].term, new Date(), buyDiscountCouponCheck[0].discount, "expired", null, couponCode], (err) => {
          if (err) {
            console.error("Error assigning buy discount coupon:", err);
            logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
            reject(err);
          }
          resolve();
        });
      });
    };

    await userCouponInsertFn();

    res.json({ message: "The coupon has been granted!" });
  } else {
    db.query("SELECT * FROM coupons WHERE coupon_key = ?", [couponCode], (err, couponResults) => {
      if (err) {
        console.error("Error fetching coupon:", err);
        logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
        res.status(500).send("Failed to fetch coupon");
        return;
      }
      if (couponResults.length === 0) {
        return res.status(404).json({ error: "Coupon not found" });
      }

      const coupon = couponResults[0];

      // 有効期限チェック
      const currentDate = new Date();
      const couponDate = new Date(coupon.date);
      const termDays = coupon.term;
      const expiryDate = new Date(couponDate);
      expiryDate.setDate(couponDate.getDate() + termDays);

      if (currentDate > expiryDate) {
        return res.status(409).json({ error: "The coupon has expired." });
      }

      if (coupon.rest <= 0) {
        return res.status(409).json({ error: "This coupon is out of stock." });
      }

      // 重複チェック
      db.query("SELECT * FROM userCoupons WHERE userId = ? AND couponId = ?", [userId, coupon.id], (err, userCouponResults) => {
        if (err) {
          logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
          console.error("Error checking user coupons:", err);
          res.status(500).send("Failed to check user coupons");
          return;
        }
        if (userCouponResults.length > 0) {
          return res.status(409).json({ error: "This coupon has already been used." });
        }

        // クーポンをユーザーに割り当てる
        let userCouponStatus = "";

        if (coupon.type === "discount") {
          userCouponStatus = "active";
        } else {
          userCouponStatus = "expired";
        }

        let couponPercentage = coupon.percentage;

        if (coupon.type === "random") {
          couponPercentage = Math.floor(Math.random() * coupon.roundNum) + 1;
          const pointsToAssign = Math.floor((coupon.percentage * couponPercentage) / 100);
          console.log(`Assigned random percentage: ${couponPercentage}% which equals ${pointsToAssign} points`);

          db.query("UPDATE user SET point = point + ? WHERE id = ?", [pointsToAssign, userId], (err) => {
            if (err) {
              logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
              console.error("Error updating user points:", err);
              res.status(500).send("Failed to update user points");
              return;
            }

            db.query("UPDATE coupons SET rest = rest - 1 WHERE id = ?", [coupon.id], (err) => {
              if (err) {
                logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
                console.error("Error decrementing coupon rest:", err);
                res.status(500).send("Failed to decrement coupon rest");
                return;
              }
            });

            db.query("INSERT INTO userCoupons (userId, name, type, term, percentage, status, couponId) VALUES (?, ?, ?, ?, ?, ?, ?)", [userId, coupon.name, coupon.type, coupon.term, couponPercentage, userCouponStatus, coupon.id], (err) => {
              if (err) {
                logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
                console.error("Error assigning coupon:", err);
                res.status(500).send("Failed to assign coupon");
                return;
              }
              res.json({ message: `The coupon has been granted with ${couponPercentage}% of the total (${pointsToAssign}P)!` });
            });
          });
        } else {
          db.query("INSERT INTO userCoupons (userId, name, type, term, percentage, status, couponId) VALUES (?, ?, ?, ?, ?, ?, ?)", [userId, coupon.name, coupon.type, coupon.term, couponPercentage, userCouponStatus, coupon.id], (err) => {
            if (err) {
              logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
              console.error("Error assigning coupon:", err);
              res.status(500).send("Failed to retrieve the coupon.");
              return;
            }

            db.query("UPDATE coupons SET rest = rest - 1 WHERE id = ?", [coupon.id], (err) => {
              if (err) {
                logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
                console.error("Error decrementing coupon rest:", err);
                res.status(500).send("Unable to retrieve the coupon.");
                return;
              }
            });

            if (coupon.type === "distribution") {
              db.query("SELECT point FROM user WHERE id = ?", [userId], (err, results) => {
                if (err) {
                  logErr(`ポイント情報の取得に失敗しました。エラー内容:${err} - user-coupons`);
                  console.error("Error fetching user points:", err);
                  res.status(500).send("Failed to retrieve the coupon.");
                  return;
                }

                const currentPoints = results[0]?.point || 0; // 現在のポイントを取得
                const updatedPoints = currentPoints + coupon.percentage; // 新しいポイントを計算

                db.query("UPDATE user SET point = point + ? WHERE id = ?", [coupon.percentage, userId], (err) => {
                  if (err) {
                    logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
                    console.error("Error updating user points:", err);
                    res.status(500).send("Failed to retrieve the coupon.");
                    return;
                  }

                  logPaymentPut(`ユーザー[${userId}]がクーポン[${coupon.id}]を取得し、ポイント[${coupon.percentage}]を取得しました。それに伴い、ポイントが[${currentPoints}]Pから[${updatedPoints}]Pに増加しました。`);
                  res.json({
                    message: `The points distribution coupon has been granted with ${coupon.percentage}P!`,
                  });
                });
              });
            } else {
              res.json({ message: `${coupon.percentage}% discount coupon has been granted!` });
            }
          });
        }
      });
    });
  }
});

// ユーザーのクーポン一覧を取得する
router.get("/user/:userId", authenticateJWT, (req, res) => {
  const userId = req.user.userId;

  db.query("SELECT * FROM userCoupons WHERE userId = ?", [userId], (err, coupons) => {
    if (err) {
      logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
      console.error("Error fetching user coupons:", err);
      res.status(500).send("Failed to fetch user coupons");
      return;
    }

    const currentDate = new Date();
    const expiredCoupons = coupons.filter((coupon) => {
      const couponDate = new Date(coupon.date);
      const expiryDate = new Date(couponDate);
      expiryDate.setDate(couponDate.getDate() + coupon.term);
      return currentDate > expiryDate && coupon.status !== "expired";
    });

    if (expiredCoupons.length > 0) {
      const expiredCouponIds = expiredCoupons.map((coupon) => coupon.id);

      db.query("UPDATE userCoupons SET status = 'expired' WHERE id IN (?)", [expiredCouponIds], (updateErr) => {
        if (updateErr) {
          logErr(`該当クーポンの取得ができませんでした。エラー内容:${updateErr} - user-coupons`);
          console.error("Error updating expired coupons:", updateErr);
          res.status(500).send("Failed to update expired coupons");
          return;
        }

        // 再度クーポンを取得して返す
        db.query("SELECT * FROM userCoupons WHERE userId = ?", [userId], (finalErr, updatedCoupons) => {
          if (finalErr) {
            logErr(`該当クーポンの取得ができませんでした。エラー内容:${finalErr} - user-coupons`);
            console.error("Error fetching updated user coupons:", finalErr);
            res.status(500).send("Failed to fetch updated user coupons");
            return;
          }
          res.status(200).json(updatedCoupons);
        });
      });
    } else {
      res.status(200).json(coupons);
    }
  });
});

// 使用するときに取得するクーポンリスト
router.get("/active/:userId", authenticateJWT, (req, res) => {
  const userId = req.user.userId;

  db.query("SELECT * FROM userCoupons WHERE userId = ? AND type = 'discount' AND status = 'active'", [userId], (err, results) => {
    if (err) {
      console.error("Error fetching active discount coupons:", err);
      res.status(500).send("Failed to fetch active discount coupons");
      return;
    }
    res.status(200).json(results);
  });
});

module.exports = router;
