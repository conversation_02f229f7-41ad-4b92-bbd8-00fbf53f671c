const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/auth");
const { createFincode } = require("@fincode/node");

const API_KEY = "m_test_NzUzNzJjNTgtY2Q1Ny00M2U3LWI4OWEtYTVjYzQ5ZTYyNWI2ZTMxMmM2MDUtNDVkOS00ZDIxLTkzMjItMTc5MTlhOTRiYmU0c18yNDA4MDc1NzUwMw";
const tenantShopId = "s_24080757503"; // ここに実際のShop IDを入れてください

router.post("/", authenticateJWT, async (req, res) => {
  const { transaction, amountNum } = req.body;
  const userId = req.user.userId;

  console.log(transaction);

  const id = transaction.id;
  const accessId = transaction.access_id;
  const data = { ...transaction };
  delete data.id;

  console.log(data);

  if (!id || !accessId) {
    return res.status(400).send("transaction.id and transaction.access_id are required");
  }

  const fincode = createFincode({ apiKey: API_KEY, isLiveMode: false });

  try {
    // 決済確定のリクエスト
    const authorizeResponse = await fincode.payments.execute(id, {
      pay_type: "Card",
      ...data,
    });
    const updateUserPointsQuery = "UPDATE user SET point = point + ?, day_point = day_point + ?, weekly_point = weekly_point + ?, month_point = month_point + ?, allPoint = allPoint + ? WHERE id = ?";
    db.query(updateUserPointsQuery, [amountNum, amountNum, amountNum, amountNum, amountNum, userId], (err, results) => {
      if (err) {
        return res.status(500).send({ message: "Failed to update points.", error: err });
      }

      res.status(200).json({
        message: "Payment successful",
        data: authorizeResponse,
      });
    });
  } catch (error) {
    console.error("Error details:", error);

    if (error.response) {
      res.status(error.response.status).json(error.response.data);
    } else {
      res.status(500).json({ error: "An error occurred" });
    }
  }
});

module.exports = router;
