const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/auth");
const { logUserPut, logErr } = require("../logger");
const User = require("../model/user");

router.get("/", authenticateJWT, (req, res) => {
  const userId = req.user.userId;

  const query = `
    SELECT 
      u.id, u.name, u.mail, u.point, u.gp, u.tel, u.address, u.address2, u.city, u.zip_code, u.pref, u.kana, u.user_image, u.country, u.first_name, u.last_name,
      ue.exp, ue.level, ue.total_recharge, ue.vip_level
    FROM 
      user u
    LEFT JOIN 
      user_extension ue ON u.id = ue.id
    WHERE 
      u.id = ?
  `;
  db.query(query, [userId], async (err, results) => {
    if (err) {
      logErr(`ユーザー[${userId}]の情報が取得できませんでした。エラー内容:${err} - user`);
      return res.status(500).json({ message: "A database error occurred." });
    }

    if (results.length === 0) {
      return res.status(404).json({ message: "User not found." });
    }

    const addHistory = await User.insertLoginHistory(userId, res);

    const user = results[0];
    user.fastLogin = addHistory.fastLogin;

    const vipLevelMap = {
      0: "Guest",
      1: "Bronze Member",
      2: "Silver Member",
      3: "Gold Member",
      4: "Platinum Member",
      5: "Diamond Member",
    };
    user.vip_title = vipLevelMap[user.vip_level] || "Guest";

    res.status(200).json(user);
    logUserPut(`ユーザー[${userId}]の情報が取得できました。 - user`);
  });
});


// 新增：更新用户个人信息的接口
router.post("/update_personal_info", authenticateJWT, (req, res) => {
  const userId = req.user.userId;
  const { first_name, last_name, tel, email, address, zip_code, pref, city, country } = req.body;

  const updates = {};
  // 只有在请求中提供了字段时，才将其添加到更新对象中
  if (first_name !== undefined) updates.first_name = first_name;
  if (last_name !== undefined) updates.last_name = last_name;
  if (tel !== undefined) updates.tel = tel;
  if (email !== undefined) updates.mail = email;
  if (address !== undefined) updates.address = address;
  if (zip_code !== undefined) updates.zip_code = zip_code;
  if (pref !== undefined) updates.pref = pref;
  if (city !== undefined) updates.city = city;
  if (country !== undefined) updates.country = country;

  if (Object.keys(updates).length === 0) {
    return res.status(400).json({ message: "No fields to update provided." });
  }

  // 如果同时更新了 first_name 和 last_name，也更新 'name' 字段
  if (updates.first_name && updates.last_name) {
    updates.name = `${updates.first_name} ${updates.last_name}`;
  }

  db.query("UPDATE user SET ? WHERE id = ?", [updates, userId], (err, results) => {
    if (err) {
      logErr(`ユーザー[${userId}]の個人情報更新に失敗しました。エラー内容:${err} - user/update_personal_info`);
      return res.status(500).json({ message: "Failed to update personal information." });
    }

    if (results.affectedRows === 0) {
      return res.status(404).json({ message: "User not found or data was unchanged." });
    }

    logUserPut(`ユーザー[${userId}]の個人情報を更新しました。 - user/update_personal_info`);
    res.json({ message: "Personal information updated successfully." });
  });
});


// 新增：更新用户手机号的接口
router.post("/update_phone_number", authenticateJWT, (req, res) => {
  const userId = req.user.userId; // 从 JWT 中提取用户 ID
  const { phoneNumber } = req.body;

  // 参数校验
  if (!phoneNumber) {
    return res.status(400).json({ message: "Phone number is required." });
  }

  // 更新数据库中的手机号
  db.query("UPDATE user SET tel = ? WHERE id = ?", [phoneNumber, userId], (err, results) => {
    if (err) {
      console.error("Error updating phone number:", err);
      return res.status(500).json({ message: "Failed to update phone number." });
    }

    // 检查是否成功更新
    if (results.affectedRows === 0) {
      return res.status(404).json({ message: "User not found or phone number not updated." });
    }

    // 返回成功消息
    res.json({ message: "Phone number updated successfully." });
  });
});


module.exports = router;
