const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/auth");
const { logUserPut, logErr } = require("../logger");

router.get("/", authenticateJWT, (req, res) => {
  const userId = req.user.userId;
  const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}`;
  const { status } = req.query;

  let query = `
    SELECT 
      ge.*, 
      (
        SELECT ss.status 
        FROM shipping_statuses ss 
        WHERE ss.user_gp_item_id = ge.id 
        ORDER BY ss.updated_at DESC 
        LIMIT 1
      ) AS shipping_status, 
      u.address AS user_address 
    FROM 
      gp_exchanges ge 
    LEFT JOIN 
      user u ON ge.user_id = u.id 
    WHERE 
      ge.user_id = ?`;

  if (status && status !== "all") {
    if (status === "pending") {
      query += ` AND (
                  SELECT ss.status 
                  FROM shipping_statuses ss 
                  WHERE ss.user_gp_item_id = ge.id 
                  ORDER BY ss.updated_at DESC 
                  LIMIT 1
                ) IS NULL`;
    } else {
      query += ` AND (
                  SELECT ss.status 
                  FROM shipping_statuses ss 
                  WHERE ss.user_gp_item_id = ge.id 
                  ORDER BY ss.updated_at DESC 
                  LIMIT 1
                ) = '${status}'`;
    }
  }

  db.query(query, [userId], (err, results) => {
    if (err) {
      console.error("Error fetching user GP items:", err);
      logErr(`ユーザー[${userId}]のGPアイテム一覧が取得できませんでした。エラー内容:${err} - get-user-gp-items`);
      return res.status(500).json({ message: "Database error" });
    }

    const itemsWithAbsoluteURLs = results.map((item) => ({
      ...item,
      image: `${baseURL}${item.image}`,
    }));

    res.status(200).json(itemsWithAbsoluteURLs);
    logUserPut(`ユーザー[${userId}]のGPアイテム一覧が取得できました。 - get-user-gp-items`);
  });
});

module.exports = router;
