const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

router.get("/", (req, res) => {
  const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}`;
  const category = req.query.category;
  let query = "SELECT * FROM gp_items";

  if (category) {
    query += ` WHERE tag LIKE '%${category}%'`;
  }

  db.query(query, (err, results) => {
    if (err) {
      console.error("Error fetching gp items:", err);
      logErr(`GPアイテムを取得できませんでした。エラー内容:${err} - get-gp-item`);
      return res.status(500).json({ message: "Database error" });
    }

    const items = results.map((item) => ({
      ...item,
      image: baseURL + item.image,
    }));

    logUserPut(`GPアイテムを取得できました。 - get-gp-item`);
    res.status(200).json(items);
  });
});

module.exports = router;
