const express = require("express");
require("dotenv").config();
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.get("/", authenticateJWT, async (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const limit = parseInt(req.query.limit) || 10;
  const offset = (page - 1) * limit;
  const sortKey = req.query.sortKey || "id";
  const sortOrder = req.query.sortOrder || "asc";
  const searchQuery = req.query.searchQuery || "";
  // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
  const baseURL = ``;

  try {
    const searchCondition = searchQuery ? `WHERE (name LIKE ? OR category LIKE ?)` : "";

    const results = await new Promise((resolve, reject) => {
      db.query(`SELECT * FROM items_loser ${searchCondition} ORDER BY ?? ${sortOrder} LIMIT ? OFFSET ?`, searchQuery ? [`%${searchQuery}%`, `%${searchQuery}%`, sortKey, limit, offset] : [sortKey, limit, offset], (err, results) => {
        if (err) {
          logErr(`ハズレアイテムの取得ができませんでした。エラー内容:${err} - get-c-prize`);
          return reject(err);
        }
        resolve(results);
      });
    });

    const itemsWithAbsoluteImageURL = results.map((item) => ({
      ...item,
      image: baseURL + item.image,
    }));

    const totalItems = await new Promise((resolve, reject) => {
      db.query(`SELECT COUNT(*) AS count FROM items_loser ${searchCondition}`, searchQuery ? [`%${searchQuery}%`, `%${searchQuery}%`] : [], (err, results) => {
        if (err) {
          return reject(err);
        }
        resolve(results[0].count);
      });
    });

    res.json({
      items: itemsWithAbsoluteImageURL,
      totalItems,
      totalPages: Math.ceil(totalItems / limit),
      currentPage: page,
    });

    logAdminPut(`ハズレアイテムの取得ができました。 - get-c-prize`);
  } catch (error) {
    console.error("Error fetching C prize items:", error);
    res.status(500).json({ message: "Failed to fetch C prize items" });
    logErr(`ハズレアイテムの取得ができませんでした。エラー内容:${error} - get-c-prize`);
  }
});

module.exports = router;
