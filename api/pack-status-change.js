const express = require("express");
const router = express.Router();
const db = require("../db/db");
const authenticateJWT = require("../auth/admin-auth");
const { logAdminPut, logErr } = require("../logger");

router.post("/", authenticateJWT, async (req, res) => {
  const { packId, packChangeStatus } = req.body;

  if (!packId || !packChangeStatus) {
    return res.status(400).send("packId and packChangeStatus are required");
  }

  console.log(`Updating pack status for packId: ${packId} to ${packChangeStatus}`);

  const statusChangeQuery = packChangeStatus === "validity" ? "UPDATE pack_info SET status = ?, validity_at = NOW(), sort_number = ? WHERE id = ?" : "UPDATE pack_info SET status = ? WHERE id = ?";

  // pack_infoテーブルのstatusがvalidityのものからsort_numberの一番低い数値を取得するクエリ
  const getMinSortNumberQuery = "SELECT MIN(sort_number) as min_sort_number FROM pack_info WHERE status = 'validity'";

  try {
    const minSortNumber = await new Promise((resolve, reject) => {
      db.query(getMinSortNumberQuery, (err, results) => {
        if (err) {
          logErr(`sort_numberの取得ができませんでした。エラー内容:${err} - pack-status-change`);
          return reject(err);
        }
        resolve(results[0].min_sort_number);
      });
    });

    console.log("一番低い数字" + minSortNumber);

    const newSortNumber = minSortNumber - 1;

    console.log("新しいsort_number" + newSortNumber);

    let result;

    if (packChangeStatus === "validity") {
      result = await new Promise((resolve, reject) => {
        db.query(statusChangeQuery, [packChangeStatus, newSortNumber, packId], (err, results) => {
          if (err) {
            logErr(`パック情報の更新ができませんでした。エラー内容:${err} - pack-status-change`);
            return reject(err);
          }
          resolve(results);
        });
      });
    } else {
      result = await new Promise((resolve, reject) => {
        db.query(statusChangeQuery, [packChangeStatus, packId], (err, results) => {
          if (err) {
            logErr(`パック情報の更新ができませんでした。エラー内容:${err} - pack-status-change`);
            return reject(err);
          }
          resolve(results);
        });
      });
    }

    if (result.affectedRows === 0) {
      return res.status(404).send("No pack found with the given ID");
    }

    res.status(200).json({ message: "Pack status updated successfully" });
    logAdminPut(`パック情報の更新ができました。 - pack-status-change`);
  } catch (error) {
    logErr(`パック情報の更新ができませんでした。エラー内容:${error} - pack-status-change`);
    console.error("Error updating pack status:", error);
    res.status(500).send("Failed to update pack status");
  }
});

module.exports = router;
