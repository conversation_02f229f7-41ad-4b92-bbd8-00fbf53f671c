// pool.js
const mysql = require("mysql");
const { logServerPut, logErr } = require("../logger");

let pool;

function getDbConnection() {
  if (!pool) {
    pool = mysql.createPool({
      connectionLimit: 100,
      host: process.env.DATABASE_HOST,
      user: process.env.DATABASE_USER,
      password: process.env.DATABASE_PASSWORD,
      database: process.env.DATABASE_NAME,
      port: process.env.DATABASE_PORT,
      waitForConnections: true,
      queueLimit: 0,
    });

    pool.on("connection", () => {
      console.log("MySQL connection established.");
      logServerPut("MySQL接続が確立されました。");
    });

    pool.on("error", (err) => {
      console.error("MySQLエラー: ", err);
      logErr(`MySQLに致命的なエラーが発生しました。エラー内容:${err}`);
      if (err.code === "PROTOCOL_PACKETS_OUT_OF_ORDER") {
        console.error("Packets out of order error detected.");
      } else {
        throw err;
      }
    });
  }

  return pool;
}

module.exports = {
  getDbConnection,
};
