// const mysql = require("mysql");
// const { logServerPut, logErr } = require("../logger");

// let db;

// function handleDisconnect() {
//   db = mysql.createConnection({
//     host: process.env.DATABASE_HOST,
//     user: process.env.DATABASE_USER,
//     password: process.env.DATABASE_PASSWORD,
//     database: process.env.DATABASE_NAME,
//     port: process.env.DATABASE_PORT,
//   });

//   db.connect((err) => {
//     if (err) {
//       console.error("MySQL接続エラー: ", err);
//       logErr(`MySQLに致命的なエラーが発生しました。エラー内容:${err}`);
//       setTimeout(handleDisconnect, 2000); // 再接続の試行
//     } else {
//       console.log("MySQL connected...");
//       logServerPut("MySQLに接続できました。");
//     }
//   });

//   db.on("error", (err) => {
//     console.error("MySQLエラー: ", err);
//     logErr(`MySQLに致命的なエラーが発生しました。エラー内容:${err}`);
//     if (err.code === "PROTOCOL_PACKETS_OUT_OF_ORDER") {
//       handleDisconnect(); // 接続が失われた場合の再接続
//     } else {
//       throw err;
//     }
//   });
// }

// handleDisconnect();

// module.exports = db;
// db.js
const { getDbConnection } = require("./pool");

let db = getDbConnection();

module.exports = db;
