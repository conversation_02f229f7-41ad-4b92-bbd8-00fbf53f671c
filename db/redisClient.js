const Redis = require("ioredis");

// 检查是否为本地环境
const valkey_switch_on = process.env.VALKEY_SWITCH_ON === "true";

let valkeyClient;

if (valkey_switch_on) {
    const valkeyConfig = {
        // host: "clustercfg.backend-dev.ranqms.use1.cache.amazonaws.com", // Valkey 主机地址
        host: process.env.VALKEY_HOST, // Valkey 主机地址
        port: process.env.VALKEY_PORT, // Valkey 端口
        tls: {}, // 启用 TLS 加密
        connectTimeout: 5000, // 连接超时时间为 5 秒
        maxRetriesPerRequest: 1, // 每个请求的最大重试次数
    };

    valkeyClient = new Redis(valkeyConfig);

    // 监听连接成功事件
    valkeyClient.on("connect", () => {
        console.log("Successfully connected to Valkey!");
    });

    // 监听错误事件
    valkeyClient.on("error", (err) => {
        console.error("Valkey connection error:", err);
    });
} else {
    console.warn("Skipping Valkey initialization for local development.");
    valkeyClient = null; // 或者使用 Mock 数据
}

module.exports = valkeyClient;
