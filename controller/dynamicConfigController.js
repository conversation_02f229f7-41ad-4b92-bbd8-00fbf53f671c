const DynamicConfig = require('../model/dynamicConfig');

// Query API: get row by dataId
exports.getConfigByDataId = async (req, res) => {
    try {
        const { dataId } = req.query;
        if (!dataId) {
            return res.status(400).json({ message: 'dataId parameter is required' });
        }
        const result = await DynamicConfig.getByDataId(null, dataId);
        if (!result) {
            return res.status(404).json({ message: 'Config not found' });
        }
        res.status(200).json(result);
    } catch (err) {
        console.error('Failed to query dynamic config:', err);
        res.status(500).json({ message: 'Failed to query dynamic config' });
    }
};

// Update API: update config by id
exports.updateConfigById = async (req, res) => {
    try {
        const { id } = req.body;
        const { config } = req.body;
        if (!id || !config) {
            return res.status(400).json({ message: 'id and config parameters are required' });
        }
        const result = await DynamicConfig.updateConfigById(id, config);
        res.status(200).json({ message: 'Config updated successfully', result });
    } catch (err) {
        console.error('Failed to update dynamic config:', err);
        res.status(500).json({ message: 'Failed to update dynamic config' });
    }
}; 