const Public = require("../model/public");
const Res = require("../util/responseHelpers");
const Day = require("../util/dayHelpers");
const axios = require("axios");
const bcrypt = require("bcrypt");
const saltRounds = 10;
require("dotenv").config();
const jwt = require("jsonwebtoken");
const { logUserPut, logErr } = require("../logger");
const createTransporter = require("../service/mailService");

/* ---------------------------------------------------------------------
パック取得
-----------------------------------------------------------------------*/
exports.getPacks = async (req, res) => {
  try {
    const { category } = req.params;

    // パックデータの処理
    const processPackData = (pack, specialItems, randomOrderDays, soldOutDisplayDays, currentDate) => {
      // sold-out 状態のチェック
      if (!category) {
        if (pack.status === "sold-out" && soldOutDisplayDays && pack.updated_at) {
          const packUpdatedAtDate = new Date(pack.updated_at);
          const daysSincePackUpdated = Day.getDateDifferenceInDays(packUpdatedAtDate, currentDate);
          if (daysSincePackUpdated > soldOutDisplayDays) {
            return null;
          }
        }
      }

      // ランダム順序チェック
      if (randomOrderDays && pack.updated_at) {
        const packUpdatedAtDate = new Date(pack.updated_at);
        const daysSincePackUpdated = Day.getDateDifferenceInDays(packUpdatedAtDate, currentDate);
        if (daysSincePackUpdated >= randomOrderDays) {
          specialItems.sort(() => Math.random() - 0.5);
        }
      }

      return { ...pack, image: pack.image, specialItems: specialItems };
    };

    const settings = await Public.getPackSettings();
    const currentDate = new Date();
    const { random_order_days, sold_out_display_days } = settings;
    const { packId } = req.query;

    if (packId) {
      const pack = await Public.getPackById(packId);
      if (!pack) {
        return res.status(404).json({ message: "Pack not found" });
      }
      const specialItems = await Public.getSpecialItemsForPack(pack.id);
      const specialItemImage = specialItems.map((item) => ({
        ...item,
        image: item.image,
      }));

      const result = processPackData(pack, specialItemImage, random_order_days, sold_out_display_days, currentDate);
      if (!result) {
        return res.status(200).json([]);
      }
      return res.status(200).json([result]);
    }

    const packs = await Public.getPacks("validity", category || undefined);
    // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;

    const updatedResults = await Promise.all(
      packs.map(async (pack) => {
        const specialItems = await Public.getSpecialItemsForPack(pack.id);
        const specialItemImage = specialItems.map((item) => ({
          ...item,
          image: item.image,
        }));

        return processPackData(pack, specialItemImage, random_order_days, sold_out_display_days, currentDate);
      })
    );

    const filteredResults = updatedResults.filter((pack) => pack !== null);
    res.status(200).json(filteredResults);
  } catch (error) {
    logErr(`パックデータの取得に失敗しました。エラー内容: ${error} - controller_publicController_getPacks`);
    console.error("Error processing packs and items:", error);
    res.status(500).send("Failed to process packs and items");
  }
};

/* ---------------------------------------------------------------------
ジャックポット取得
-----------------------------------------------------------------------*/
exports.getJackpot = async (req, res) => {
  try {
    const jackpot = await Public.getJackpot(req);
    res.status(200).json(jackpot);
  } catch (error) {
    logErr(`ジャックポットの取得に失敗しました。エラー内容: ${error} - controller_publicController_getJackpot`);
    console.error("Error fetching jackpot:", error);
    res.status(500).send("Failed to fetch jackpot");
  }
};

/* ---------------------------------------------------------------------
アイテムの詳細情報を取得
-----------------------------------------------------------------------*/
exports.getPackDetails = async (req, res) => {
  const { packId } = req.params;
  // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}`;
  const baseURL = ``;

  try {
    const packStatus = await Public.getPackStatus(packId);
    const results = await Public.getPackItems(packId);

    if (packStatus === "invalid" || packStatus === "sold-out") {
      logErr(`該当のアイテム詳細[${packId}]を取得できませんでした。 - detail-items`);
      throw new Error("This gacha does not exist.");
    }

    const items = results.map((item) => {
      let imageUrl = item.image;
      if (item.category === "itemsS" || item.category === "itemsA" || item.category === "itemsB" || item.category === "last_one" || item.category === "roundNum") {
        // imageUrl = `${baseURL}/${item.image}`;
        imageUrl = `${item.image}`;
      }
      return {
        ...item,
        image: imageUrl,
      };
    });

    logUserPut(`該当のアイテム詳細[${packId}]を取得できました。 - detail-items`);
    res.json(items);
  } catch (error) {
    console.error("Failed to fetch items for pack:", packId, error);
    res.status(500).json({ message: error.message });
  }
};
