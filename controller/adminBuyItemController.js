const db = require("../db/db");
const BuyItem = require("../model/adminBuyItem");
const fs = require("fs");
const { logAdminPut, logErr } = require("../logger");
const Res = require("../util/responseHelpers");
const exp = require("constants");

const AWS = require("aws-sdk");

// 初始化 S3 客户端
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
  region: process.env.AWS_REGION || "us-east-1", // S3 区域
});

exports.addBuyItem = async (req, res) => {
  const file = req.files[0]; // 获取上传的文件
  const { buy_item_value } = req.body;

  try {
    // 将文件上传到 S3
    const params = {
      Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
      Key: `${Date.now()}-${file.originalname}`, // 文件名（带时间戳避免重复）
      Body: file.buffer, // 文件内容（存储在内存中的 Buffer）
      ContentType: file.mimetype, // 文件类型
    };

    const s3Result = await s3.upload(params).promise();
    const filePath = s3Result.Location; // 获取文件的公共访问 URL

    console.log("付费物品值:", buy_item_value, "文件路径:", filePath);

    // 调用数据库方法保存数据
    await BuyItem.addBuyItem(buy_item_value, filePath);
    Res.resJsonDataFn(res, 200, "成功", "付费物品已成功添加。");
    logAdminPut(`付费物品添加成功。 - controller_adminBuyItemController_addBuyItem`);
  } catch (error) {
    console.error("添加付费物品时发生错误:", error);
    logErr(`付费物品添加失败。错误内容: ${error} - controller_adminBuyItemController_addBuyItem`);
    Res.resJsonDataFn(res, 500, "失败", "添加付费物品失败。");
  }
};

/* ---------------------------------------------------------------------
課金アイテム取得
-----------------------------------------------------------------------*/
exports.getBuyItem = async (req, res) => {
  try {
    const results = await BuyItem.getBuyItem();
    Res.resJsonDataFn(res, 200, "Success", "Paid items retrieved successfully.", results);
  } catch (error) {
    console.log("Error fetching buy items:", error);
    logErr(`課金アイテムを取得できませんでした。エラー内容:${error} - controller_adminBuyItemController_getBuyItem`);
    Res.resJsonDataFn(res, 500, "Failed", "Failed to retrieve paid items.");
  }
};

/* ---------------------------------------------------------------------
課金アイテム削除
-----------------------------------------------------------------------*/
exports.removeBuyItem = async (req, res) => {
  const { removeNumber } = req.body;

  try {
    await BuyItem.removeBuyItem(removeNumber);
    Res.resJsonDataFn(res, 200, "Success", "The paid item has been removed.");
  } catch (error) {
    console.error("Error removing buy item:", error);
    logErr(`課金アイテムを削除できませんでした。エラー内容:${error} - controller_adminBuyItemController_removeBuyItem`);
    Res.resJsonDataFn(res, 500, "Failed", "Failed to remove the paid item.");
  }
};

/* ---------------------------------------------------------------------
idを用いた課金アイテム取得
-----------------------------------------------------------------------*/
exports.getIdBuyItem = async (req, res) => {
  const buy_item_id = req.query.buy_item_id;
  console.log("id", buy_item_id);

  try {
    const results = await BuyItem.getIdBuyItem(buy_item_id);
    Res.resJsonDataFn(res, 200, "Success", "The paid item has been retrieved successfully.", results);
  } catch (error) {
    console.error("Error fetching buy item:", error);
    logErr(`課金アイテムを取得できませんでした。エラー内容:${error} - controller_adminBuyItemController_getIdBuyItem`);
    Res.resJsonDataFn(res, 500, "Failed", "Failed to retrieve the paid item.");
  }
};

/* ---------------------------------------------------------------------
該当課金アイテムの更新
-----------------------------------------------------------------------*/
exports.updateBuyItem = async (req, res) => {
  const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
  const file = req.files;
  let filePath = "";
  if (file.length > 0) {
    filePath = baseURL + file[0].path;
  }
  const { buy_item_value } = req.body;
  const { buy_item_id } = req.query;

  try {
    if (file.length > 0) {
      await BuyItem.updateBuyItem(buy_item_id, buy_item_value, filePath);
      console.log("画像ありのアップデート");
    } else {
      await BuyItem.updateBuyItemNoImage(buy_item_id, buy_item_value);
      console.log("画像なしのアップデート");
    }

    Res.resJsonDataFn(res, 200, "Success", "The paid item has been updated.");
  } catch (error) {
    console.error("Error updating buy item:", error);
    logErr(`課金アイテムを更新できませんでした。エラー内容:${error} - controller_adminBuyItemController_updateBuyItem`);
    Res.resJsonDataFn(res, 500, "Failed", "Failed to update the paid item.");
  }
};
