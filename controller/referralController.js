const {generateAndSaveInviteCode, getInviteSummary, getRewardRecordsByInviter} = require("../service/referralService");
const db = require("../db/db");
const {logUserPut, logErr} = require("../logger");

// 模块一：邀请码管理
/**
 * 1.生成邀请码接口（GET /referral/code?userId=xxx）
 */
exports.generateInviteCode = async (req, res) => {
    const {userId} = req.query;

    if (!userId) {
        return res.status(400).json({message: "缺少必要参数：userId"});
    }

    try {
        const inviteCode = await generateAndSaveInviteCode(userId);
        res.status(200).json({code: inviteCode});
        logUserPut(`用户[${userId}]的邀请码已成功生成`);
    } catch (error) {
        logErr(`用户[${userId}]生成邀请码时发生错误，错误内容：${error}`);
        res.status(500).json({message: "邀请码生成失败"});
    }
};


exports.getMyInviteInfo = async (req, res) => {
    const userId = req.user.userId;

    if (!userId) {
        return res.status(400).json({ message: "缺少必要参数：userId" });
    }

    try {
        // 1. 查询邀请码
        const codeQuery = `
            SELECT code 
            FROM referral_invite_codes 
            WHERE user_id = ? AND is_active = TRUE
        `;
        const code = await new Promise((resolve, reject) => {
            db.query(codeQuery, [userId], (err, results) => {
                if (err) return reject(err);
                resolve(results[0]?.code || null);
            });
        });

        // 如果没有有效邀请码，则生成一个新的
        let referralCode = code;
        if (!referralCode) {
            referralCode = await generateAndSaveInviteCode(userId);
            logUserPut(`用户[${userId}]未找到邀请码，已自动生成新邀请码`);
        }

        // 2. 查询邀请记录数量（即已邀请人数）
        const recordsQuery = `
            SELECT COUNT(*) AS count
            FROM referral_invite_records
            WHERE inviter_id = ?
        `;
        const invitedCount = await new Promise((resolve, reject) => {
            db.query(recordsQuery, [userId], (err, results) => {
                if (err) return reject(err);
                resolve(results[0]?.count || 0);
            });
        });

        // 3. 返回整合结果
        const result = {
            invitedCount,      // 已邀请人数
            referralCode       // 邀请码
        };

        res.status(200).json(result);
        logUserPut(`用户[${userId}]成功获取邀请信息: ${JSON.stringify(result)}`);
    } catch (error) {
        logErr(`用户[${userId}]获取邀请信息失败: ${error}`);
        res.status(500).json({ message: "获取邀请信息失败" });
    }
};

//模块二：邀请记录处理
// 1. 用户注册时使用邀请码，记录邀请关系
exports.recordInvite = async (req, res) => {
    const {inviterId, inviteeId, inviteCodeUsed} = req.body;

    if (!inviterId || !inviteeId || !inviteCodeUsed) {
        return res.status(400).json({
            message: "缺少必要参数：inviterId、inviteeId 或 inviteCodeUsed",
        });
    }

    const checkQuery = `
        SELECT id
        FROM referral_invite_records
        WHERE invitee_id = ?
    `;
    const insertQuery = `
        INSERT INTO referral_invite_records
            (inviter_id, invitee_id, invite_code_used)
        VALUES (?, ?, ?)
    `;

    try {
        // 检查是否已有邀请记录
        const existing = await new Promise((resolve, reject) => {
            db.query(checkQuery, [inviteeId], (err, results) => {
                if (err) return reject(err);
                resolve(results);
            });
        });

        if (existing.length > 0) {
            return res.status(400).json({message: "该用户已被邀请过"});
        }

        // 插入邀请记录
        await new Promise((resolve, reject) => {
            db.query(insertQuery, [inviterId, inviteeId, inviteCodeUsed], (err) => {
                if (err) return reject(err);
                resolve();
            });
        });

        res.status(201).json({message: "邀请记录添加成功"});
        logUserPut(`用户[${inviterId}]为被邀请人[${inviteeId}]添加了邀请记录`);
    } catch (error) {
        logErr(`记录邀请关系失败: 邀请人[${inviterId}], 被邀请人[${inviteeId}], 错误:${error}`);
        res.status(500).json({message: "邀请记录添加失败"});
    }
};

/**
 * 2. 获取某人的邀请记录（GET /referral/records?userId=xxx）
 */
exports.getInviteRecordsByInviter = async (req, res) => {
    const {userId} = req.query;

    if (!userId) {
        return res.status(400).json({message: "缺少必要参数：userId"});
    }

    const query = `
        SELECT *
        FROM referral_invite_records
        WHERE inviter_id = ?
    `;

    try {
        const records = await new Promise((resolve, reject) => {
            db.query(query, [userId], (err, results) => {
                if (err) return reject(err);
                resolve(results);
            });
        });

        res.status(200).json(records);
        logUserPut(`用户[${userId}]成功获取邀请记录`);
    } catch (error) {
        logErr(`用户[${userId}]获取邀请记录失败: ${error}`);
        res.status(500).json({message: "获取邀请记录失败"});
    }
};

/**
 * 3. 获取某人的邀请人数（GET /referral/count?userId=xxx）
 */
exports.countInvitedUsers = async (req, res) => {
    const {userId} = req.query;

    if (!userId) {
        return res.status(400).json({message: "缺少必要参数：userId"});
    }

    const query = `
        SELECT COUNT(*) AS count
        FROM referral_invite_records
        WHERE inviter_id = ? AND is_reviewed = TRUE AND is_reward_given = TRUE
    `;

    try {
        const result = await new Promise((resolve, reject) => {
            db.query(query, [userId], (err, results) => {
                if (err) return reject(err);
                resolve(results);
            });
        });

        const count = result[0]?.count || 0;
        res.status(200).json({count});
        logUserPut(`用户[${userId}]邀请人数统计结果: ${count}`);
    } catch (error) {
        logErr(`用户[${userId}]统计邀请人数失败: ${error}`);
        res.status(500).json({message: "统计邀请人数失败"});
    }
};


// 模块三：人工审核管理（运营后台）
/**
 * 1. 获取所有待审核的邀请记录（GET /referral/pending-review）
 */
exports.getPendingReviewRecords = async (req, res) => {
    const query = `
        SELECT 
            rir.*,
            inviter.name AS inviter_name,
            inviter.mail AS inviter_mail,
            invitee.name AS invitee_name,
            invitee.mail AS invitee_mail
        FROM 
            referral_invite_records AS rir
        LEFT JOIN
            user AS inviter ON rir.inviter_id = inviter.id
        LEFT JOIN
            user AS invitee ON rir.invitee_id = invitee.id
        WHERE rir.is_reviewed = FALSE
    `;

    try {
        const records = await new Promise((resolve, reject) => {
            db.query(query, (err, results) => {
                if (err) return reject(err);
                resolve(results);
            });
        });

        res.status(200).json(records);
        logUserPut(`成功获取待审核邀请记录`);
    } catch (error) {
        logErr(`获取待审核邀请记录失败: ${error}`);
        res.status(500).json({message: "获取待审核记录失败"});
    }
};

/**
 * 2. 审核通过，更新状态（POST /referral/approve-record）
 */
exports.approveInviteRecord = async (req, res) => {
    
    const adminUserId = req.user.id; 
    const { recordId } = req.body;
    if (!recordId) {
        return res.status(400).json({message: "缺少必要参数：recordId"});
    }

    const query = `
        UPDATE referral_invite_records
        SET is_reviewed = TRUE,
            reviewed_by = ?,
            reviewed_at = NOW()
        WHERE id = ?
    `;

    try {
        const result = await new Promise((resolve, reject) => {
            db.query(query, [adminUserId, recordId], (err, results) => {
                if (err) return reject(err);
                resolve(results);
            });
        });

        if (result.affectedRows === 0) {
            return res.status(404).json({message: "邀请记录不存在"});
        }

        res.status(200).json({message: "邀请记录审核通过"});
        logUserPut(`邀请记录[${recordId}]审核通过`);
    } catch (error) {
        logErr(`审核邀请记录[${recordId}]失败: ${error}`);
        res.status(500).json({message: "审核邀请记录失败"});
    }
};
//模块四：奖励发放管理 todo

/**
 * 1. 新增邀请记录接口（POST /referral/record）
 */
exports.createInviteRecord = async (req, res) => {
    const {inviterId, inviteeId, inviteCodeUsed} = req.body;

    if (!inviterId || !inviteeId || !inviteCodeUsed) {
        return res.status(400).json({message: "缺少必要参数：inviterId、inviteeId 或 inviteCodeUsed"});
    }

    const query = `
        INSERT INTO referral_invite_records
            (inviter_id, invitee_id, invite_code_used)
        VALUES (?, ?, ?)
    `;

    db.query(query, [inviterId, inviteeId, inviteCodeUsed], (err) => {
        if (err) {
            logErr(`用户[${inviterId}]添加邀请记录失败，错误内容：${err}`);
            return res.status(500).json({message: "邀请记录添加失败"});
        }

        res.status(201).json({message: "邀请记录添加成功"});
        logUserPut(`用户[${inviterId}]为被邀请人[${inviteeId}]添加了邀请记录`);
    });
};

/**
 * 新增奖励记录接口（POST /referral/reward）
 */
exports.createRewardRecord = async (req, res) => {
    const {inviterId, inviteRecordId, rewardType, rewardValue} = req.body;

    if (!inviterId || !inviteRecordId || !rewardType) {
        return res.status(400).json({message: "缺少必要参数：inviterId、inviteRecordId 或 rewardType"});
    }

    const query = `
        INSERT INTO referral_invite_records
            (inviter_id, invite_record_id, reward_type, reward_value)
        VALUES (?, ?, ?, ?)
    `;

    db.query(query, [inviterId, inviteRecordId, rewardType, rewardValue || null], (err) => {
        if (err) {
            logErr(`用户[${inviterId}]添加奖励记录失败，错误内容：${err}`);
            return res.status(500).json({message: "奖励记录添加失败"});
        }

        res.status(201).json({message: "奖励记录添加成功"});
        logUserPut(`用户[${inviterId}]发放奖励类型：${rewardType}`);
    });
};

exports.getReferralSummary = async (req, res) => {
    const {userId} = req.query;

    try {
        const summary = await getInviteSummary(userId);
        res.status(200).json(summary);
    } catch (error) {
        logErr(`用户[${userId}]获取邀请概览失败，错误内容:${error}`);
        res.status(500).json({message: "获取邀请概览失败"});
    }
};

exports.getRewardRecords = async (req, res) => {
    const {userId} = req.query;
    try {
        const rewards = await getRewardRecordsByInviter(userId);
        res.status(200).json(rewards);
    } catch (error) {
        logErr(`用户[${userId}]获取奖励记录失败，错误内容:${error}`);
        res.status(500).json({message: "获取奖励记录失败"});
    }
};
