const UserDraw = require("../model/userDraw");
const User = require("../model/user");
const Rank = require("../model/rank");
require("dotenv").config();
const db = require("../db/db");
const {logUserPut, logErr, logPaymentPut, logServerPut} = require("../logger");
const {formatNumber} = require("../util/formatNumber");
const Redis = require("ioredis");
const {default: Redlock} = require("redlock"); // Redlockのdefaultエクスポートを使用
const redis = require("../db/redisClient");
const { v4: uuidv4 } = require('uuid');
const UserTransaction = require("../model/userTransaction");
const DynamicConfig = require("../model/dynamicConfig");
const Level = require("../model/level");
const UserExtension = require("../model/userExtension");

/* ---------------------------------------------------------------------
用户抽卡控制器

1. 初始化与前置检查
   ├── 获取 Redis 分布式锁
   └── 数据库连接与事务初始化

2. 验证输入参数
   ├── 生成唯一抽卡结果 ID
   ├── 验证抽卡数量
   ├── 验证优惠券
   └── 获取用户和抽卡包信息

3. 检查抽卡条件
   ├── 抽卡包有效性
   ├── 用户等级要求
   └── 每日和总次数限制

4. 抽卡逻辑
   ├── 计算所需积分
   ├── 验证剩余抽卡次数
   └── 获取抽卡包中的物品信息

5. 处理特殊奖励
   ├── 「最后一件奖」
   └── 「整数番号奖」

6. 普通物品抽取
   ├── 筛选普通物品列表
   ├── 顺序物品抽取
   └── 非顺序物品抽取

7. 更新数据
   ├── 减少抽卡包的剩余次数
   ├── 分配物品给用户
   └── 更新用户积分

8. 用户等级更新
   ├── 计算新等级和奖励积分
   └── 更新用户等级和奖励积分

9. GP 值计算与更新
   ├── 获取用户的 GP 比率
   └── 更新用户的 GP 值

10. 提交事务与返回响应
    ├── 提交事务
    ├── 返回响应
    ├── 日志记录
    └── 释放分布式锁

-----------------------------------------------------------------------*/
function generateRandomNumber() {
    return Math.floor(10000000 + Math.random() * 90000000);
}

async function processRoundNumItems() {
    for (const num of RoundNumOnlyArr) {
        if (num > packDrewRestTypeNumber) {
            const roundNumItem = items.find((item) => item.round_num === num && item.category === "roundNum");
            if (roundNumItem) {
                console.log(`[Trace ID: ${traceId}] 这是整数番号奖`);

                // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
                const baseURL = ``;
                const imageUrl = baseURL + roundNumItem.image;

                // 获取该物品的剩余数量
                const restOfRoundNumItem = await UserDraw.getItemRest(connection, roundNumItem.id);

                // 如果剩余数量不为 0，则分配物品
                if (Number(restOfRoundNumItem) !== 0) {
                    const giveRoundNumItem = await UserDraw.giveRoundNumItem(connection, userId, roundNumItem.id, roundNumItem.category, roundNumItem.name, roundNumItem.price, imageUrl, drawNumber);

                    selectedItems.push({
                        userItemId: giveRoundNumItem.userItemId,
                        id: giveRoundNumItem.id,
                        category: giveRoundNumItem.category,
                        name: giveRoundNumItem.name,
                        price: giveRoundNumItem.price,
                        image: giveRoundNumItem.image,
                    });

                    // 减少物品的剩余数量
                    await UserDraw.decreaseItemRest(connection, roundNumItem.id);

                    historyRecords.push({
                        pack_info_id: packInfoId,
                        user_id: userId,
                        item_id: roundNumItem.id,
                        round_num: num,
                    });
                }
            }
        }
    }
}

function weightedRandom(items) {
    const totalWeight = items.reduce((sum, item) => sum + item.rest, 0);
    if (totalWeight <= 0) {
        return -1;
    }
    let randomValue = Math.random() * totalWeight;

    for (let i = 0; i < items.length; i++) {
        if (randomValue < items[i].rest) {
            return i;
        }
        randomValue -= items[i].rest;
    }
    return -1;
}
exports.userDraw = async (req, res) => {
    const redlock = new Redlock(
        [redis], // Redis 客户端数组
        {
            retryCount: 5, // 重试次数
            retryDelay: 200, // 重试间隔（毫秒）
            retryJitter: 100, // 添加随机延迟
        }
    );
    const {packInfoId, packExecutionQuantity, couponId} = req.body;
    const userId = req.user.userId;
    const traceId = uuidv4();
    console.log("生成traceId",userId,"traceId:",traceId)
    const lockKey = `draw_lock_user_${userId}`;
    let connection;
    let lock;
    try {
        try {
            lock = await redlock.acquire([lockKey], 130000);
            console.log(`[Trace ID: ${traceId}] 获取锁成功 userId: ${userId}`);
        } catch (lockErr) {
            console.error(`[Trace ID: ${traceId}] 锁获取失败 userId: ${userId}, Lock Key: ${lockKey}`, lockErr);
            return res.status(503).json({message: "It seems you're drawing cards on another device or browser tab. Please try again later."});
        }
        connection = await new Promise((resolve, reject) => {
            db.getConnection((err, conn) => {
                if (err) return reject(err);
                resolve(conn);
            });
        });

        // 开始事务
        await new Promise((resolve, reject) => {
            connection.beginTransaction((err) => {
                if (err) return reject(err);
                console.log(`[Trace ID: ${traceId}] 开启事务 userId: ${userId}`);
                resolve();
            });
        });
        // 生成随机数，用于结果 ID
        let drawNumber = generateRandomNumber();
        // 验证抽卡数量是否超过 100
        if (packExecutionQuantity > 101) {
            logErr(`单次设置的最大抽卡数量为 100。用户[${userId}] - draw`);
            throw new Error("The maximum number of draws allowed at one time is 100.");
        }
        // 获取用户信息
        const user = await UserDraw.getUser(connection, userId);
        // 获取抽卡包信息
        const packInfo = await UserDraw.getPackInfo(connection, packInfoId, true);
        if (packInfo.status === "invalid") {
            console.log(`[Trace ID: ${traceId}] 此抽卡无效 userId: ${userId}`);
            throw new Error("This gacha is invalid.");
        }
        // 获取当前时间
        const now = new Date();
        // 检查抽卡包的开始时间
        const packDisplayStart = new Date(packInfo.timer_date);
        if (packDisplayStart !== null && now < packDisplayStart) {
            console.log(`[Trace ID: ${traceId}] 此抽卡尚未开始 userId: ${userId}`);
            throw new Error("This gacha has not started yet.");
        }
        // 计算抽卡所需的积分
        let totalPointsRequired = packInfo.point * packExecutionQuantity;
        // 比较用户积分与抽卡所需积分
        if (user.point < totalPointsRequired) {
            console.log(`[Trace ID: ${traceId}] 积分不足 userPoint: ${user.point}`);
            throw new Error("You need more points for this.");
        }
        // 比较请求的抽卡次数与剩余抽卡次数
        if (packInfo.rest < packExecutionQuantity) {
            console.log(`[Trace ID: ${traceId}] 抽卡次数不足 packInfoRest: ${packInfo.rest} packExecutionQuantity:${packExecutionQuantity}`);
            throw new Error("Not enough draws left.");
        }
        // 获取抽卡包中的物品信息
        const items = await UserDraw.getPackItems(connection, packInfoId, true);
        if (items.length === 0) {
            console.log(`[Trace ID: ${traceId}] 此抽卡包中没有物品`);
            throw new Error("There are no items in this gacha pack.");
        }
        // 存储选中的物品
        const selectedItems = [];
        // 存储选中的物品用于历史记录
        const historyRecords = [];
        // 抽卡包的剩余次数
        let packRest = packInfo.rest;
        // 减少抽卡包的剩余次数
        const totalPackUpdateResult = await UserDraw.decreasePackRest(connection, packInfoId, packExecutionQuantity);
        if (totalPackUpdateResult.affectedRows === 0) {
            console.log(`[Trace ID: ${traceId}] 抽卡库存不足`);
            throw new Error("抽卡库存不足 userId:",userId);
        }
        // 从普通物品列表中排除「最后一件奖」和「整数番号奖」，仅保留剩余数量大于 0 的物品
        let normalItems = items.filter((item) => item.category !== "last_one" && item.category !== "roundNum" && item.rest > 0);

        // 普通物品抽取逻辑
        for (let i = 0; i < packExecutionQuantity; i++) {
            let selectedItem = null;
            let selectedItemIndex = -1;
            // 获取设置了顺序且剩余数量大于 0 的物品
            const orderItems = normalItems.filter((item) => item.order !== null && item.rest > 0);
            // 从顺序物品中选择
            if (orderItems.length > 0) {
                for (let j = 0; j < orderItems.length; j++) {
                    console.log(`[Trace ID: ${traceId}] 抽卡剩余次数${packRest}]`);
                    if (Number(orderItems[j].order) === Number(packRest)) {
                        // 在普通物品列表中找到匹配的物品
                        selectedItemIndex = normalItems.findIndex((item) => item.id === orderItems[j].id);

                        if (selectedItemIndex !== -1) {
                            selectedItem = normalItems[selectedItemIndex];
                            console.log(`[Trace ID: ${traceId}] 找到匹配的顺序物品`);
                            break; // 找到后退出循环
                        }
                    }
                }
            }

            // 如果未从顺序物品中选择，则从非顺序物品中选择
            if (!selectedItem) {
                const nonOrderItems = normalItems.filter((item) => item.order === null && item.rest > 0);
                console.log(`[Trace ID: ${traceId}] 非顺序物品`);

                if (nonOrderItems.length === 0) {
                    console.log(`[Trace ID: ${traceId}] 没有可选的物品nonOrderItems`);
                    throw new Error("没有可选的物品");
                }

                selectedItemIndex = weightedRandom(nonOrderItems);
                if (selectedItemIndex === -1) {
                    console.log(`[Trace ID: ${traceId}] 没有可选的物品selectedItemIndex`);
                    throw new Error("没有可选的物品。");
                }

                selectedItem = nonOrderItems[selectedItemIndex];

                // 更新 `selectedItemIndex` 到普通物品列表中的索引
                selectedItemIndex = normalItems.findIndex((item) => item.id === selectedItem.id);
                console.log(`[Trace ID: ${traceId}] 选择的非顺序物品`);
            }

            // 检查选中物品的剩余数量
            if (selectedItem.rest <= 0) {
                normalItems.splice(selectedItemIndex, 1);
                console.log(`[Trace ID: ${traceId}] 物品剩余数量为 0`);
                i--; // 调整循环计数器
                continue;
            }
            selectedItem.rest -= 1;
            // 如果剩余数量为 0，则从普通物品列表中移除
            if (selectedItem.rest <= 0) {
                normalItems.splice(selectedItemIndex, 1);
                console.log(`[Trace ID: ${traceId}] 物品剩余数量已达到 0，从普通物品列表中移除`);
            }
            // 减少抽卡包的剩余次数
            packRest -= 1;
            console.log(`[Trace ID: ${traceId}] 更新后的抽卡剩余次数`);

            // 分配物品给用户并保存历史记录
            const baseURL = '';
            const imageUrl = selectedItem.category === "C" ? selectedItem.image : baseURL + selectedItem.image;
            await UserDraw.decreaseItemRest(connection, selectedItem.id);

            const giveItemResult = await UserDraw.giveItem(connection, userId, selectedItem.id, selectedItem.category, selectedItem.name, selectedItem.price, imageUrl, selectedItem.is_exchange_only, drawNumber, selectedItem.rest, selectedItem.is_ship_only);

            selectedItems.push({
                userItemId: giveItemResult.userItemId,
                id: giveItemResult.id,
                category: giveItemResult.category,
                name: giveItemResult.name,
                price: giveItemResult.price,
                image: imageUrl,
                rest: giveItemResult.rest,
            });

            // 添加到抽取历史记录
            historyRecords.push({
                pack_info_id: packInfoId,
                user_id: userId,
                item_id: selectedItem.id,
            });
        }

        // 如果抽卡包剩余次数不足，抛出错误
        if (packRest < 0) {
            throw new Error("You do not have enough gacha.");
        }

        // 如果抽卡包的剩余次数为 0，则将状态更新为 sold-out
        const updatedPackInfo = await UserDraw.getPackInfo(connection, packInfoId);
        const updatePackRest = Number(updatedPackInfo.rest);
        if (updatePackRest === 0) {
            await UserDraw.updatePackStatus(connection, packInfoId);
        }

        // 批量保存抽取历史记录
        await Promise.all(
            historyRecords.map((record) => {
                UserDraw.saveDrawHistory(connection, record.pack_info_id, record.user_id, record.item_id, record.round_num);
            })
        );

        // 扣减用户积分
        const pointResult = await UserDraw.decreaseUserPoint(connection, packInfoId, totalPointsRequired, userId);
        if (pointResult.affectedRows === 0) {
            // 积分不足
            throw new Error("You do not have enough points.");
        }
        // 获取扣除积分后用户的当前积分
        const currentUserPoint = await User.getUserPoint(connection, userId);
        // 新增积分消耗记录
        await UserTransaction.add(
            connection, // 事务调用
            userId,
            "consume",
            totalPointsRequired,
            drawNumber,
            "consume points",
            currentUserPoint
        );

        // 更新用户已使用的积分
        await UserDraw.updateUserUsedPoint(connection, packInfoId, totalPointsRequired, userId);
        try {
            console.log(`[Trace ID: ${traceId}] [EXP] 开始更新经验值和等级...`);
            const config = await DynamicConfig.getByDataId(connection, 'points_experience_ratio');
            console.log(`[Trace ID: ${traceId}] [EXP] 经验值配置:`, config);
            let ratio = 0;
            if (config && config.config) {
                try {
                    ratio = JSON.parse(config.config).ratio || 0;
                } catch (parseError) {
                    console.error(`[Trace ID: ${traceId}] [EXP] 解析经验值配置JSON失败:`, parseError);
                }
            }
            console.log(`[Trace ID: ${traceId}] [EXP] 经验值比例: ${ratio}`);

            if (ratio > 0) {
                const earnedExp = Math.floor(totalPointsRequired * ratio);
                console.log(`[Trace ID: ${traceId}] [EXP] 本次获得经验: ${earnedExp}`);

                if (earnedExp > 0) {
                    const userExtension = await UserExtension.getOrCreateByUserId(connection, userId);
                    console.log(`[Trace ID: ${traceId}] [EXP] 当前用户扩展信息:`, userExtension);
                    const currentExp = userExtension.exp;
                    const currentLevel = userExtension.level;

                    const newTotalExp = currentExp + earnedExp;
                    console.log(`[Trace ID: ${traceId}] [EXP] 新的总经验值: ${newTotalExp}`);

                    const allLevels = await Level.getAll(connection);
                    const levels = allLevels.filter(level => level.type === 'exp');
                    console.log(`[Trace ID: ${traceId}] [EXP] 等级定义:`, levels);

                    let newLevel = currentLevel;
                    for (let i = levels.length - 1; i >= 0; i--) {
                        if (newTotalExp >= levels[i].value) {
                            newLevel = levels[i].level;
                            break;
                        }
                    }
                    console.log(`[Trace ID: ${traceId}] [EXP] 计算出的新等级: ${newLevel}`);

                    await UserExtension.updateExperienceAndLevel(connection, userId, newTotalExp, newLevel);
                    console.log(`[Trace ID: ${traceId}] [EXP] 成功更新用户经验和等级。`);
                }
            }
        } catch (expError) {
            console.error(`[Trace ID: ${traceId}] [EXP] 更新经验等级出错:`, expError);
            logErr(`用户[${userId}]更新经验值与等级时发生错误: ${expError} - controller_userDrawController_userDraw_experience`);
        }

        // 提交事务
        await new Promise((resolve, reject) => {
            connection.commit((err) => {
                if (err) return reject(err);
                resolve();
            });
        });
        // 返回响应
        res.json({
            message: "物品获取成功",
            items: selectedItems,
            newRank: null,
            resultId: drawNumber,
        });

        // 获取用户的当前积分
        const userPoint = await User.getUserPoint(connection, userId);
        console.log(`[Trace ID: ${traceId}] 用户获得了抽卡包`);

        // 记录日志
        logPaymentPut(
            `用户[${userId}]获得了抽卡包[${packInfoId}]的物品（使用明细: ${formatNumber(packInfo.point)} * ${formatNumber(packExecutionQuantity)} = ${formatNumber(totalPointsRequired)}），用户的积分从[${formatNumber(user.point)}]变为[${formatNumber(userPoint)}]。 - draw - 物品: ${JSON.stringify(
                selectedItems.map(({userItemId, id}) => ({userItemId, id})),
                null,
                2
            )}`
        );

        // 释放分布式锁
        await lock.release();
        console.log(`[Trace ID: ${traceId}] 锁释放成功 ${lockKey}`);
    } catch (error) {
        if (connection) {
            await new Promise((resolve, reject) => {
                connection.rollback((err) => {
                    if (err) return reject(err);
                    resolve();
                });
            });
        }

        if (error && error.code === "ER_LOCK_DEADLOCK") {
            // ログには残す
            console.error("Deadlock occurred:", error);
            console.log(`[Trace ID: ${traceId}] Deadlock occurred`);
            // ユーザーにはやわらかい文言を返す
            return res.status(500).json({
                message: "The process failed due to high traffic. Please try again later. (Your points will not be deducted.)",
            });
        }

        if (lock) {
            try {
                await lock.release();
                console.log(`[Trace ID: ${traceId}] 锁释放成功 ${lockKey}`);

            } catch (releaseErr) {
                // 即使释放锁失败，也不中断服务器运行，仅记录日志
                console.log(`[Trace ID: ${traceId}] 锁释放失败 ${lockKey}`);

            }
        }
        console.error(error);
        logErr(`用户[${userId}]抽取的抽卡包[${packInfoId}]出现异常。错误内容:${error} - controller_userDrawController_userDraw`);
        res.status(500).json({message: error.message});
    } finally {
        if (connection) connection.release(); // 接続を解放
    }
};

/* ---------------------------------------------------------------------
ガチャの結果を取得
-----------------------------------------------------------------------*/
exports.getUserDrawResult = async (req, res) => {
    const resultId = req.query.resultId;

    try {
        const resultItems = await UserDraw.getDrawResult(resultId);

        const responseItems = resultItems.map((item) => {
            return {
                userItemId: item.id,
                id: item.item_id,
                category: item.category,
                name: item.name,
                price: item.price,
                image: item.image,
                is_ship_only: item.is_ship_only,
            };
        });

        console.log("responseItems", responseItems);

        if (resultItems.length === 0) {
            throw new Error("No draw result found.");
        }
        return res.status(200).json({responseItems});
    } catch (error) {
        logErr(`Failed to get draw result: ${error} controller_userDrawController_getUserDrawResult`);
        return res.status(500).json({message: "Failed to get draw result."});
    }
};
