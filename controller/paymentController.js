const User = require("../model/user");
const AdminUser = require("../model/adminUser");
const Payment = require("../model/payment");
const BuyItem = require("../model/userBuyItem");
const Res = require("../util/responseHelpers");
const express = require("express");
const fs = require("fs");
const path = require("path");
const app = express();
app.use(express.json());
require("dotenv").config();
const { createFincode } = require("@fincode/node");
const Client = require("@amazonpay/amazon-pay-api-sdk-nodejs");
const crypto = require("crypto");
const { logAdminPut, logErr, logUserPut, logPaymentPut } = require("../logger");
const { formatNumber } = require("../util/formatNumber");
const { log } = require("console");
const createTransporter = require("../service/mailService");
const UserTransaction = require("../model/userTransaction");
const UserExtension = require("../model/userExtension");

// リトライ関数
async function retryOperation(operation, retries, delay) {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      if (attempt === retries) throw error; // リトライ回数を超えたらエラーを投げる
      console.warn(`Retry attempt ${attempt} failed: ${error.message}`);
      await new Promise((resolve) => setTimeout(resolve, delay * Math.pow(2, attempt - 1))); // 指数バックオフ
    }
  }
}

// クーポンの割引があるかチェックする関数
const discountCheck = async (userId, buy_item_id) => {
  let coupons = [];

  // クーポンが存在するかチェック
  const coupon_code = await BuyItem.getCoupon(userId);
  console.log("coupon_code", coupon_code);

  // クーポンが存在する場合
  if (coupon_code && coupon_code.buy_code !== undefined) {
    // クーポンが使用されているかチェック
    const couponCheck = await BuyItem.checkCoupon(coupon_code.buy_code, userId);
    console.log("couponCheck", couponCheck);

    // クーポンが使用されていない場合
    if (couponCheck.length === 0) {
      coupons = await BuyItem.getCouponByCode(coupon_code.buy_code);
      console.log("couponsList", coupons);
      console.log("buy_item_id", buy_item_id);
      coupons = coupons.find((coupon) => {
        console.log("couponItemId", coupon.item_id);
        console.log("buyItemId", buy_item_id);
        return Number(coupon.item_id) === Number(buy_item_id);
      });
      console.log("findCoupons", coupons);
    }
  }

  if (coupons !== undefined) {
    return coupons.discount;
  }
};

// クーポンコードを出力する関数
const discountCouponCode = async (userId, buy_item_id) => {
  let coupons = [];

  // クーポンが存在するかチェック
  const coupon_code = await BuyItem.getCoupon(userId);
  console.log("coupon_code", coupon_code);

  // クーポンが存在する場合
  if (coupon_code && coupon_code.buy_code !== undefined) {
    // クーポンが使用されているかチェック
    const couponCheck = await BuyItem.checkCoupon(coupon_code.buy_code, userId);
    console.log("couponCheck", couponCheck);

    // クーポンが使用されていない場合
    if (couponCheck.length === 0) {
      coupons = await BuyItem.getCouponByCode(coupon_code.buy_code);
      console.log("couponsList", coupons);
      console.log("buy_item_id", buy_item_id);
      coupons = coupons.find((coupon) => {
        console.log("couponItemId", coupon.item_id);
        console.log("buyItemId", buy_item_id);
        return Number(coupon.item_id) === Number(buy_item_id);
      });
      console.log("findCoupons", coupons);

      return coupon_code.buy_code;
    }
  }
};

/* --------------------------------------------------------------------- 
決済登録を行う（カード）
-----------------------------------------------------------------------*/
exports.addPayment = async (req, res) => {
  const userId = req.user.userId;
  const request = require("request");
  const { buy_item_id } = req.body;
  const Bearer_API_KEY = `Bearer ${process.env.FINCODE_API_KEY}`;
  let BASE_URL = "";
  if (process.env.FINCODE_ENV === "development") {
    BASE_URL = "https://api.test.fincode.jp";
  } else {
    BASE_URL = "https://api.fincode.jp";
  }
  const endpoint = "/v1/payments";

  // 該当購入アイテムの金額を取得
  const pointData = await BuyItem.getPoint(buy_item_id);
  const point = pointData.point;

  // クーポンの割引があるかチェック
  const discount = await discountCheck(userId, buy_item_id);

  const DATA = {
    pay_type: "Card",
    job_code: "CAPTURE",
    amount: `${discount ? point - point * (discount / 100) : point}`,
    tds_type: "0",
    tds2_type: "3",
  };

  const options = {
    url: BASE_URL + endpoint,
    headers: {
      "Content-Type": "application/json; charset=utf-8",
      Authorization: Bearer_API_KEY,
    },
    json: DATA,
  };

  const fincode = createFincode({ apiKey: process.env.FINCODE_API_KEY, isLiveMode: process.env.FINCODE_ENV === "development" ? false : true, options: { timeout: 10000 } });
  try {
    const result = await fincode.payments.create({
      pay_type: "Card",
      job_code: "CAPTURE",
      amount: `${discount ? point - point * (discount / 100) : point}`,
      tds_type: "2",
      tds2_type: "2",
    });
    console.log("result");
    console.log(result);
    // ペイメント情報を記録する
    await Payment.paymentInfoInsertNoPromise(userId, result.id, result.amount, result.status, result.pay_type);
    res.header("Content-Type", "application/json; charset=utf-8");
    res.send(result);
    logUserPut(`ユーザー[${userId}]のクレジットカード決済登録が完了しました - controller_paymentController_addPayment`);
    logPaymentPut(`ユーザー[${userId}]のクレジットカード決済[${result.id}]の登録が完了しました。まだポイントは付与していません - controller_paymentController_addPayment`);
  } catch (error) {
    logErr(`ユーザー[${userId}]のクレジットカード決済登録エラー: ${error} - controller_paymentController_addPayment`);
    console.log(error);
  }
};

/* --------------------------------------------------------------------- 
ユーザー課金後、レスポンスが正しければ所持ポイントを更新する
-----------------------------------------------------------------------*/
exports.runPayment = async (req, res) => {
  try {
    const { transaction, amountNum, buy_item_id, creditToken } = req.body;
    console.log("creditToken", creditToken);
    const userId = req.user.userId;
    const tenantShopId = "s_24080757503";

    const id = transaction.id;
    const accessId = transaction.access_id;
    const data = { ...transaction };
    data.token = creditToken;
    console.log("data", data);
    const orderId = data.id;
    delete data.id;

    if (!id || !accessId) {
      return res.status(400).send("transaction.id and transaction.access_id are required");
    }

    const fincode = createFincode({ apiKey: process.env.FINCODE_API_KEY, isLiveMode: process.env.FINCODE_ENV === "development" ? false : true, options: { timeout: 10000 } });

    try {
      // 決済確定のリクエスト
      const authorizeResponse = await fincode.payments.execute(id, {
        pay_type: "Card",
        ...data,
        return_url: `${process.env.CORS_URL}/user/creditComp?authorizeResponseId=${orderId}&buy_item_id=${buy_item_id}`,
      });

      console.log("Payment executed:", authorizeResponse);

      // ペイメント情報を記録する
      await Payment.paymentInfoInsert(userId, authorizeResponse.id, authorizeResponse.amount, authorizeResponse.status, authorizeResponse.pay_type);

      Res.resJsonDataFn(res, 200, null, "Payment has been completed! Points will not be granted yet.", authorizeResponse.redirect_url);

      logUserPut(`ユーザー[${userId}]のクレジットカード決済実行が完了しました - controller_paymentController_runPayment`);
      logPaymentPut(`ユーザー[${userId}]のクレジットカード決済[${authorizeResponse.id}]の実行が完了しました。まだポイントは付与していません - controller_paymentController_runPayment`);
    } catch (error) {
      logErr(`ユーザー[${userId}]のクレジットカード決済実行エラー: ${error} - controller_paymentController_runPayment`);
      console.error("Error details:", error);
      if (error.response) {
        logErr(`ユーザー[${userId}]のクレジットカード決済実行エラー: ${error.response.data} - controller_paymentController_runPayment`);
        res.status(error.response.status).json(error.response.data);
      } else {
        logErr(`ユーザー[${userId}]のクレジットカード決済実行エラー: ${error} - controller_paymentController_runPayment`);
        res.status(500).json({ error: "An error occurred" });
      }
    }
  } catch (error) {
    logErr(`クレジットカード決済実行エラー: ${error} - controller_paymentController_runPayment`);
    console.log(error);
  }
};

/* --------------------------------------------------------------------- 
ユーザーの決済情報を取得し、ポイントを付与していない場合はポイントを付与する
-----------------------------------------------------------------------*/
exports.getPaymentInfoAndAddPoint = async (req, res) => {
  const userId = req.user ? req.user.userId : null;
  const { authorizeResponseId, buyItemId } = req.body;
  const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
  const fincode = createFincode({ apiKey: process.env.FINCODE_API_KEY, isLiveMode: process.env.FINCODE_ENV === "development" ? false : true, options: { timeout: 10000 } });

  let payment;

  try {
    // リクエストの送信
    payment = await retryOperation(
      () =>
        fincode.payments.retrieve(authorizeResponseId, {
          pay_type: "Card",
        }),
      3,
      1000
    );

    console.log("Payment details:", payment);

    // 決済ステータス
    const payStatus = payment.status;

    // 認証ステータス
    const authStatus = payment.tds2_status;

    if (payStatus === "CAPTURED" && authStatus === "AUTHENTICATED") {
      // ポイント付与したかどうかをチェック
      const pointAddedCheck = await Payment.checkPointAdded(authorizeResponseId);

      if (pointAddedCheck.length === 0) {
        // 該当購入アイテムの金額を取得
        const pointData = await BuyItem.getPoint(buyItemId);
        let point = pointData.point;

        // ユーザーのランクで付与するポイント率を取得
        const getBuyAddPointPercent = await User.getBuyAddPointPercent(userId);

        // ポイントに、ランク加算ポイントを追加
        point = point + point * (getBuyAddPointPercent.percent / 100);

        // 変更前のポイント
        const userPastPoint = await AdminUser.getUserPoint(userId);

        // ポイント更新のモデル関数を呼び出す
        const result = await User.updateUserPoints(userId, point, baseURL);

        // 充值金额
        const rechargeAmount = payment.amount;

        // 添加充值记录
        await UserTransaction.add(
          null,
          userId,
          "recharge",
          rechargeAmount,
          authorizeResponseId,
          "recharge success",
          null
        );

        // 更新用户总充值金额和VIP等级
        await UserExtension.updateRechargeAndVipLevel(userId, rechargeAmount);

        // 変更後のポイント
        const userNewPoint = await AdminUser.getUserPoint(userId);

        // クーポンコードを取得
        const couponCode = await discountCouponCode(userId, buyItemId);

        // クーポンコードを使用済みに変更
        if (couponCode) {
          logPaymentPut(`ユーザー[${userId}]がクーポンコード[${couponCode}]を使用しました - controller_paymentController_getPaymentInfoAndAddPoint`);
          await BuyItem.useCoupon(couponCode, userId);
        }

        // 自社ペイメント情報のポイント付与ステータスを
        await Payment.updateCreditCompStatus(authorizeResponseId);

        const responseData = {
          point: payment.total_amount,
        };

        // レスポンスを返す
        Res.resJsonDataFn(res, 200, null, "Points update completed successfully!", responseData);

        logUserPut(`ユーザー[${userId}]にクレジットカードでポイント[${point}]分の付与が完了しました - controller_paymentController_getPaymentInfoAndAddPoint`);
        logPaymentPut(`ユーザー[${userId}]にクレジットカード決済でポイント[${point}]分の付与が完了しました。ランクは[${getBuyAddPointPercent.rank}]。課金ボーナス率は[${getBuyAddPointPercent.percent}]。ポイントは[${formatNumber(userPastPoint)}]から[${formatNumber(userNewPoint)}]になりました。 - controller_paymentController_getPaymentInfoAndAddPoint`);
      } else {
        Res.resJsonDataFn(res, 422, null, "Points have already been granted", null);
        // エラーをスローする
        throw new Error("Points have already been granted.");
      }
    }
  } catch (e) {
    console.error("Error details:", e);
    logErr(`ユーザー[${userId}]のポイント付与エラー: ${e} - controller_paymentController_getPaymentInfoAndAddPoint`);
  }
};

/* --------------------------------------------------------------------- 
決済登録 -> 実行（銀行）
-----------------------------------------------------------------------*/
exports.addBankPayment = async (req, res) => {
  const request = require("request");
  const Bearer_API_KEY = `Bearer ${process.env.FINCODE_API_KEY}`;
  let BASE_URL = "";
  if (process.env.FINCODE_ENV === "development") {
    BASE_URL = "https://api.test.fincode.jp";
  } else {
    BASE_URL = "https://api.fincode.jp";
  }
  const endpoint = "/v1/payments";
  const userId = req.user.userId;
  const buy_item_id = req.body.buy_item_id;

  // クーポンの割引があるかチェック
  const discount = await discountCheck(userId, buy_item_id);

  // 該当購入アイテムの金額を取得
  const pointData = await BuyItem.getPoint(buy_item_id);
  const point = pointData.point;

  const DATA = {
    pay_type: "Virtualaccount",
    billing_amount: `${discount ? point - point * (discount / 100) : point}`,
    client_field_1: `${userId}`,
    client_field_2: `${buy_item_id}`,
  };

  const options = {
    url: BASE_URL + endpoint,
    headers: {
      "Content-Type": "application/json; charset=utf-8",
      Authorization: Bearer_API_KEY,
    },
    json: DATA,
  };

  main();

  const executePayment = async (data) => {
    const fincode = createFincode({ apiKey: process.env.FINCODE_API_KEY, isLiveMode: process.env.FINCODE_ENV === "development" ? false : true, options: { timeout: 10000 } });

    const orderId = data.id;
    const accessId = data.access_id;

    try {
      // リクエストの送信
      const payment = await fincode.payments.execute(orderId, {
        pay_type: "Virtualaccount",
        access_id: accessId,
      });
      console.log("Payment executed:", payment);
      await Payment.paymentInfoInsert(userId, payment.id, payment.billing_amount, payment.status, payment.pay_type);
      res.send(payment);
      logUserPut(`ユーザー[${userId}]の銀行決済実行が完了しました - controller_paymentController_addBankPayment`);
      logPaymentPut(`ユーザー[${userId}]の銀行決済[${payment.id}]の実行が完了しました - controller_paymentController_addBankPayment`);
    } catch (e) {
      logErr(`銀行決済登録エラー: ${e} - controller_paymentController_addBankPayment`);
      console.error("Error details:", e);
      res.status(500).json({ error: "The bank payment service is currently under maintenance. We apologize for the inconvenience." });
    }
  };

  function main() {
    request.post(options, (error, response, body) => {
      if (error) {
        console.error("リクエストエラー:", error.message);
        logErr(`銀行決済実行エラー: ${error.message} - controller_paymentController_addBankPayment`);
        return;
      }

      if (200 != response.statusCode) {
        console.log("ERROR");
        console.log(body);
        logErr(`銀行決済実行エラー: ${body} - controller_paymentController_addBankPayment`);
      } else {
        console.log("SUCCESS");
        console.log(body);
        executePayment(body);
        logUserPut(`ユーザー[${userId}]の銀行決済登録が完了しました - controller_paymentController_addBankPayment`);
      }
    });
  }
};

/* --------------------------------------------------------------------- 
fincode決済通知受け取り(銀行)
-----------------------------------------------------------------------*/
exports.getBankPaymentInfo = async (req, res) => {
  // リクエストボディを取得
  const data = req.body;
  try {
    // Fincode-Signatureヘッダーを取得
    const fincodeSignature = req.headers["fincode-signature"];

    // 登録時に設定した署名（signature）と比較
    const expectedSignature = process.env.FINCODE_WEB_HOOK_SECRET;

    if (fincodeSignature !== expectedSignature) {
      console.error("署名が一致しません");
      // リトライを行うために"1"を返却
      res.set("Content-Type", "text/plain");
      return res.status(200).send("1");
    }

    console.log("Fincodeからの通知を受け取りました:", data);
    const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;

    // 振込金額と登録時の金額が一致しているかチェック
    if (data.amount !== data.billing_amount) {
      console.error("振込金額と登録時の金額が一致しません");
      logErr(`銀行決済通知受け取りエラー: 振込金額と登録時の金額が一致しません - controller_paymentController_getBankPaymentInfo`);

      // リトライを行うために"1"を返却
      res.set("Content-Type", "text/plain");
      return res.status(200).send("1");
    }

    // 該当購入アイテムの金額を取得
    const pointData = await BuyItem.getPoint(data.client_field_2);
    let point = pointData.point;

    // ユーザーのランクで付与するポイント率を取得
    const getBuyAddPointPercent = await User.getBuyAddPointPercent(data.client_field_1);

    // ポイントに、ランク加算ポイントを追加
    point = point + point * (getBuyAddPointPercent.percent / 100);

    console.log("銀行point", point);

    // 更新前のポイント
    const userPastPoint = await AdminUser.getUserPoint(data.client_field_1);

    // await updatePaymentStatus(data)
    await User.updateUserPoints(data.client_field_1, point, baseURL);

    // 更新後のポイント
    const userNewPoint = await AdminUser.getUserPoint(data.client_field_1);

    // オーダーIDを保存する
    await Payment.paymentInfoInsert(data.client_field_1, data.order_id, data.amount, data.status, data.pay_type);

    // クーポンコードを取得
    const couponCode = await discountCouponCode(data.client_field_1, data.client_field_2);

    // クーポンコードを使用済みに変更
    if (couponCode) {
      logPaymentPut(`ユーザー[${data.client_field_1}]がクーポンコード[${couponCode}]を使用しました - controller_paymentController_getBankPaymentInfo`);
      await BuyItem.useCoupon(couponCode, data.client_field_1);
    }

    // 正常終了を通知するために"0"を返却
    res.set("Content-Type", "text/plain");
    logUserPut(`ユーザー[${data.client_field_1}]の銀行決済通知受け取りが完了し、ポイント[${point}]分を付与しました - controller_paymentController_getBankPaymentInfo`);
    logPaymentPut(`ユーザー[${data.client_field_1}]の銀行決済[${data.order_id}]の通知受け取りが完了しました。取得したポイントは[${formatNumber(point)}。ランクは[${getBuyAddPointPercent.rank}]。課金ボーナス率は[${getBuyAddPointPercent.percent}]。ポイントは[${formatNumber(userPastPoint)}]から[${formatNumber(userNewPoint)}]になりました。 - controller_paymentController_getBankPaymentInfo`);
    return res.status(200).send("0");
  } catch (error) {
    console.error("エラーが発生しました:", error);
    logErr(`ユーザー[${data.client_field_1}]の銀行決済通知受け取りエラー: ${error} - controller_paymentController_getBankPaymentInfo`);
    // リトライを行うために"1"を返却
    res.set("Content-Type", "text/plain");
    return res.status(200).send("1");
  }
};

/* --------------------------------------------------------------------- 
amazonPayの署名生成
-----------------------------------------------------------------------*/
exports.amazonPayCreateSignature = async (req, res) => {
  // Amazon Pay Clientの設定
  const config = {
    publicKeyId: process.env.AMAZON_PAY_PUBLIC_KEY,
    privateKey: fs.readFileSync(path.join(__dirname, "..", "amazon", process.env.AMAZON_PAY_PRIVATE_KEY), "utf8"),
    region: "jp",
    sandbox: process.env.FINCODE_ENV === "development" ? true : false,
  };

  // クライアントのインスタンスを生成
  const AmazonPayClient = new Client.AmazonPayClient(config);
  const buy_item_id = req.body.buy_item_id;
  console.log("amazonPayCreateSignatureのbuy_item_id", buy_item_id);
  const pointData = await BuyItem.getPoint(buy_item_id);
  const point = pointData.point;
  let amount = point;

  // クーポンの割引があるかチェック
  const discount = await discountCheck(req.user.userId, buy_item_id);

  if (discount) {
    amount = point - point * (discount / 100);
  }

  // 署名生成のためのペイロード設定
  const payload = {
    webCheckoutDetails: {
      checkoutReviewReturnUrl: `${process.env.CORS_URL}/user/creditAmazon?amount=${amount}&id=${buy_item_id}`,
    },
    storeId: process.env.AMAZON_PAY_STORE_ID,
    scopes: ["name", "email", "phoneNumber", "billingAddress"],
    deliverySpecifications: {
      specialRestrictions: ["RestrictPOBoxes"],
      addressRestrictions: {
        type: "Allowed",
        restrictions: {
          US: {
            statesOrRegions: ["WA"],
            zipCodes: ["95050", "93405"],
          },
          GB: {
            zipCodes: ["72046", "72047"],
          },
          IN: {
            statesOrRegions: ["AP"],
          },
          JP: {},
        },
      },
    },
  };

  try {
    const signature = AmazonPayClient.generateButtonSignature(payload);
    res.json({
      signature: signature,
      publicKeyId: config.publicKeyId,
      payload: payload,
    });
    logUserPut(`ユーザーID[${req.user.userId}]のAmazonPay署名生成が完了しました - controller_paymentController_amazonPayCreateSignature`);
    logPaymentPut(`ユーザーID[${req.user.userId}]のAmazonPay署名生成が完了しました - controller_paymentController_amazonPayCreateSignature`);
  } catch (error) {
    logErr(`ユーザーID[${req.user.userId}]のAmazonPay署名生成エラー: ${error} - controller_paymentController_amazonPayCreateSignature`);
    console.error("Error generating signature:", error);
    res.status(500).send("Internal Server Error");
  }
};

/* --------------------------------------------------------------------- 
amazonPayの支払い情報を取得
-----------------------------------------------------------------------*/
exports.amazonPayGetCheckoutSession = async (req, res) => {
  // amazonPayClientの設定
  const config = {
    publicKeyId: process.env.AMAZON_PAY_PUBLIC_KEY,
    privateKey: fs.readFileSync(path.join(__dirname, "..", "amazon", process.env.AMAZON_PAY_PRIVATE_KEY), "utf8"),
    region: "jp",
    sandbox: process.env.FINCODE_ENV === "development" ? true : false,
    algorithm: "AMZN-PAY-RSASSA-PSS-V2",
  };

  // クライアントのインスタンスを生成
  const amazonPayClient = new Client.WebStoreClient(config);

  // リクエストからセッションIDを取得
  const { sessionId } = req.body;

  // ユーザーIDを取得
  const userId = req.user.userId;

  try {
    // セッションIDを使って支払い情報を取得
    const response = await amazonPayClient.getCheckoutSession(sessionId);
    res.json(response.data);
    console.log("ゲットチェックアウトセッション:", response.data);
    logUserPut(`ユーザーID[${userId}]のAmazonPay支払い情報取得が完了しました - controller_paymentController_amazonPayGetCheckoutSession`);
    logPaymentPut(`ユーザーID[${userId}]のAmazonPay支払い情報取得が完了しました - controller_paymentController_amazonPayGetCheckoutSession`);
  } catch (error) {
    logErr(`ユーザーID[${userId}]のAmazonPay支払い情報取得エラー: ${error} - controller_paymentController_amazonPayGetCheckoutSession`);
    console.error("Error fetching Checkout Session:", error);
    res.status(500).send("Error fetching Checkout Session");
  }
};

/* --------------------------------------------------------------------- 
amazonPayの支払い情報を更新
-----------------------------------------------------------------------*/
exports.amazonPayUpdateCheckoutSession = async (req, res) => {
  // amazonPayClientの設定
  const config = {
    publicKeyId: process.env.AMAZON_PAY_PUBLIC_KEY,
    privateKey: fs.readFileSync(path.join(__dirname, "..", "amazon", process.env.AMAZON_PAY_PRIVATE_KEY), "utf8"),
    region: "jp",
    sandbox: process.env.FINCODE_ENV === "development" ? true : false,
    algorithm: "AMZN-PAY-RSASSA-PSS-V2",
  };

  const amazonPayClient = new Client.WebStoreClient(config);

  const { sessionId, buy_item_id } = req.body;
  console.log("amazonPayUpdateCheckoutSessionのbuy_item_id", buy_item_id);
  const pointData = await BuyItem.getPoint(buy_item_id);
  const point = pointData.point;
  let amount = point;

  // クーポンの割引があるかチェック
  const discount = await discountCheck(req.user.userId, buy_item_id);

  if (discount) {
    amount = point - point * (discount / 100);
  }

  const payload = {
    webCheckoutDetails: {
      checkoutResultReturnUrl: `${process.env.CORS_URL}/user/creditAmazonComp?amount=${amount}&id=${buy_item_id}`,
    },
    paymentDetails: {
      paymentIntent: "AuthorizeWithCapture",
      canHandlePendingAuthorization: false,
      chargeAmount: {
        amount: amount,
        currencyCode: "JPY",
      },
      merchantMetadata: {
        merchantReferenceId: crypto.randomUUID().toString().replace(/-/g, ""),
        merchantStoreName: "AmazonTestStoreFront",
        noteToBuyer: "merchantNoteForBuyer",
        customInformation: "",
      },
    },
  };

  try {
    const response = await amazonPayClient.updateCheckoutSession(sessionId, payload);
    res.json(response.data);
    console.log("アップデートチェックアウトセッション:", response.data);
    await Payment.paymentInfoInsertNoPromise(req.user.userId, response.data.checkoutSessionId, response.data.paymentDetails.chargeAmount.amount, response.data.statusDetails.state, "amazonPay");
    logUserPut(`ユーザーID[${req.user.userId}]のAmazonPay支払い情報更新が完了しました - controller_paymentController_amazonPayUpdateCheckoutSession`);
    logPaymentPut(`ユーザーID[${req.user.userId}]のAmazonPay支払い情報更新が完了しました - controller_paymentController_amazonPayUpdateCheckoutSession`);
  } catch (error) {
    logErr(`ユーザーID[${req.user.userId}]のAmazonPay支払い情報更新エラー: ${error} - controller_paymentController_amazonPayUpdateCheckoutSession`);
    console.error("Error fetching Checkout Session:", error);
    res.status(500).send("Error fetching Checkout Session");
  }
};

/* --------------------------------------------------------------------- 
amazonPayの支払い情報を完了
-----------------------------------------------------------------------*/
exports.amazonPayCompleteCheckoutSession = async (req, res) => {
  const config = {
    publicKeyId: process.env.AMAZON_PAY_PUBLIC_KEY,
    privateKey: fs.readFileSync(path.join(__dirname, "..", "amazon", process.env.AMAZON_PAY_PRIVATE_KEY), "utf8"),
    region: "jp",
    sandbox: process.env.FINCODE_ENV === "development" ? true : false,
    algorithm: "AMZN-PAY-RSASSA-PSS-V2",
  };

  const amazonPayClient = new Client.WebStoreClient(config);
  const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
  const { sessionId } = req.body;
  const userId = req.user.userId;
  const buy_item_id = req.body.buy_item_id;
  console.log("amazonPayCompleteCheckoutSessionのbuy_item_id", buy_item_id);
  const pointData = await BuyItem.getPoint(buy_item_id);
  const point = pointData.point;
  let addPoint = pointData.point;
  const amount = point;
  let amountNum = Number(point);

  // ユーザーのランクで付与するポイント率を取得
  const getBuyAddPointPercent = await User.getBuyAddPointPercent(userId);

  // ポイントに、ランク加算ポイントを追加
  addPoint = addPoint + addPoint * (getBuyAddPointPercent.percent / 100);

  // クーポンの割引があるかチェック
  const discount = await discountCheck(userId, buy_item_id);

  if (discount) {
    amountNum = amount - amount * (discount / 100);
  }

  const payload = {
    chargeAmount: {
      amount: amountNum,
      currencyCode: "JPY",
    },
  };

  let sessionResponse;
  let response;

  try {
    // まず、セッションの現在の状態を取得する
    sessionResponse = await retryOperation(
      () => amazonPayClient.getCheckoutSession(sessionId),
      3, // リトライ回数
      1000 // 初回の遅延（ミリ秒）
    );

    const sessionState = sessionResponse.data.statusDetails.state;

    // セッションが「Completed」状態であれば処理を終了
    if (sessionState === "Completed") {
      return res.status(400).json({ message: "This checkout session is already completed." });
    }

    // セッションが未完了の場合のみ、支払い完了処理を実行
    response = await retryOperation(
      () => amazonPayClient.completeCheckoutSession(sessionId, payload),
      3, // リトライ回数
      1000 // 初回の遅延（ミリ秒）
    );

    console.log("Complete Checkout Session:", response.data);

    // ペイメント情報を記録する
    await Payment.paymentInfoInsertNoPromise(req.user.userId, response.data.chargePermissionId, amountNum, response.data.statusDetails.state, "amazonPay");

    // 更新前のポイント
    const userPastPoint = await AdminUser.getUserPoint(userId);

    // ポイント更新
    const result = await User.updateUserPoints(userId, addPoint, baseURL);

    // 更新後のポイント
    const userNewPoint = await AdminUser.getUserPoint(userId);

    // クーポンコードを取得
    const couponCode = await discountCouponCode(userId, buy_item_id);

    // クーポンコードを使用済みに変更
    if (couponCode) {
      logPaymentPut(`ユーザー[${userId}]がクーポンコード[${couponCode}]を使用しました - controller_paymentController_amazonPayCompleteCheckoutSession`);
      await BuyItem.useCoupon(couponCode, userId);
    }

    const data = {
      state: response.data.statusDetails.state,
      ...result,
    };

    res.json(data);
    logUserPut(`ユーザーID[${userId}]のAmazonPay支払いが完了し、ポイント[${addPoint}]分を付与しました - controller_paymentController_amazonPayCompleteCheckoutSession`);
    logPaymentPut(`ユーザーID[${userId}]のAmazonPay支払いが完了し、ポイント[${formatNumber(addPoint)}]分を付与しました。ランクは[${getBuyAddPointPercent.rank}]。課金ボーナス率は[${getBuyAddPointPercent.percent}]。ポイントは[${formatNumber(userPastPoint)}]から[${formatNumber(userNewPoint)}]になりました。 - controller_paymentController_amazonPayCompleteCheckoutSession`);
  } catch (error) {
    logErr(`ユーザーID[${userId}]のAmazonPay支払い情報完了エラー: ${error} - controller_paymentController_amazonPayCompleteCheckoutSession`);
    console.error("Error completing Checkout Session:", error);
    res.status(500).send("Error completing Checkout Session");
  }
};
