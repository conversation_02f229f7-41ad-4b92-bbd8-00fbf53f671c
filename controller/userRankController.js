const User = require("../model/user");
const { logAdminPut, logErr, logUserPut } = require("../logger");
const db = require("../db/db");

/* ---------------------------------------------------------------------
ユーザーのランクを取得するコントローラー
-----------------------------------------------------------------------*/
exports.getUserRank = async (req, res) => {
  const userId = req.user ? req.user.userId : null;
  try {
    // const userRank = await User.getUserRank(userId, baseURL);
    const userRank = await User.getUserRank(userId, '');
    res.status(200).json(userRank);
  } catch (err) {
    console.error("Error fetching user rank:", err);
    logErr(`ユーザー[${userId}]のランクを取得できませんでした。エラー内容:${err} - controller_userRankController_getUserRank`);
    res.status(500).send("Failed to fetch user rank");
  }
};

/* ---------------------------------------------------------------------
ユーザーのランクに基づいた、アイテム課金時のボーナスポイント率を取得するコントローラー
-----------------------------------------------------------------------*/
exports.getBuyBonus = async (req, res) => {
  const userId = req.user ? req.user.userId : null;

  try {
    const buyBonus = await User.getBuyAddPointPercent(userId);
    res.status(200).json(buyBonus);
  } catch (err) {
    console.error("Error fetching buy bonus:", err);
    logErr(`ユーザー[${userId}]のランクに基づいた、アイテム課金時のボーナスポイント率を取得できませんでした。エラー内容:${err} - controller_userRankController_getBuyBonus`);
    res.status(500).send("Failed to fetch buy bonus");
  }
};

const AWS = require("aws-sdk");

// 初始化 AWS S3 实例
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID,
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
  region: process.env.AWS_REGION || "us-east-1",
});


// 👇 adminAddRank 方法开始
exports.adminAddRank = async (req, res) => {
  try {
    const ranks = [];

    // 获取所有字段名
    const bodyKeys = Object.keys(req.body);
    const fileKeys = req.files ? req.files.map(file => file.fieldname) : [];

    // 找出所有唯一的 rank 编号（如 rank1, rank2）
    const rankNumbers = new Set();

    [...bodyKeys, ...fileKeys].forEach(key => {
      const match = key.match(/^rank(\d+)_/); // 匹配 rank1_xxx
      if (match && match[1]) {
        rankNumbers.add(parseInt(match[1], 10));
      }
    });

    // 对 rank 编号排序并处理
    const sortedRanks = Array.from(rankNumbers).sort((a, b) => a - b);

    for (const num of sortedRanks) {
      const prefix = `rank${num}`;

      // 提取该 rank 的字段
      const name = req.body[`${prefix}_name`];
      const point = parseInt(req.body[`${prefix}_point`], 10);
      const gpPercent = parseFloat(req.body[`${prefix}_gpPercent`]);
      const bonusPoint = parseInt(req.body[`${prefix}_bonusPoint`], 10);
      const order = parseInt(req.body[`${prefix}_order`], 10);
      const buyAddPointPercent = parseInt(req.body[`${prefix}_buyAddPointPercent`], 10);

      // ✅ 只要 name 不存在，就跳过整个 rank
      if (!name) continue;

      // 🔍 查找对应的图片文件
      const backImgFile = req.files.find(file => file.fieldname === `${prefix}_backImg`);

      let backImgUrl = null;
      if (backImgFile) {
        const params = {
          Bucket: process.env.AWS_BUCKET_NAME,
          Key: `${Date.now()}-${backImgFile.originalname}`,
          Body: backImgFile.buffer,
          ContentType: backImgFile.mimetype,
        };

        const s3Result = await s3.upload(params).promise();
        backImgUrl = s3Result.Location;
      }

      // ✅ 构造 rank 对象
      ranks.push({
        name,
        point,
        gp: gpPercent,
        bonus: bonusPoint,
        back_img: backImgUrl,
        order,
        buy_add_point_percent: buyAddPointPercent
      });
    }

    // ✅ 插入数据库（使用 Promise.all 并行上传）
    const insertQuery = `
      INSERT INTO \`rank\` (
        name,
        point,
        gp,
        bonus,
        back_img,
        \`order\`,
        buy_add_point_percent
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    await Promise.all(
        ranks.map(rank =>
            new Promise((resolve, reject) => {
              db.query(
                  insertQuery,
                  [
                    rank.name,
                    rank.point,
                    rank.gp,
                    rank.bonus,
                    rank.back_img,
                    rank.order,
                    rank.buy_add_point_percent
                  ],
                  (err, result) => {
                    if (err) return reject(err);
                    resolve(result);
                  }
              );
            })
        )
    );

    return res.status(200).json({ message: "Ranks added successfully" });
  } catch (error) {
    console.error("🚫 添加 Rank 失败:", error);
    return res.status(500).json({ error: "Internal Server Error" });
  }
};
