const User = require("../model/user");
const Setting = require("../model/setting");
const UserItem = require("../model/userItem");
const Res = require("../util/responseHelpers");
const axios = require("axios");
const bcrypt = require("bcrypt");
const saltRounds = 10;
const twilio = require("twilio");
require("dotenv").config();
const jwt = require("jsonwebtoken");
const {logUserPut, logErr} = require("../logger");
const createTransporter = require("../service/mailService");
const { getInviterIdByCode, recordInvite } = require("../service/referralService");

const secretKey = process.env.USER_JWT_SECRET_KEY;
const {OAuth2Client} = require("google-auth-library");
const db = require("../db/db");
const {getConnection} = require("mysql/lib/Pool");

const client = new OAuth2Client(process.env.GOOGLE_CLIENT_ID);

/* ---------------------------------------------------------------------
電話番号認証コード送信
-----------------------------------------------------------------------*/
exports.telAuthSendCode = async (req, res) => {
    try {
        const client = new twilio(process.env.TWILIO_SID, process.env.TWILIO_TOKEN);
        const TWILIO_FROM = process.env.TWILIO_FROM;
        const serviceName = await Setting.getServiceName();

        function formatPhoneNumber(phoneNumber) {
            if (!phoneNumber.startsWith("+")) {
                return null;
            }
            return phoneNumber.replace(/\s+/g, "");
        }

        async function sendSMS(code, phoneNumber) {
            try {
                const message = await client.messages.create({
                    body: `${serviceName ? serviceName[0].service_name : "Service"} authentication code: ${code}`,
                    from: TWILIO_FROM,
                    to: phoneNumber,
                });
                console.log(`Message sent: ${message.sid}`);
                return message.sid;
            } catch (error) {
                console.error(`Failed to send SMS: ${error}`);
                logErr(`SMS sending failed. Error: ${error} - controller_authController_telAuthSendCode`);
                throw error;
            }
        }

        function generateNumericCode(length = 6) {
            return Math.floor(100000 + Math.random() * 900000).toString();
        }

        const {phoneNumber} = req.body;
        const formattedPhoneNumber = formatPhoneNumber(phoneNumber);
        if (!formattedPhoneNumber) {
            return Res.resJsonFn(res, 400, null, "Invalid phone number format. Please enter a valid international number.");
        }

        const normalizedPhoneNumber = formattedPhoneNumber.replace(/\D/g, "").slice(-10);

        const phoneExists = await User.telCheck(normalizedPhoneNumber);
        if (phoneExists.length > 0) {
            return Res.resJsonFn(res, 400, null, "This phone number is already in use.");
        }

        const code = generateNumericCode();
        // 将验证码存储到数据库
        const insertQuery = 'INSERT INTO verification_codes (phone, code, type, expires_at) VALUES (?, ?, ?, DATE_ADD(NOW(), INTERVAL 10 MINUTE))';
        db.query(insertQuery, [phoneNumber, code, 'sms'], (insertErr, result) => {
            if (insertErr) {
                console.error("sms Verification Code Insert Error:", insertErr);
                return res.status(500).json({message: "Failed to save sms verification code"});
            }
            console.log("Verification code stored successfully.", result);
        });

        await sendSMS(code, formattedPhoneNumber);
        return Res.resJsonFn(res, 200, null, "The sms authentication code has been sent.");
    } catch (error) {
        console.error("Error in phone authentication:", error);
        return Res.resJsonFn(res, 500, null, "A server error occurred during processing.");
    }
};

/* ---------------------------------------------------------------------
電話番号認証
-----------------------------------------------------------------------*/
/*exports.telAuth = async (req, res) => {
    const userId = req.user.userId;

    const {code, phoneNumber} = req.body;
    if (!code) {
        return res.status(400).json({message: "Verification code is required."});
    }
    if (req.session.code && code === req.session.code) {
        try {
            // 電話番号を登録する
            const telAuth = User.telAuth(phoneNumber, userId, res);
            Res.resJsonFn(res, 200, null, "Phone number registration is complete!");
        } catch (err) {
            logErr(`電話番号の認証ができませんでした。エラー内容:${err} - controller_authController_telAuth`);
            console.error("Error during registration process:", err);
            Res.resJsonFn(res, 500, null, "Failed to register the phone number.");
        }
    } else {
        res.status(400).json({message: "Invalid verification code or session expired."});
    }
};*/

exports.telAuth = async (req, res) => {
    const userId = req.user.userId;

    const {code, phoneNumber} = req.body;
    if (!code) {
        return res.status(400).json({message: "sms Verification code is required."});
    }

    console.log("Received sms code:", code);

    try {
        // 从数据库中获取验证码记录
        const result = await new Promise((resolve, reject) => {
            db.query(
                "SELECT * FROM verification_codes WHERE phone = ? AND type = 'sms' AND expires_at > NOW()",
                [phoneNumber],
                (err, results) => {
                    if (err) {
                        console.error("Database query error:", err);
                        return reject(err);
                    }
                    console.log("Query results:", results);
                    resolve(results);
                }
            );
        });

        if (result.length === 0) {
            return res.status(404).json({message: "No valid verification code found for the provided phone."});
        }
        const storedCode = result[0].code;

        // 验证验证码是否匹配
        if (code !== storedCode) {
            return res.status(400).json({message: "The sms authentication code is incorrect."});
        }

        // 验证成功后清除验证码
        await db.query("DELETE FROM verification_codes WHERE phone = ? AND type = 'sms'", [phoneNumber]);

        // 電話番号を登録する
        const telAuth = User.telAuth(phoneNumber, userId, res);
        Res.resJsonFn(res, 200, null, "Phone number registration is complete!");

    } catch (e) {
        logErr(`電話番号の認証ができませんでした。エラー内容:${err} - controller_authController_telAuth`);
        console.error("Error during registration process:", err);
        Res.resJsonFn(res, 500, null, "Failed to register the phone number.");
        // res.status(400).json({message: "Invalid verification code or session expired."});

    }

}
/* ---------------------------------------------------------------------
Google Authentication Handler
-----------------------------------------------------------------------*/
exports.googleAuth = (req, res) => {
    console.log("Google Auth Called");

    try {
        const {credential} = req.body;

        if (!credential) {
            return res.status(400).json({message: "Google credential is missing."});
        }

        client.verifyIdToken({
            idToken: credential,
            audience: process.env.GOOGLE_CLIENT_ID,
        })
            .then((ticket) => {
                const payload = ticket.getPayload();
                const googleId = payload.sub;
                const email = payload.email;
                const name = payload.name;
                const profileImage = payload.picture;

                console.log("Google Auth Verified:", payload);

                db.query("SELECT * FROM user WHERE mail = ?", [email], (err, rows) => {
                    if (err) {
                        console.error("Database Query Error:", err);
                        return res.status(500).json({message: "Internal Server Error"});
                    }

                    let user = rows.length > 0 ? rows[0] : null;

                    if (!user) {
                        const defaultValues = {
                            pass: "",
                            address: "",
                            point: 0,
                            gp: 0,
                            tel: "",
                            zip_code: "",
                            pref: "",
                            kana: "",
                            day_point: 0,
                            weekly_point: 0,
                            month_point: 0,
                            reset_password_token: null,
                            reset_password_expires: null,
                            allPoint: 0,
                            rank: 0,
                            last_bonus_claim_date: null,
                            use_point: 0,
                            display: "visible",
                            city: "",
                            country: "",
                        };

                        const insertQuery = `
                            INSERT INTO user (name, mail, pass, address, point, gp, tel, zip_code, pref, kana,
                                              day_point,
                                              weekly_point, month_point, reset_password_token,
                                              reset_password_expires,
                                              allPoint, \`rank\`,
                                              last_bonus_claim_date, use_point, display, city, country)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        `;

                        db.query(insertQuery, [
                            name, email,
                            defaultValues.pass, defaultValues.address, defaultValues.point, defaultValues.gp,
                            defaultValues.tel, defaultValues.zip_code, defaultValues.pref, defaultValues.kana,
                            defaultValues.day_point, defaultValues.weekly_point, defaultValues.month_point,
                            defaultValues.reset_password_token, defaultValues.reset_password_expires,
                            defaultValues.allPoint, defaultValues.rank, defaultValues.last_bonus_claim_date,
                            defaultValues.use_point, defaultValues.display, defaultValues.city, defaultValues.country
                        ], (insertErr, result) => {
                            if (insertErr) {
                                console.error("User Insert Error:", insertErr);
                                return res.status(500).json({message: "User creation failed"});
                            }

                            db.query("SELECT * FROM user WHERE mail = ?", [email], (fetchErr, newUserRows) => {
                                if (fetchErr) {
                                    console.error("User Fetch Error:", fetchErr);
                                    return res.status(500).json({message: "Error retrieving user"});
                                }

                                let user = newUserRows.length > 0 ? newUserRows[0] : null;
                                if (!user) {
                                    return res.status(500).json({message: "User creation failed"});
                                }

                                sendAuthResponse(user, res);
                            });
                        });
                    } else {
                        sendAuthResponse(user, res);
                    }
                });
            })
            .catch((error) => {
                console.error("Google Auth Error:", error);
                res.status(500).json({message: "Internal Server Error"});
            });
    } catch (error) {
        console.error("Google Auth Error:", error);
        res.status(500).json({message: "Internal Server Error"});
    }
};

const sendAuthResponse = (user, res) => {
    const token = jwt.sign({userId: user.id}, process.env.USER_JWT_SECRET_KEY, {
        expiresIn: "7d",
    });

    // res.cookie("token", token, {
    //   httpOnly: true,
    //   secure: true,
    //   sameSite: "Lax"
    // });
    res.cookie("oripa-user-token", token, {
        httpOnly: true,
        secure: true, // 仅 HTTPS
        sameSite: "None", // 跨域必须为 None
        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 天
        path: "/",
    });

    // レスポンスを返す前に確認
    console.log("Cookie set:", res.get("Set-Cookie"));


    return res.json({
        message: "Google authentication successful!",
        token,
        user: {
            id: user.id,
            name: user.name,
            email: user.mail,
        }
    });
};


/* ---------------------------------------------------------------------
LINEログインで使うステートをセッションに登録
-----------------------------------------------------------------------*/
exports.lineStateAdd = async (req, res) => {
    try {
        // req.body.lineState が配列の場合はリターン
        if (Array.isArray(req.body.lineState)) {
            Res.resJsonFn(res, 400, null, "The data is invalid.");
            return;
        }

        const stateCheck = await User.checkLineState(req.body.lineState);
        if (stateCheck.length > 0) {
            Res.resJsonFn(res, 400, null, "This state has already been used.");
            return;
        } else {
            await User.insertLineState(req.body.lineState);
        }
        req.session["line_login_state_" + req.body.lineState] = req.body.lineState;
        req.session.save((err) => {
            if (err) {
                console.error("セッション保存エラー:", err);
                logErr(`LINEログインにかかるセッションへのデータ登録ができませんでした。エラー内容:${err} - controller_authController_lineStateAdd`);
                res.status(500).send("Failed to save session.");
            } else {
                console.log("セッションへのデータ登録:", req.session["line_login_state_" + req.body.lineState]);
                Res.resJsonFn(res, 200, null, "The state has been registered.");
            }
        });
    } catch (error) {
        res.status(400).json({message: "No data available."});
        console.log(error);
    }
};

/* ---------------------------------------------------------------------
LINEログインのステートがセッションに登録されているかを確認の上、DBに登録
-----------------------------------------------------------------------*/
exports.lineStatusAuth = async (req, res) => {
    const {authCode, lineAuthState, lpItemId, type} = req.body;
    const sessionKey = "line_login_state_" + lineAuthState;
    if (Array.isArray(lineAuthState)) {
        Res.resJsonFn(res, 400, null, "The data is invalid.");
        return;
    }
    const stateKey = await User.checkLineState(lineAuthState);
    console.log("DBに登録されているステート", stateKey);
    console.log("セッションキー:", sessionKey);
    console.log("セッション値:", req.session[sessionKey]);
    if (lpItemId) {
        console.log("LPアイテムID:", lpItemId);
    }

    let redirectUri = "";

    if (type === "signUp") {
        redirectUri = lpItemId ? process.env.LINE_REDIRECT_SIGNUP_ID + `?lp_item_id=${lpItemId}` : process.env.LINE_REDIRECT_SIGNUP_ID;
    } else {
        redirectUri = process.env.LINE_REDIRECT_ID;
    }

    if (stateKey.length > 0) {
        const url = "https://api.line.me/oauth2/v2.1/token";
        const data = {
            grant_type: "authorization_code",
            code: authCode,
            client_id: process.env.LINE_CLIENT_ID,
            redirect_uri: redirectUri,
            client_secret: process.env.LINE_SECRET_ID,
        };

        try {
            const response = await axios.post(url, data, {
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                },
            });

            const id_token = response.data.id_token;

            if (id_token) {
                const verifyData = {
                    id_token: id_token,
                    client_id: process.env.LINE_CLIENT_ID,
                };

                try {
                    // id_tokenの認証
                    const verifyResponse = await axios.post("https://api.line.me/oauth2/v2.1/verify", verifyData, {
                        headers: {
                            "Content-Type": "application/x-www-form-urlencoded",
                        },
                    });

                    // LINEのレスポンスからメールアドレスを取得
                    const email = verifyResponse.data.email;

                    if (!email) {
                        logErr(`LINEログインに失敗しました。エラー内容:メールアドレスが取得できませんでした。 - controller_authController_lineStatusAuth`);
                        return res.status(400).json({message: "Please add your email address to the LINE app."});
                    }

                    // ユーザーが既に存在するかを確認
                    const results = await User.checkEmailDb(email);

                    if (results.length > 0) {
                        const user = results[0];
                        const token = jwt.sign({
                            userId: user.id,
                            mail: user.mail
                        }, process.env.USER_JWT_SECRET_KEY, {expiresIn: "30d"});
                        res.cookie("oripa-user-token", token, {
                            httpOnly: true,
                            secure: process.env.NODE_ENV === "production",
                            maxAge: 2592000000,
                            sameSite: "Strict",
                        });
                        res.status(200).json({token});
                    } else {
                        // ユーザーが存在しない場合、新しいユーザーを挿入
                        const {userId, token} = await User.userInsert(email, "line");

                        let lpItem = [];
                        let lpItemCheck = [];

                        // ベースURLを設定
                        const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host") + "/"}`;

                        // lpItemが存在するか確認
                        if (lpItemId) {
                            lpItem = await UserItem.getLpItem(lpItemId);
                        }

                        // LPアイテムがすでに該当ユーザーに取得されているかの確認
                        if (lpItemId) {
                            lpItemCheck = await UserItem.checkLpItem(userId);
                        }

                        // LPアイテムが存在する場合
                        if (lpItem.length > 0 && lpItemCheck.length === 0) {
                            // ユーザーにLPアイテムを付与
                            const lpItemData = lpItem[0];
                            const lpItemAddRes = await UserItem.addLpItem(userId, lpItemData.id, lpItemData.name, lpItemData.price, baseURL + lpItemData.image);
                            console.log(lpItemAddRes);
                        }

                        // クッキーの設定
                        res.cookie("oripa-user-token", token, {
                            httpOnly: true,
                            secure: process.env.NODE_ENV === "production",
                            maxAge: 2592000000,
                            sameSite: "Strict",
                        });

                        res.status(200).json({token});
                    }

                    await User.deleteLineState(lineAuthState);
                } catch (error) {
                    logErr(`LINEログインに失敗しました。エラー内容:${error} - controller_authController_lineStatusAuth`);
                    res.status(500).json({error: "", message: ""});
                }
            }
        } catch (error) {
            if (error.response) {
                logErr(`LINEログインに失敗しました。エラー内容:${error.response.data} - controller_authController_lineStatusAuth`);
                console.error("Error status:", error.response.status);
                console.error("Error data:", error.response.data);
            } else {
                logErr(`LINEログインに失敗しました。エラー内容:${error.message} - controller_authController_lineStatusAuth`);
                console.error("Error:", error.message);
            }
            logErr(`LINEログインに失敗しました。エラー内容:${error} - controller_authController_lineStatusAuth`);
            if (!res.headersSent) {
                res.status(500).send("Failed to log in with LINE.");
            }
        }
    } else {
        if (!res.headersSent) {
            res.status(400).send("Failed to log in with LINE.");
        }
        console.log("呼び出されたステート:", lineAuthState);
    }
};

/* ---------------------------------------------------------------------
メール認証コード送信
-----------------------------------------------------------------------*/
exports.mailAuthSendCode = async (req, res) => {
    // メール取得
    const {mail} = req.body;

    function generateNumericCode(length) {
        let code = "";
        for (let i = 0; i < length; i++) {
            code += Math.floor(Math.random() * 10).toString();
        }
        return code;
    }

    try {
        const emailExists = await User.checkEmailDb(mail);

        if (emailExists.length > 0) {
            return res.status(400).json({message: "This email address is already in use."});
        }

        // セッションに認証コードを設定
        const code = generateNumericCode(4);
        console.log("验证码是:", code)

        const serviceName = await Setting.getServiceName();

        // メール送信関数
        const sendMail = async (mail, code) => {
            // メール内容設定
            const userMailOptions = {
                from: process.env.SMTP_USER,
                to: mail,
                subject: `${serviceName ? serviceName[0].service_name : "Oripa"}: Verification Code "${code}"`,
                text: `Thank you for registering with ${serviceName ? serviceName[0].service_name : "noName"}. Your email verification code is "${code}".`,
            };

            try {
                const transport = createTransporter();
                await transport.sendMail(userMailOptions);
            } catch (error) {
                console.log(error);
                throw new Error("Failed to send email.");
            }
        };

        // メール送信関数実行
        // await sendMail(mail, code);

        // 将验证码存储到数据库
        const insertQuery = 'INSERT INTO verification_codes (email, code, type, expires_at) VALUES (?, ?, ?, DATE_ADD(NOW(), INTERVAL 10 MINUTE))';
        db.query(insertQuery, [mail, code, 'email'], (insertErr, result) => {
            if (insertErr) {
                console.error("Verification Code Insert Error:", insertErr);
                return res.status(500).json({message: "Failed to save verification code"});
            }
            console.log("Verification code stored successfully.", result);
        });

        Res.resJsonFn(res, 200, null, `An authentication code has been sent to ${mail}`);
        logUserPut(`Verification code sent successfully. - send-code`);
    } catch (error) {
        logErr(`Failed to send verification code. Error: ${error} - controller_authController_mailAuthSendCode`);
        Res.resJsonFn(res, 500, null, `A server error occurred during processing.`);
    }
};


exports.mailAuth = async (req, res) => {
    const {code, mail, pass, referralCode} = req.body;

    // 检查验证码是否为空
    if (!code) {
        return res.status(400).json({message: "Verification code is required."});
    }

    // 检查邮箱是否已注册
    const emailExists = await User.checkEmailDb(mail);
    if (emailExists.length > 0) {
        return res.status(400).json({message: "Email already exists."});
    }

    console.log("Received code:", code);

    try {
        // 从数据库中获取验证码记录
        const result = await new Promise((resolve, reject) => {
            db.query(
                "SELECT * FROM verification_codes WHERE email = ? AND type = 'email' AND expires_at > NOW()",
                [mail],
                (err, results) => {
                    if (err) {
                        console.error("Database query error:", err);
                        return reject(err);
                    }
                    console.log("Query results:", results);
                    resolve(results);
                }
            );
        });

        if (result.length === 0) {
            return res.status(404).json({message: "No valid verification code found for the provided email."});
        }
        const storedCode = result[0].code;

        // 验证验证码是否匹配
        if (code !== storedCode) {
            return res.status(400).json({message: "The authentication code is incorrect."});
        }

        // 验证成功后清除验证码
        await db.query("DELETE FROM verification_codes WHERE email = ? AND type = 'email'", [mail]);

        // 哈希化密码
        const hashedPassword = await bcrypt.hash(pass, saltRounds);

        // 将用户信息插入数据库
        const {userId} = await User.userInsert(mail, hashedPassword);
        // 转介绍处理
        // 在注册流程中（不需要 await）
        if (referralCode) {
            // 启动异步任务，不等待结果
            (async () => {
                try {
                    const inviterId = await getInviterIdByCode(referralCode);
                    if (inviterId) {
                        await recordInvite(inviterId, userId, referralCode);
                    }
                } catch (error) {
                    console.error("邀请码处理过程中发生错误，不影响主流程", error);
                    // 日志已由 service 层记录，这里可选择不再重复记录
                }
            })();
        }

        // 返回成功响应
        res.status(200).json({userId});
        logUserPut(`User registered successfully. - mailAuth`);
    } catch (err) {
        console.error("Error during registration process:", err);
        logErr(`Failed to register user in the database. Error: ${err} - controller_authController_mailAuth`);
        res.status(500).json({message: "Server error during registration."});
    }
};


/* ---------------------------------------------------------------------
メール認証
-----------------------------------------------------------------------*/
// exports.mailAuth = async (req, res) => {
//   const { code, mail, pass, lpItemId } = req.body;
//   if (!code) {
//     return res.status(400).json({ message: "Verification code is required." });
//   }
//
//   console.log("送られてきたコード:" + code);
//   console.log("セッションのコード:" + req.session.code);
//
//   if (req.session.code && code === req.session.code) {
//     try {
//       // ユーザーが存在するか確認
//       const emailExists = await User.checkEmailDb(mail);
//
//       if (emailExists.length > 0) {
//         return res.status(400).json({ message: "Email already exists." });
//       }
//
//       // ベースURLを設定
//       const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host") + "/"}`;
//
//       let lpItem = [];
//       let lpItemCheck = [];
//
//       // lpItemが存在するか確認
//       if (lpItemId) {
//         lpItem = await UserItem.getLpItem(lpItemId);
//       }
//
//       // パスワードをハッシュ化
//       const hashedPassword = await bcrypt.hash(pass, saltRounds);
//
//       // ユーザー情報をデータベースに登録
//       const { userId } = await User.userInsert(mail, hashedPassword);
//
//       // LPアイテムがすでに該当ユーザーに取得されているかの確認
//       if (lpItemId) {
//         lpItemCheck = await UserItem.checkLpItem(userId);
//       }
//
//       // LPアイテムが存在する場合
//       if (lpItem.length > 0 && lpItemCheck.length === 0) {
//         // ユーザーにLPアイテムを付与
//         const lpItemData = lpItem[0];
//         const lpItemAddRes = await UserItem.addLpItem(userId, lpItemData.id, lpItemData.name, lpItemData.price, baseURL + lpItemData.image);
//         console.log(lpItemAddRes);
//       }
//
//       res.status(200).json({ userId });
//     } catch (err) {
//       logErr(`Failed to register user in the database. Error: ${err} - controller_authController_mailAuth`);
//       console.error("Error during registration process:", err);
//       res.status(500).json({ message: "Server error during registration." });
//     }
//   } else {
//     res.status(400).json({ message: "The authentication code is incorrect." });
//     logUserPut(`Failed to register user in the database.`);
//   }
// };

/* ---------------------------------------------------------------------
通常ログイン
-----------------------------------------------------------------------*/
exports.userLogin = async (req, res) => {
    const {mail, pass} = req.body;

    console.log("Login information:", mail, pass);

    try {
        // ユーザーが存在するか確認
        const emailExists = await User.checkEmailDb(mail);

        if (emailExists) {
            const user = emailExists[0];

            if (user) {
                bcrypt.compare(pass, user.pass, (err, isMatch) => {
                    if (err) {
                        return res.status(500).json({message: "An error occurred during password verification."});
                    }

                    if (!isMatch) {
                        return res.status(401).json({message: "Invalid email address or password."});
                    }

                    // JWTトークンの生成
                    const token = jwt.sign({userId: user.id, mail: user.mail}, secretKey, {expiresIn: "30d"});

                    // クッキーにトークンをセットしてレスポンスを返す
                    // res.cookie("oripa-user-token", token, {
                    //   // httpOnly: true,
                    //   // secure: process.env.NODE_ENV === "production",
                    //   maxAge: 2592000000,
                    //   sameSite: "None",
                    //   secure: true,
                    //   // sameSite: "Strict",
                    //   path: "/"
                    // });


                    // 将 refreshToken 存入 HttpOnly Cookie

                    res.cookie("oripa-user-token", token, {
                        httpOnly: true,
                        secure: true, // 仅 HTTPS
                        sameSite: "None", // 跨域必须为 None
                        maxAge: 30 * 24 * 60 * 60 * 1000, // 30 天
                        path: "/",
                    });

                    // レスポンスを返す前に確認
                    console.log("Cookie set:", res.get("Set-Cookie"));


                    res.status(200).json({token});
                    logUserPut(`User logged in successfully. - login`);
                });
            } else {
                res.status(400).json({message: "Login failed."});
                throw new Error("User not found.");
            }
        }
    } catch (error) {
        logErr(`Failed to log in. Error: ${error} - controller_authController_userLogin`);
        console.log(error);
    }
};

