const BuyItem = require("../model/userBuyItem");
const { logAdminPut, logErr } = require("../logger");
const Res = require("../util/responseHelpers");

/* --------------------------------------------------------------------- 
課金アイテム取得
-----------------------------------------------------------------------*/
exports.getBuyItems = async (req, res) => {
  try {
    const buyItems = await BuyItem.getBuyItems();
    Res.resJsonDataFn(res, 200, "", "Paid items have been retrieved", buyItems);
  } catch (err) {
    logErr(`課金アイテム取得エラー: ${err} controller_userBuyItem_getBuyItems`);
  }
};

/* --------------------------------------------------------------------- 
該当ユーザーが所持している課金アイテムのクーポンを取得
-----------------------------------------------------------------------*/
exports.getCoupon = async (req, res) => {
  try {
    const userId = req.user.userId;
    console.log(userId);

    // クーポンが存在するかチェック
    const coupon_code = await BuyItem.getCoupon(userId);
    if (!coupon_code) {
      console.log("クーポンが存在しません");
      Res.resJsonDataFn(res, 400, "Coupon does not exist", "Coupon retrieval error");
      return;
    }

    // クーポンが使用されているかチェック
    const couponCheck = await BuyItem.checkCoupon(coupon_code.buy_code, userId);
    if (couponCheck.length === 0) {
      const coupons = await BuyItem.getCouponByCode(coupon_code.buy_code);
      Res.resJsonDataFn(res, 200, "", "Coupon has been retrieved", coupons);
    } else {
      // エラー処理
      Res.resJsonDataFn(res, 400, "The coupon has already been used", "Coupon retrieval error");
      return;
    }
  } catch (err) {
    console.log(err);
    logErr(`クーポン取得エラー: ${err} controller_userBuyItem_getCoupon`);
    res.status(500).json({ message: "An error has occurred." });
  }
};