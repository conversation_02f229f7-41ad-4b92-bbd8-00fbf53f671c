const { emoji<PERSON>he<PERSON> } = require("../util/textCheck");
const db = require("../db/db");
const UserCoupon = require("../model/userCoupon");
const User = require("../model/user");
const { logPaymentPut } = require("../logger");
const { formatNumber } = require("../util/formatNumber");

/* --------------------------------------------------------------------- 
ユーザーにクーポンを付与する
-----------------------------------------------------------------------*/
exports.assignUserCoupon = async (req, res) => {
  const { couponCode } = req.body;
  const userId = req.user.userId;

  let connection;

  try {
    if (couponCode) {
        emojiChecker(couponCode, "Emojis cannot be used in the coupon code.");
    }

    if (!couponCode || !userId) {
        throw new Error("The coupon code or user ID is invalid.");
    }

    connection = await new Promise((resolve, reject) => {
      db.getConnection((err, conn) => {
        if (err) return reject(err);
        resolve(conn);
      });
    });

    await new Promise((resolve, reject) => {
      connection.beginTransaction((err) => {
        if (err) return reject(err);
        resolve();
      });
    });

    // リクエストされたコードが課金用クーポンかどうかを確認
    const checkBuyDiscountCouponResults = await UserCoupon.checkBuyDiscountCoupon(connection, couponCode);

    // リクエストされたコードが課金用クーポンの場合
    if (checkBuyDiscountCouponResults.length > 0) {
      // buy_discount_couponテーブルから該当コードのクーポンからrestを取得
      const rest = checkBuyDiscountCouponResults[0].rest;

      // 有効期限チェック
      const currentDate = new Date();
      const couponDate = new Date(checkBuyDiscountCouponResults[0].created_at);
      const termDays = checkBuyDiscountCouponResults[0].term;
      const expiryDate = new Date(couponDate);
      expiryDate.setDate(couponDate.getDate() + termDays);

      if (currentDate > expiryDate) {
        throw new Error("The coupon has expired.");
      }

      if (rest <= 0) {
        throw new Error("This coupon is out of stock.");
      }

      // userCouponsテーブルに該当クーポンが存在するかを確認
      const userCouponCheckResult = await UserCoupon.userCouponCheck(connection, couponCode, userId);

      if (userCouponCheckResult.length > 0) {
        throw new Error("You have already claimed the coupon.");
      }

      // 該当クーポンの残数をデクリメント
      await UserCoupon.couponDecrement(connection, couponCode);

      // userCouponsテーブルのuserIdにはreq.user.userIdを、nameにはbuy_discount_couponテーブルのnameを、typeには”購入割引”を、termにはbuy_discount_couponテーブルのtermを、dateにはインサートする際の日付を、percentageにはbuy_discount_couponテーブルのdiscountを、statusには”expired”を、couponIdにはnullを、buy_codeにはreq.body.couponCodeをインサートする（課金用クーポンとポイント付与クーポンは別テーブルのため）
      await UserCoupon.assignUserBuyDiscountCoupon(connection, userId, checkBuyDiscountCouponResults[0].name, checkBuyDiscountCouponResults[0].term, checkBuyDiscountCouponResults[0].discount, couponCode);

      res.json({ message: "The coupon has been granted!" });
    } else {
      // 通常クーポンが存在するか確認
      const checkNormalCouponResults = await UserCoupon.checkNormalCoupon(connection, couponCode);

      if (checkNormalCouponResults.length === 0) {
        throw new Error("The specified coupon does not exist.");
      }

      const coupon = checkNormalCouponResults[0];

      // 有効期限チェック
      const currentDate = new Date();
      const couponDate = new Date(coupon.date);
      const termDays = coupon.term;
      const expiryDate = new Date(couponDate);
      expiryDate.setDate(couponDate.getDate() + termDays);

      if (currentDate > expiryDate) {
        throw new Error("The expiration date has passed.");
      }

      if (coupon.rest <= 0) {
        throw new Error("This coupon is out of stock.");
      }

      // 重複チェック
      const userCouponResults = await UserCoupon.userNormalCouponCheck(connection, userId, coupon.id);

      if (userCouponResults.length > 0) {
        throw new Error("You have already obtained the coupon.");
      }

      // クーポン種類を代入
      let userCouponStatus = "";
      if (coupon.type === "discount") {
        userCouponStatus = "active";
      } else {
        userCouponStatus = "expired";
      }

      let couponPercentage = coupon.percentage;

      if (coupon.type === "random") {
        // 付与ポイントをランダムに設定
        couponPercentage = Math.floor(Math.random() * coupon.roundNum) + 1;
        const pointsToAssign = Math.floor((coupon.percentage * couponPercentage) / 100);

        // userテーブルポイントを取得（更新前）
        const userBeforePoint = await User.getUserPoint(connection, userId);

        // ポイントを付与
        await User.updateUserPointsChange(connection, userId, pointsToAssign);

        // 通常クーポンの残巣をデクリメント
        await UserCoupon.normalCouponDecrement(connection, coupon.id);

        // userテーブルポイントを取得（更新後）
        const userAfterPoint = await User.getUserPoint(connection, userId);

        // ユーザーにクーポンを付与
        await UserCoupon.assignUserNormalCoupon(connection, userId, coupon.name, coupon.type, coupon.term, couponPercentage, userCouponStatus, coupon.id);

        logPaymentPut(`ユーザー[${userId}]にランダムクーポンが付与されました。付与前のポイント:${formatNumber(userBeforePoint)}、付与後のポイント:${formatNumber(userAfterPoint)}、付与ポイント:${formatNumber(pointsToAssign)}、付与クーポン:${coupon.name}、付与クーポンの種類:${coupon.type}、付与クーポンの有効期限:${coupon.term}日、付与クーポンのID:${coupon.id}`);

        res.json({ message: `A coupon has been granted for ${couponPercentage}% of the total (${pointsToAssign}P)!` });
      } else if (coupon.type === "distribution") {
        // userテーブルポイントを取得（更新前）
        const userBeforePoint = await User.getUserPoint(connection, userId);

        // ポイントを付与
        await User.updateUserPointsChange(connection, userId, coupon.percentage);

        // userテーブルポイントを取得（更新後）
        const userAfterPoint = await User.getUserPoint(connection, userId);

        // 通常クーポンの残数をデクリメント
        await UserCoupon.normalCouponDecrement(connection, coupon.id);

        // ユーザーにクーポンを付与
        await UserCoupon.assignUserNormalCoupon(connection, userId, coupon.name, coupon.type, coupon.term, couponPercentage, userCouponStatus, coupon.id);

        logPaymentPut(`ユーザー[${userId}]に配布クーポンが付与されました。付与前のポイント:${formatNumber(userBeforePoint)}、付与後のポイント:${formatNumber(userAfterPoint)}、付与ポイント:${formatNumber(coupon.percentage)}、付与クーポン:${coupon.name}、付与クーポンの種類:${coupon.type}、付与クーポンの有効期限:${coupon.term}日、付与クーポンのID:${coupon.id}`);

        res.json({ message: `${coupon.percentage} points have been granted!` });
      } else {
        // 通常クーポンの残巣をデクリメント
        await UserCoupon.normalCouponDecrement(connection, coupon.id);

        // ユーザーにクーポンを付与
        await UserCoupon.assignUserNormalCoupon(connection, userId, coupon.name, coupon.type, coupon.term, couponPercentage, userCouponStatus, coupon.id);

        res.json({ message: `${coupon.percentage}% discount coupon has been granted!` });
      }
    }

    await new Promise((resolve, reject) => {
      connection.commit((err) => {
        if (err) return reject(err);
        resolve();
      });
    });
  } catch (error) {
    if (connection) {
      await new Promise((resolve) => {
        connection.rollback(() => resolve());
      });
    }

    console.error("Error assigning coupon:", error);

    return res.status(500).json({ message: error.message });
  } finally {
    if (connection) connection.release();
  }
};