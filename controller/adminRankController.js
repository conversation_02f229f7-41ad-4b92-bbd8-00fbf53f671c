const multer = require("multer");
const RankModel = require("../model/adminRank");
const Res = require("../util/responseHelpers");
const { logAdminPut, logErr } = require("../logger");
const AWS = require("aws-sdk");


// // Multer設定
// const storage = multer.diskStorage({
//   destination: function (req, file, cb) {
//     cb(null, "uploads/");
//   },
//   filename: function (req, file, cb) {
//     cb(null, `${Date.now()}-${file.originalname}`);
//   },
// });
//
// const upload = multer({ storage: storage }).any();
//
// exports.upload = (req, res, next) => {
//   upload(req, res, function (err) {
//     if (err instanceof multer.MulterError) {
//       logErr(`ファイルのアップロードに失敗しました。エラー内容: ${err} - controller_adminRankController_upload`);
//       return res.status(500).json({ message: "File upload failed." });
//     } else if (err) {
//       logErr(`ファイルのアップロード中に未知のエラーが発生しました。エラー内容: ${err} - controller_adminRankController_upload`);
//       return res.status(500).json({ message: "An unknown error occurred during file upload." });
//     }
//     next();
//   });
// };

// 初始化 S3 客户端
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
  region: process.env.AWS_REGION || "us-east-1", // S3 区域
});

// Multer 配置（改为内存存储）
const upload = multer({
  storage: multer.memoryStorage(), // 将文件存储在内存中，而不是磁盘
}).any(); // 支持解析任意数量的文件字段

// 文件上传中间件
exports.upload = (req, res, next) => {
  upload(req, res, function (err) {
    if (err instanceof multer.MulterError) {
      logErr(`createRank upload文件类型不对: ${err} - controller_adminRankController_upload`);
      return res.status(500).json({ message: "File upload failed." });
    } else if (err) {
      logErr(`createRank。エラー内容: ${err} - controller_adminRankController_upload`);
      return res.status(500).json({ message: "An unknown error occurred during file upload." });
    }
    next();
  });
};

/* ---------------------------------------------------------------------
ランクを作成
-----------------------------------------------------------------------*/
exports.createRank = async (req, res) => {
  try {
    const files = req.files;

    for (let file of files) {
      const fieldMatch = file.fieldname.match(/rank(\d+)_backImg/);
      if (fieldMatch) {
        const rankId = fieldMatch[1];
        const name = req.body[`rank${rankId}_name`];
        const point = req.body[`rank${rankId}_point`];
        const gpPercent = req.body[`rank${rankId}_gpPercent`];
        const bonusPoint = req.body[`rank${rankId}_bonusPoint`];

        // 上传文件到 S3
        const params = {
          Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
          Key: `${Date.now()}-${file.originalname}`, // 文件名
          Body: file.buffer, // 文件内容（存储在内存中的 Buffer）
          ContentType: file.mimetype, // 文件类型
        };

        const result = await s3.upload(params).promise(); // 上传文件
        const backImg = result.Location; // 获取文件的公共访问 URL

        const order = req.body[`rank${rankId}_order`];
        const buyAddPointPercent = req.body[`rank${rankId}_buyAddPointPercent`];

        // 插入数据库
        await RankModel.insertRank(name, point, gpPercent, bonusPoint, backImg, order, buyAddPointPercent);
      }
    }

    res.status(200).json({ message: "Data has been successfully inserted." });
  } catch (error) {
    logErr(`createRank异常: ${error} - controller_adminRankController_createRank`);
    res.status(500).json({ message: "Failed to insert data." });
  }
};

/* ---------------------------------------------------------------------
全てのランクデータの取得
-----------------------------------------------------------------------*/
exports.getRanks = (req, res) => {
  RankModel.getAllRanks((error, ranks) => {
    if (error) {
      logErr(`データの取得に失敗しました。エラー内容: ${error} - controller_adminRankController_getRanks`);
      res.status(500).json({ message: "Failed to retrieve data." });
    } else {
      res.status(200).json(ranks);
    }
  });
};

/* ---------------------------------------------------------------------
IDで指定したランク情報取得
-----------------------------------------------------------------------*/
exports.getRank = async (req, res) => {
  try {
    const { rank_id } = req.params;
    console.log(rank_id);
    const result = await RankModel.getRank(rank_id, req);
    console.log(result);
    res.status(200).json(result);
  } catch (error) {
    console.error(error);
    logErr(`データの取得に失敗しました。エラー内容: ${error} - controller_adminRankController_getRank`);
    res.status(500).json({ message: "Failed to retrieve data." });
  }
};

/* ---------------------------------------------------------------------
該当IDのランクデータ更新
-----------------------------------------------------------------------*/
exports.updateRank = async (req, res) => {
  const rank_id = req.params.rank_id; // URLパラメータから取得

  // req.bodyにはテキストフィールドが含まれます
  console.log("req.body:", req.body);
  // req.filesにはファイルが含まれます
  console.log("req.files:", req.files);

  const name = req.body[`rank${rank_id}_name`];
  const point = req.body[`rank${rank_id}_point`];
  const gpPercent = req.body[`rank${rank_id}_gpPercent`];
  const bonusPoint = req.body[`rank${rank_id}_bonusPoint`];
  const order = req.body[`rank${rank_id}_order`];
  const buyAddPointPercent = req.body[`rank${rank_id}_buyAddPointPercent`];

  // ファイルを取得
  let backImgFile = req.files.find((file) => file.fieldname === `rank${rank_id}_backImg`);
  let backImg = backImgFile ? `uploads/${backImgFile.filename}` : null;

  console.log(rank_id, name, point, gpPercent, bonusPoint, backImg, order);

  try {
    if (backImg) {
      const result = await RankModel.updateRankYesImage(rank_id, name, point, gpPercent, bonusPoint, backImg, order, buyAddPointPercent);
      res.status(200).json({ message: "Data has been successfully updated", result });
    } else {
      const result = await RankModel.updateRankNoImage(rank_id, name, point, gpPercent, bonusPoint, order, buyAddPointPercent);
      res.status(200).json({ message: "Data has been successfully updated", result });
    }
  } catch (error) {
    logErr(`ランクの更新ができませんでした。 : ${error} - controller_adminRankController_updateRank`);
    console.error("データベースへの更新エラー:", error);
    res.status(500).json({ message: "Failed to update data." });
  }
};

/* ---------------------------------------------------------------------
ランクデータのオーダー更新
-----------------------------------------------------------------------*/
exports.changeOrder = async (req, res) => {
  const { items } = req.body;
  try {
    const update = await RankModel.changeOrder(items);
    if (update.success === true) {
      Res.resJsonFn(res, 200, null, "更新が完了しました。");
    } else {
      Res.resJsonFn(res, 500, null, "更新がうまくいきませんでした。");
    }
  } catch (error) {
    logErr(`ランクの順序設定について更新ができませんでした。: ${error} - controller_adminRankController_changeOrder`);
    console.log(error);
  }
};

/* ---------------------------------------------------------------------
ランクデータの削除
-----------------------------------------------------------------------*/
exports.removeRank = async (req, res) => {
  try {
    const { removeNumber } = req.body;
    const result = await RankModel.removeRank(removeNumber);
    Res.resJsonFn(res, 200, null, "対象ランクの削除に成功しました。");
  } catch (error) {
    console.log(error);
    logErr(`対象ランクの削除に失敗しました。: ${error} - controller_adminRankController_removeRank`);
    Res.resJsonFn(res, 500, null, "対象ランクの削除に失敗しました。");
  }
};
