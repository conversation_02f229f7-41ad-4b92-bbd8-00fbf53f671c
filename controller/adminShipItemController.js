const Res = require("../util/responseHelpers");
const { logAdminPut, logErr } = require("../logger");
const ShipItem = require("../model/adminShipItem");
const User = require("../model/user");
const db = require("../db/db");

// コールバックをPromiseでラップする関数
const getConnection = (db) => {
  return new Promise((resolve, reject) => {
    db.getConnection((err, connection) => {
      if (err) {
        console.log(err);
        return reject(err);
      }
      resolve(connection);
    });
  });
};

const beginTransaction = (connection) => {
  return new Promise((resolve, reject) => {
    connection.beginTransaction((err) => {
      if (err) {
        console.log(err);
        return reject(err);
      }
      resolve();
    });
  });
};

const commit = (connection) => {
  return new Promise((resolve, reject) => {
    connection.commit((err) => {
      if (err) {
        console.log(err);
        return reject(err);
      }
      resolve();
    });
  });
};

const rollback = (connection) => {
  return new Promise((resolve) => {
    connection.rollback(() => resolve());
  });
};

/* --------------------------------------------------------------------- 
配送依頼を受けたアイテムをポイントに交換
-----------------------------------------------------------------------*/
exports.exchangeItemToPoint = async (req, res) => {
  const { itemId, userId } = req.body;
  let connection;

  try {
    // プールからコネクションを取得
    connection = await getConnection(db);

    // トランザクション開始
    await beginTransaction(connection);

    // 該当アイテムのポイントを取得
    const itemPoint = await ShipItem.getShipItemPoint(connection, itemId);
    console.log("itemPoint", itemPoint);

    // 該当アイテムのポイントをユーザーのポイントに加算
    await User.updateUserPointsChange(connection, userId, itemPoint);

    // 該当アイテムの削除
    await ShipItem.deleteShipItem(connection, itemId);

    // トランザクションをコミット
    await commit(connection);

    Res.resJsonFn(res, 200, null, "The specified item has been exchanged for points.");
  } catch (error) {
    // エラーが発生した場合はロールバック
    if (connection) {
      await rollback(connection);
    }

    console.error("配送依頼を受けたアイテムをポイントに交換できませんでした:", error);
    logErr(`配送依頼を受けたアイテムをポイントに交換できませんでした: ${error} - controller_adminShipItemController_exchangeItemToPoint`);
    res.status(500).json({ message: "Failed to insert data." });
  } finally {
    if (connection) {
      connection.release();
    }
  }
};
