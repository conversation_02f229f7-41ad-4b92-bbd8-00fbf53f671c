const Level = require("../model/level");

exports.getAllLevels = async (req, res) => {
    try {
        const levels = await Level.getAll();
        res.status(200).json({ levels });
    } catch (err) {
        console.error("Failed to get level list:", err);
        res.status(500).json({ message: "Failed to get level list" });
    }
};

exports.addLevel = async (req, res) => {
    try {
        const { type, level, value } = req.body;
        if (typeof level !== 'number' || typeof value !== 'number') {
            return res.status(400).json({ message: "level and value must be numbers" });
        }
        await Level.insert({ type, level, value });
        res.status(201).json({ message: "Level added successfully" });
    } catch (err) {
        console.error("Failed to add level:", err);
        res.status(500).json({ message: "Failed to add level" });
    }
};

exports.deleteLevel = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({ message: "Missing id parameter" });
        }
        await Level.deleteById(id);
        res.status(200).json({ message: "Level deleted successfully" });
    } catch (err) {
        console.error("Failed to delete level:", err);
        res.status(500).json({ message: "Failed to delete level" });
    }
}; 