const UserExtension = require('../model/userExtension');
const { resJsonDataFn, resJsonFn } = require('../util/responseHelpers');

exports.getUserExtension = async (req, res) => {
  try {
    const userId = req.user.userId;
    const userExtensionData = await UserExtension.getOrCreateByUserId(null, userId);
    resJsonDataFn(res, 200, null, 'Success', userExtensionData);
  } catch (error) {
    console.error('获取用户扩展数据时出错:', error);
    resJsonFn(res, 500, 'Error', 'Failed to get user extension data');
  }
}; 