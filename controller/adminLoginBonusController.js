const db = require("../db/db");
const fs = require("fs");
const { logAdminPut, logErr } = require("../logger");
const AWS = require("aws-sdk");
const multer = require("multer");

/* ---------------------------------------------------------------------
ログインボーナスを作成
-----------------------------------------------------------------------*/
// exports.createLoginBonus = async (req, res) => {
//   db.getConnection((err, connection) => {
//     if (err) {
//       console.error("数据库连接错误:", err);
//       logErr(`数据库连接错误: ${err} - controller_adminLoginBonusController_createLoginBonus`);
//       return res.status(500).json({ error: "发生数据库连接错误。" });
//     }
//
//     // 开始事务
//     connection.beginTransaction((err) => {
//       if (err) {
//         connection.release();
//         console.error("事务开始错误:", err);
//         logErr(`事务开始错误: ${err} - controller_adminLoginBonusController_createLoginBonus`);
//         return res.status(500).json({ error: "启动事务时发生错误。" });
//       }
//
//       // 从请求体中获取数据
//       let loginBonusesData;
//       try {
//         loginBonusesData = JSON.parse(req.body.loginBonuses);
//       } catch (parseErr) {
//         connection.release();
//         console.error("JSON解析错误:", parseErr);
//         logErr(`JSON解析错误: ${parseErr} - controller_adminLoginBonusController_createLoginBonus`);
//         return res.status(400).json({ message: "请求数据无效。" });
//       }
//
//       // 文件映射
//       const filesMap = {};
//       if (req.files && req.files.length > 0) {
//         req.files.forEach((file) => {
//           filesMap[file.fieldname] = file;
//         });
//       }
//
//       // 定义辅助函数以同步处理异步操作
//       const processLoginBonus = (index) => {
//         if (index >= loginBonusesData.length) {
//           // 所有处理完成后提交事务
//           connection.commit((err) => {
//             connection.release();
//             if (err) {
//               console.error("提交错误:", err);
//               logErr(`提交错误: ${err} - controller_adminLoginBonusController_createLoginBonus`);
//               return res.status(500).json({ error: "提交时发生错误。" });
//             } else {
//               res.status(200).json({ message: "登录奖励保存成功。" });
//             }
//           });
//           return;
//         }
//
//         const bonus = loginBonusesData[index];
//         const { day, items } = bonus;
//
//         // 检查是否存在相同 day 的记录
//         connection.query("SELECT COUNT(*) AS count FROM loginBonus WHERE day = ?", [day], (err, results) => {
//           if (err) {
//             connection.rollback(() => {
//               connection.release();
//               console.error("loginBonus 查询错误:", err);
//               logErr(`loginBonus 查询错误: ${err} - controller_adminLoginBonusController_createLoginBonus`);
//               cleanupUploadedFiles(req.files);
//               return res.status(500).json({ error: "发生数据库错误。" });
//             });
//             return;
//           }
//
//           if (results[0].count > 0) {
//             // 如果存在相同 day 的记录，返回错误
//             connection.rollback(() => {
//               connection.release();
//               console.error(`第 ${day} 天的登录奖励已存在。`);
//               cleanupUploadedFiles(req.files);
//               return res.status(400).json({ message: `第 ${day} 天的登录奖励已存在。` });
//             });
//             return;
//           } else {
//             // 如果不存在，则插入到 loginBonus 表
//             connection.query("INSERT INTO loginBonus (day) VALUES (?)", [day], (err, results) => {
//               if (err) {
//                 connection.rollback(() => {
//                   connection.release();
//                   console.error("loginBonus 插入错误:", err);
//                   logErr(`loginBonus 插入错误: ${err} - controller_adminLoginBonusController_createLoginBonus`);
//                   cleanupUploadedFiles(req.files);
//                   return res.status(500).json({ error: "发生数据库错误。" });
//                 });
//                 return;
//               }
//
//               const loginBonusId = results.insertId;
//
//               // 处理奖励物品
//               processBonusItems(index, 0, loginBonusId);
//             });
//           }
//         });
//       };
//
//       const processBonusItems = (bonusIndex, itemIndex, loginBonusId) => {
//         const bonus = loginBonusesData[bonusIndex];
//         const items = bonus.items;
//
//         if (itemIndex >= items.length) {
//           // 处理下一个登录奖励
//           processLoginBonus(bonusIndex + 1);
//           return;
//         }
//
//         const item = items[itemIndex];
//         const { point } = item;
//
//         // 获取对应的视频文件
//         const fileKey = `video-${bonusIndex}-${itemIndex}`;
//         const file = filesMap[fileKey];
//
//         if (!file) {
//           connection.rollback(() => {
//             connection.release();
//             console.error(`未找到视频文件: 第 ${bonusIndex + 1} 个奖励, 第 ${itemIndex + 1} 个物品`);
//             cleanupUploadedFiles(req.files);
//             logErr(`未找到视频文件: 第 ${bonusIndex + 1} 个奖励, 第 ${itemIndex + 1} 个物品 - controller_adminLoginBonusController_createLoginBonus`);
//             return res.status(400).json({ error: "未找到所需的视频文件。" });
//           });
//           return;
//         }
//
//         const videoPath = file.path;
//
//         // 插入到 loginBonusPoints 表
//         connection.query("INSERT INTO loginBonusPoints (login_bonus_id, point, video_path) VALUES (?, ?, ?)", [loginBonusId, point, videoPath], (err) => {
//           if (err) {
//             connection.rollback(() => {
//               connection.release();
//               console.error("loginBonusPoints 插入错误:", err);
//               logErr(`loginBonusPoints 插入错误: ${err} - controller_adminLoginBonusController_createLoginBonus`);
//               cleanupUploadedFiles(req.files);
//               return res.status(500).json({ error: "发生数据库错误。" });
//             });
//             return;
//           }
//
//           // 处理下一个奖励物品
//           processBonusItems(bonusIndex, itemIndex + 1, loginBonusId);
//         });
//       };
//
//       const cleanupUploadedFiles = (files) => {
//         if (files && files.length > 0) {
//           files.forEach((file) => {
//             fs.unlink(file.path, (err) => {
//               if (err) console.error(`删除文件 ${file.path} 时失败:`, err);
//             });
//           });
//         }
//       };
//
//       // 开始处理
//       processLoginBonus(0);
//     });
//   });
// };



// 初始化 S3 客户端
const s3 = new AWS.S3({
  accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
  region: process.env.AWS_REGION || "us-east-1", // S3 区域
});


exports.createLoginBonus = async (req, res) => {
  let connection;
  try {
    // 获取数据库连接
    connection = await new Promise((resolve, reject) => {
      db.getConnection((err, conn) => {
        if (err) return reject(err);
        resolve(conn);
      });
    });

    // 开始事务
    await new Promise((resolve, reject) => {
      connection.beginTransaction((err) => {
        if (err) return reject(err);
        resolve();
      });
    });

    // 从请求体中获取数据
    let loginBonusesData;
    try {
      loginBonusesData = JSON.parse(req.body.loginBonuses);
    } catch (parseErr) {
      throw new Error("请求数据无效。");
    }

    // 文件映射
    const filesMap = {};
    if (req.files && req.files.length > 0) {
      req.files.forEach((file) => {
        filesMap[file.fieldname] = file;
      });
    }

    // 处理登录奖励
    for (const bonus of loginBonusesData) {
      const { day, items } = bonus;

      // 检查是否存在相同 day 的记录
      const [results] = await new Promise((resolve, reject) => {
        connection.query("SELECT COUNT(*) AS count FROM loginBonus WHERE day = ?", [day], (err, results) => {
          if (err) return reject(err);
          resolve(results);
        });
      });

      if (results.count > 0) {
        throw new Error(`第 ${day} 天的登录奖励已存在。`);
      }

      // 插入到 loginBonus 表
      const insertResult = await new Promise((resolve, reject) => {
        connection.query("INSERT INTO loginBonus (day) VALUES (?)", [day], (err, results) => {
          if (err) return reject(err);
          resolve(results);
        });
      });

      const loginBonusId = insertResult.insertId;

      // 处理奖励物品
      for (const [itemIndex, item] of items.entries()) {
        const { point } = item;

        // 获取对应的视频文件
        const fileKey = `video-${loginBonusesData.indexOf(bonus)}-${itemIndex}`;
        const file = filesMap[fileKey];
        if (!file) {
          throw new Error(`未找到视频文件: 第 ${loginBonusesData.indexOf(bonus) + 1} 个奖励, 第 ${itemIndex + 1} 个物品`);
        }

        // 上传文件到 S3
        const params = {
          Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
          Key: `${Date.now()}-${file.originalname}`, // 文件名
          Body: file.buffer, // 文件内容（存储在内存中的 Buffer）
          ContentType: file.mimetype, // 文件类型
        };

        const s3Result = await s3.upload(params).promise();
        const videoPath = s3Result.Location; // 获取文件的公共访问 URL

        // 插入到 loginBonusPoints 表
        await new Promise((resolve, reject) => {
          connection.query(
              "INSERT INTO loginBonusPoints (login_bonus_id, point, video_path) VALUES (?, ?, ?)",
              [loginBonusId, point, videoPath],
              (err) => {
                if (err) return reject(err);
                resolve();
              }
          );
        });
      }
    }

    // 提交事务
    await new Promise((resolve, reject) => {
      connection.commit((err) => {
        if (err) return reject(err);
        resolve();
      });
    });

    res.status(200).json({ message: "登录奖励保存成功。" });
  } catch (error) {
    console.error("处理登录奖励时发生错误:", error);

    // 回滚事务
    if (connection) {
      await new Promise((resolve) => {
        connection.rollback(() => resolve());
      });
    }

    cleanupUploadedFiles(req.files);
    res.status(error.message.includes("已存在") ? 400 : 500).json({ error: error.message });
  } finally {
    // 释放连接
    if (connection) {
      connection.release();
    }
  }
};

// 清理上传的文件
const cleanupUploadedFiles = (files) => {
  if (files && files.length > 0) {
    files.forEach((file) => {
      // S3 不需要清理本地文件
    });
  }
};


/* ---------------------------------------------------------------------
ログインボーナスを取得
-----------------------------------------------------------------------*/
exports.getLoginBonus = async (req, res) => {
  const sql = `
  SELECT lb.id AS login_bonus_id, lb.day, lb.created_at AS login_bonus_created_at,
         lbp.id AS bonus_point_id, lbp.point, lbp.video_path, lbp.created_at AS bonus_point_created_at
  FROM loginBonus lb
  LEFT JOIN loginBonusPoints lbp ON lb.id = lbp.login_bonus_id
  ORDER BY lb.day ASC, lbp.id ASC
`;

  db.query(sql, (err, results) => {
    if (err) {
      console.error("データ取得エラー:", err);
      logErr(`データ取得エラー: ${err} - controller_adminLoginBonusController_getLoginBonus`);
      return res.status(500).json({ error: "An error occurred while retrieving data." });
    }

    // データを整形
    const bonuses = {};
    // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
    const baseURL = ``;
    results.forEach((row) => {
      const bonusId = row.login_bonus_id;
      if (!bonuses[bonusId]) {
        bonuses[bonusId] = {
          id: bonusId,
          day: row.day,
          created_at: row.login_bonus_created_at,
          items: [],
        };
      }

      if (row.bonus_point_id) {
        bonuses[bonusId].items.push({
          id: row.bonus_point_id,
          point: row.point,
          video_path: baseURL + row.video_path,
          created_at: row.bonus_point_created_at,
        });
      }
    });

    // 配列に変換
    const bonusList = Object.values(bonuses);

    res.status(200).json(bonusList);
  });
};

/* ---------------------------------------------------------------------
IDを用いてログインボーナスを取得
-----------------------------------------------------------------------*/
exports.getIdLoginBonus = async (req, res) => {
  const loginBonusId = req.params.id;

  const sql = `
    SELECT lb.id AS login_bonus_id, lb.day, lb.created_at AS login_bonus_created_at,
           lbp.id AS bonus_point_id, lbp.point, lbp.video_path, lbp.created_at AS bonus_point_created_at
    FROM loginBonus lb
    LEFT JOIN loginBonusPoints lbp ON lb.id = lbp.login_bonus_id
    WHERE lb.id = ?
    ORDER BY lbp.id ASC
  `;

  db.query(sql, [loginBonusId], (err, results) => {
    if (err) {
      console.error("データ取得エラー:", err);
      logErr(`データ取得エラー: ${err} - controller_adminLoginBonusController_getIdLoginBonus`);
      return res.status(500).json({ error: "An error occurred while retrieving data." });
    }

    if (results.length === 0) {
      return res.status(404).json({ error: "The specified login bonus was not found." });
    }

    // データを整形
    const bonus = {
      id: results[0].login_bonus_id,
      day: results[0].day,
      created_at: results[0].login_bonus_created_at,
      items: [],
    };

    results.forEach((row) => {
      if (row.bonus_point_id) {
        bonus.items.push({
          id: row.bonus_point_id,
          point: row.point,
          video_path: row.video_path,
          created_at: row.bonus_point_created_at,
        });
      }
    });

    res.status(200).json(bonus);
  });
};

/* ---------------------------------------------------------------------
ログインボーナスを削除
-----------------------------------------------------------------------*/
exports.removeLoginBonus = async (req, res) => {
  const { removeNumber } = req.body;
  if (!removeNumber) {
    return res.status(400).json({ error: "The ID to delete has not been specified." });
  }

  // トランザクションを開始
  db.getConnection((err, connection) => {
    if (err) {
      console.error("データベース接続エラー:", err);
      logErr(`データベース接続エラー: ${err} - controller_adminLoginBonusController_removeLoginBonus`);
      return res.status(500).json({ error: "A database connection error has occurred." });
    }

    connection.beginTransaction((err) => {
      if (err) {
        connection.release();
        console.error("トランザクション開始エラー:", err);
        logErr(`トランザクション開始エラー: ${err} - controller_adminLoginBonusController_removeLoginBonus`);
        return res.status(500).json({ error: "An error occurred while starting the transaction." });
      }

      // loginBonusPoints のレコードを削除
      connection.query("DELETE FROM loginBonusPoints WHERE login_bonus_id = ?", [removeNumber], (err) => {
        if (err) {
          connection.rollback(() => {
            connection.release();
            console.error("loginBonusPoints削除エラー:", err);
            logErr(`loginBonusPoints削除エラー: ${err} - controller_adminLoginBonusController_removeLoginBonus`);
            return res.status(500).json({ error: "A database error has occurred." });
          });
          return;
        }

        // loginBonus のレコードを削除
        connection.query("DELETE FROM loginBonus WHERE id = ?", [removeNumber], (err) => {
          if (err) {
            connection.rollback(() => {
              connection.release();
              console.error("loginBonus削除エラー:", err);
              logErr(`loginBonus削除エラー: ${err} - controller_adminLoginBonusController_removeLoginBonus`);
              return res.status(500).json({ error: "A database error occurred." });
            });
            return;
          }

          // トランザクションをコミット
          connection.commit((err) => {
            connection.release();
            if (err) {
              console.error("コミットエラー:", err);
              logErr(`コミットエラー: ${err} - controller_adminLoginBonusController_removeLoginBonus`);
              return res.status(500).json({ error: "A commit error has occurred." });
            } else {
              res.status(200).json({ message: "Login bonus has been successfully deleted." });
            }
          });
        });
      });
    });
  });
};

/* ---------------------------------------------------------------------
ログインボーナスを更新
-----------------------------------------------------------------------*/
exports.updateLoginBonus = async (req, res) => {
  const loginBonusId = req.params.id;
  let connection;

  // フォームデータから取得
  let loginBonusesData;
  try {
    loginBonusesData = JSON.parse(req.body.loginBonuses);
  } catch (error) {
    console.error("JSONパースエラー:", error);
    logErr(`JSONパースエラー: ${error} - controller_adminLoginBonusController_updateLoginBonus`);
    return res.status(400).json({ error: "Invalid data format." });
  }

  // アップロードされたファイルをマッピング
  const filesMap = {};
  if (req.files && req.files.length > 0) {
    req.files.forEach((file) => {
      filesMap[file.fieldname] = file;
    });
  }

  db.getConnection(async (err, conn) => {
    if (err) {
      console.error("データベース接続エラー:", err);
      logErr(`データベース接続エラー: ${err} - controller_adminLoginBonusController_updateLoginBonus`);
      return res.status(500).json({ error: "A database connection error has occurred." });
    }
    connection = conn;

    try {
      await connection.beginTransaction();

      // 既存のloginBonusを更新
      const { day, items } = loginBonusesData[0];

      // loginBonusの更新
      await new Promise((resolve, reject) => {
        connection.query("UPDATE loginBonus SET day = ? WHERE id = ?", [day, loginBonusId], (err) => {
          if (err) {
            console.error("loginBonus更新エラー:", err);
            logErr(`loginBonus更新エラー: ${err} - controller_adminLoginBonusController_updateLoginBonus`);
            reject(err);
          } else {
            resolve();
          }
        });
      });

      // データベースから既存のloginBonusPointsを取得
      const existingItems = await new Promise((resolve, reject) => {
        connection.query("SELECT id, point, video_path FROM loginBonusPoints WHERE login_bonus_id = ?", [loginBonusId], (err, results) => {
          if (err) {
            console.error("loginBonusPoints取得エラー:", err);
            logErr(`loginBonusPoints取得エラー: ${err} - controller_adminLoginBonusController_updateLoginBonus`);
            reject(err);
          } else {
            resolve(results);
          }
        });
      });

      // フロントエンドから送信されたアイテムのIDリスト
      const sentItemIds = items.filter((item) => item.id !== null && item.id !== undefined).map((item) => item.id);

      // 削除が必要なアイテム（データベースに存在するが、フロントエンドに存在しない）
      const itemsToDelete = existingItems.filter((dbItem) => !sentItemIds.includes(dbItem.id));

      // 削除処理
      for (const item of itemsToDelete) {
        // 動画ファイルを削除
        if (item.video_path) {
          fs.unlink(item.video_path, (err) => {
            if (err) {
              logErr(`ファイル ${item.video_path} の削除に失敗しました: ${err} - controller_adminLoginBonusController_updateLoginBonus`);
              console.error(`ファイル ${item.video_path} の削除に失敗しました:`, err);
            }
          });
        }

        // データベースからアイテムを削除
        await new Promise((resolve, reject) => {
          connection.query("DELETE FROM loginBonusPoints WHERE id = ?", [item.id], (err) => {
            if (err) {
              console.error("loginBonusPoints削除エラー:", err);
              logErr(`loginBonusPoints削除エラー: ${err} - controller_adminLoginBonusController_updateLoginBonus`);
              reject(err);
            } else {
              resolve();
            }
          });
        });
      }

      // 更新または挿入が必要なアイテムの処理
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        const { id: itemId, point, video_path } = item;

        // 対応する動画ファイルを取得
        const fileKey = `video-0-${i}`;
        const file = filesMap[fileKey];
        let newVideoPath = video_path;

        if (file) {
          // 新しい動画ファイルがアップロードされた場合
          newVideoPath = file.path;

          // 既存の動画ファイルを削除
          if (video_path) {
            fs.unlink(video_path, (err) => {
              if (err) {
                logErr(`ファイル ${video_path} の削除に失敗しました: ${err} - controller_adminLoginBonusController_updateLoginBonus`);
                console.error(`ファイル ${video_path} の削除に失敗しました:`, err);
              }
            });
          }
        }

        if (itemId) {
          // 既存のレコードを更新
          await new Promise((resolve, reject) => {
            connection.query("UPDATE loginBonusPoints SET point = ?, video_path = ? WHERE id = ?", [point, newVideoPath, itemId], (err) => {
              if (err) {
                console.error("loginBonusPoints更新エラー:", err);
                logErr(`loginBonusPoints更新エラー: ${err} - controller_adminLoginBonusController_updateLoginBonus`);
                reject(err);
              } else {
                resolve();
              }
            });
          });
        } else {
          // 新しいレコードを挿入
          await new Promise((resolve, reject) => {
            connection.query("INSERT INTO loginBonusPoints (login_bonus_id, point, video_path) VALUES (?, ?, ?)", [loginBonusId, point, newVideoPath], (err) => {
              if (err) {
                console.error("loginBonusPoints挿入エラー:", err);
                logErr(`loginBonusPoints挿入エラー: ${err} - controller_adminLoginBonusController_updateLoginBonus`);
                reject(err);
              } else {
                resolve();
              }
            });
          });
        }
      }

      // トランザクションをコミット
      await new Promise((resolve, reject) => {
        connection.commit((err) => {
          connection.release();
          if (err) {
            console.error("コミットエラー:", err);
            logErr(`コミットエラー: ${err} - controller_adminLoginBonusController_updateLoginBonus`);
            reject(err);
          } else {
            resolve();
          }
        });
      });

      res.status(200).json({ message: "Login bonus has been successfully updated." });
    } catch (err) {
      connection.rollback(() => {
        connection.release();
        console.error("トランザクションエラー:", err);
        logErr(`トランザクションエラー: ${err} - controller_adminLoginBonusController_updateLoginBonus`);
        res.status(500).json({ error: "A database error has occurred." });
      });
    }
  });
};
