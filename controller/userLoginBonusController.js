const User = require("../model/user");
const { logAdminPut, logErr, logUserPut, logPaymentPut } = require("../logger");
const { formatNumber } = require("../util/formatNumber");
const AdminUser = require("../model/adminUser");
const { log } = require("console");

/* ---------------------------------------------------------------------
ユーザーの連続ログインボーナスが存在するかを確認するコントローラー
-----------------------------------------------------------------------*/
exports.searchUserLoginBonus = async (req, res) => {
  // 获取用户ID
  const userId = req.user ? req.user.userId : null;
  try {
    // 获取今天的日期
    const today = new Date();

    // 获取用户最后登录奖励的领取时间
    const LastBonusClaimDate = await User.getLastBonusClaimDate(userId);

    console.log("获取日期：" + LastBonusClaimDate);
    console.log("今日日期：" + today);

    const isSameDate = LastBonusClaimDate &&
        LastBonusClaimDate.getFullYear() === today.getFullYear() &&
        LastBonusClaimDate.getMonth() === today.getMonth() &&
        LastBonusClaimDate.getDate() === today.getDate();

    // 如果用户已经领取了今天的登录奖励
    if (isSameDate) {
      return res.status(400).json({ error: "您今天已经领取了登录奖励。" });
    }

    // 确认用户的连续登录天数并获取天数
    const searchUserLoginDays = await User.searchUserLoginDays(userId);

    // 获取对应的登录奖励
    const getLoginBonus = await User.getLoginBonus(searchUserLoginDays);

    // 如果存在登录奖励，返回天数和奖励的状态
    if (getLoginBonus && getLoginBonus.length > 0) {
      res.status(200).json({ day: getLoginBonus[0].day, bonus: true });
    } else {
      res.status(200).json({ message: "暂无可用登录奖励。", bonus: false });
    }
  } catch (err) {
    console.error("获取用户登录奖励时出错：", err);
    res.status(500).send("无法获取用户登录奖励");
    logErr(`无法获取用户[${userId}]的登录奖励。错误内容：${err} - controller_userLoginBonusController_searchUserLoginBonus`);
  }
};

/* ---------------------------------------------------------------------
ユーザーの連続ログインボーナスルーレットを実行するコントローラー
-----------------------------------------------------------------------*/
exports.executeUserLoginBonus = async (req, res) => {
  // 用户ID获取
  const userId = req.user ? req.user.userId : null;

  try {
    // 获取今天的日期
    const today = new Date();

    // 获取用户最后登录奖励的领取时间
    const LastBonusClaimDate = await User.getLastBonusClaimDate(userId);

    console.log("获取日期：" + LastBonusClaimDate);
    console.log("今日日期：" + today);

    const isSameDate = LastBonusClaimDate &&
        LastBonusClaimDate.getFullYear() === today.getFullYear() &&
        LastBonusClaimDate.getMonth() === today.getMonth() &&
        LastBonusClaimDate.getDate() === today.getDate();

    // 如果用户已经领取了今天的登录奖励
    if (isSameDate) {
      return res.status(400).json({ error: "您今天已经领取了登录奖励。" });
    }

    // 获取对应的登录奖励ID
    const loginBonusGetId = async (searchUserLoginDays) => {
      try {
        const getLoginBonus = await User.getLoginBonus(searchUserLoginDays);
        return getLoginBonus[0].id;
      } catch (error) {
        logErr(`无法获取用户[${userId}]的登录奖励ID。错误内容：${error} - controller_userLoginBonusController_executeUserLoginBonus`);
        console.error("处理奖励时出错：", error);
      }
    };

    // 确认用户的连续登录天数并获取天数
    const searchUserLoginDays = await User.searchUserLoginDays(userId);
    console.log("searchUserLoginDays：" + searchUserLoginDays + "userId:"+userId);

    // 获取对应的登录奖励ID
    const loginBonusId = await loginBonusGetId(searchUserLoginDays);
    console.log("登录奖励ID是：" + loginBonusId);

    // 获取对应奖励点数的记录列表
    const loginBonusPoint = await User.getLoginBonusPointId(loginBonusId);
    console.log("loginBonusPoint:"+loginBonusPoint);

    // 创建对应奖励点数的ID数组
    const bonusPointGroup = loginBonusPoint.map((item) => {
      return item.id;
    });
    console.log("bonusPointGroup:"+bonusPointGroup);

    // 获取用户获得的奖励点数ID的函数
    const getPointId = async (bonusPointGroup) => {
      try {
        let num = bonusPointGroup.length;

        while (num) {
          let i = Math.floor(Math.random() * num);
          let str = bonusPointGroup[--num];
          bonusPointGroup[num] = bonusPointGroup[i];
          bonusPointGroup[i] = str;
        }

        console.log("获得的奖励点数ID是：" + bonusPointGroup[0]);
        return bonusPointGroup[0];
      } catch (error) {
        logErr(`无法获取用户[${userId}]的登录奖励点数ID。错误内容：${error} - controller_userLoginBonusController_executeUserLoginBonus`);
        console.error("处理奖励点数时出错：", error);
      }
    };

    // 获取用户获得的奖励点数ID
    const userGetPointId = await getPointId(bonusPointGroup);
    console.log("userGetPointId：" + userGetPointId + " userId：" + userId);

    // 获取对应ID的奖励点数
    const userGetPoint = await User.getLoginBonusPoint(userGetPointId);
    console.log("userGetPoint：" + userGetPoint + " userId：" + userId);

    // 获取更新前的用户点数
    const userPastPoint = await AdminUser.getUserPoint(userId);
    console.log("userPastPoint：" + userPastPoint + " userId：" + userId);

    // 更新用户的点数
    await User.updateUserPointsNoRank(userId, userGetPoint.point);
    logUserPut(`用户[${userId}]已领取登录奖励。获得点数：${userGetPoint.point} - controller_userLoginBonusController_executeUserLoginBonus`);

    // 获取更新后的用户点数
    const userNewPoint = await AdminUser.getUserPoint(userId);

    // const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
    const baseURL = ``;

    logPaymentPut(`用户[${userId}]已领取[${userGetPointId}]的登录奖励。获得点数：${formatNumber(userGetPoint.point)}。点数从[${formatNumber(userPastPoint)}]变更为[${formatNumber(userNewPoint)}]。 - controller_userLoginBonusController_executeUserLoginBonus`);
    res.status(200).json({ message: "登录奖励已领取。", point: userGetPoint.point, video_path: baseURL + userGetPoint.video_path });
  } catch (error) {
    logErr(`无法获取用户[${userId}]的登录奖励。错误内容：${error} - controller_userLoginBonusController_executeUserLoginBonus`);
    console.error("处理奖励时出错：", error);
  }
};
