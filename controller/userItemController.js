const UserItem = require("../model/userItem");
const { logErr } = require("../logger");
const fs = require("fs"); // Add fs module for file reading
const path = require("path"); // Add path module for file path manipulation

/* --------------------------------------------------------------------- 
  Converts items held by the user to points if they are 10 days old.
-----------------------------------------------------------------------*/
exports.itemAgeCheck = async (req, res) => {
  // Return without doing anything for now
  res.status(200).json({ message: "The item has expired." });
  return;

  const userId = req.user ? req.user.userId : null;

  try {
    // Get all items for the user
    const userItems = await UserItem.getUserItems(userId);

    // Check if items have expired
    const removeItems = userItems.filter((item) => {
      // Get current time
      const now = new Date();
      // Convert expiration_at to Date type
      const expirationDate = new Date(item.expiration_at);
      return expirationDate < now;
    });

    if (removeItems.length === 0) {
      console.log("No expired items were found.");
    } else {
      removeItems.forEach((item) => {
        console.log(`Expired item was found. Item ID:${item.id}`);
      });
    }

    // Convert expired items to points
    removeItems.forEach(async (item) => {
      // logPaymentPut(`Item [${item.id}] for user [${userId}] has expired. Converting to points.`);
      await UserItem.exchangeItemToPoint(item.id, userId);
    });
  } catch (error) {
    logErr(`Could not check for expired items for user [${userId}]. Error:${error} - controller_userItem_itemAgeCheck`);
  }
};

/* --------------------------------------------------------------------- 
  Get items owned by the user
-----------------------------------------------------------------------*/
exports.getUserItems = async (req, res) => {
  const userId = req.user.userId;
  const { status } = req.query;
  const pageNum = parseInt(req.query.pageNum, 10) || 1;
  const pageSize = parseInt(req.query.pageSize, 10) || 10;
  const offset = pageSize * (pageNum - 1);
  try {
    const { userItems, totalPages } = await UserItem.getPagedUserItems(userId, status, pageSize, offset);
    res.status(200).json({ userItems, totalPages });
  } catch (error) {
    console.log(error);
    logErr(`Could not get items for user [${userId}]. Error: ${error} - controller_userItem_getUserItems`);
    res.status(500).json({ message: "Failed to get items." });
  }
};

/* ---------------------------------------------------------------------
  Get top spenders from last week
-----------------------------------------------------------------------*/
exports.getTopSpendersLastWeek = async (req, res) => {
  const limit = parseInt(req.query.limit, 10) || 20;

  try {
    // This is the mock data logic for testing purposes.
    // It reads from a local JSON file instead of the database.
    const mockDataPath = path.join(__dirname, "..", "top-spenders-mock.json");
    const mockData = JSON.parse(fs.readFileSync(mockDataPath, "utf-8"));
    
    // Sort by total_spent descending and take the top 'limit'
    const topSpenders = mockData
      .sort((a, b) => b.total_spent - a.total_spent)
      .slice(0, limit);

    // The original database call is commented out below.
    // Uncomment it when you are ready to go live.
    // const topSpenders = await UserItem.getTopSpendersLastWeek(limit);
    res.status(200).json(topSpenders);
  } catch (error) {
    logErr(`Could not get top spenders from last week. Error: ${error.stack} - controller_userItem_getTopSpendersLastWeek`);
    res.status(500).json({ message: "Failed to get top spenders." });
  }
};