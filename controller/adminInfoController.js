const { info } = require("console");
const { logAdminPut, logErr } = require("../logger");
const AdminInfo = require("../model/adminInfo");

/* --------------------------------------------------------------------- 
お知らせ情報の並び替え
-----------------------------------------------------------------------*/
exports.changeSortCategoryList = async (req, res) => {
  const { infos } = req.body;

  try {
    infos.forEach((info) => {
      AdminInfo.changeSortNumber(info.id, info.sort_number);
    });

    res.status(200).json({ message: "Sorting has been saved successfully." });
  } catch (error) {
    res.status(500).json({ message: "Failed to save the sorting." });
    console.log("Error updating sort numbers:", error);
  }
};
