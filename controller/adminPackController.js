const multer = require("multer");
const PackModel = require("../model/adminPack");
const Res = require("../util/responseHelpers");
const {logAdminPut, logErr} = require("../logger");
const TextCheck = require("../util/textCheck");
const Redis = require("ioredis");
const {default: Redlock} = require("redlock"); // Redlockのdefaultエクスポートを使用
const db = require("../db/db");
const redis = require("../db/redisClient");
const AWS = require("aws-sdk");

// Multer設定
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, "uploads/");
    },
    filename: function (req, file, cb) {
        cb(null, `${Date.now()}-${file.originalname}`);
    },
});

const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID, // 从环境变量中读取 Access Key
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY, // 从环境变量中读取 Secret Key
    region: process.env.AWS_REGION || "us-east-1", // S3 区域
});

const upload = multer({storage: storage}).any();

exports.upload = (req, res, next) => {
    upload(req, res, function (err) {
        if (err instanceof multer.MulterError) {
            return res.status(500).json({message: "File upload failed."});
        } else if (err) {
            return res.status(500).json({message: "An unknown error occurred during file upload."});
        }
        next();
    });
};

/* ---------------------------------------------------------------------
パック情報を取得する
-----------------------------------------------------------------------*/
exports.getPack = async (req, res) => {
    const packId = req.params.id;

    try {
        const pack = await PackModel.getPack(packId); // awaitで結果を待つ
        if (!pack.length) {
            // パックが存在しない場合の処理
            return res.status(404).json({message: "Pack not found."});
        }
        const packItems = await PackModel.getPackItems(packId);
        const packVideos = await PackModel.getPackVideos(packId);
        res.status(200).json({pack, packItems, packVideos});
    } catch (error) {
        console.error("データベースからのパック取得エラー:", error);
        logErr(`データベースからのパック取得エラー: ${error} - controller_adminPackController_getPack`);
        res.status(500).json({message: "Failed to retrieve data."});
    }
};

exports.updatePack = async (req, res) => {
    logAdminPut("updatePack开始执行")
    let packId = req.body["pack_id"]; // 获取请求中的 packId

    const redlock = new Redlock(
        [redis], // Redis 客户端数组
        {
            retryCount: 5, // 最大重试次数
            retryDelay: 200, // 每次重试的延迟时间（毫秒）
            retryJitter: 100, // 随机延迟时间（防止雷同的重试时间）
        }
    );

    const lockKey = `update_pack_lock_${packId}`; // 定义锁的键，确保每个 packId 的更新操作是独立的

    const files = req.files || []; // 请求中上传的文件列表
    let cItems = []; // C 奖项的 JSON 数据
    let videoIds = {}; // 演出视频的 JSON 数据
    let connection; // 数据库连接对象
    let lock; // 分布式锁对象

    try {
        try {
            lock = await redlock.acquire([lockKey], 130000); // 尝试获取分布式锁，锁的有效时间为 130 秒
        } catch (lockErr) {
            console.error("获取锁失败:", lockErr);
            return res.status(503).json({
                message: "其他设备或浏览器标签页可能正在同时执行更新操作。请稍后重试。"
            });
        }

        // 获取数据库连接
        connection = await new Promise((resolve, reject) => {
            db.getConnection((err, conn) => {
                if (err) return reject(err); // 如果获取连接失败，抛出错误
                resolve(conn); // 成功获取连接
            });
        });

        // 开始事务
        await new Promise((resolve, reject) => {
            connection.beginTransaction((err) => {
                if (err) return reject(err); // 如果事务开始失败，抛出错误
                resolve();
            });
        });

        // 解析 C 奖项的 JSON 数据并存储
        if (req.body.cItems && req.body.cItems !== "undefined") {
            cItems = JSON.parse(req.body.cItems); // 将 C 奖项的 JSON 字符串解析为对象
        }

        // 解析演出视频的 JSON 数据并存储
        if (req.body.videos && req.body.videos !== "undefined") {
            videoIds = JSON.parse(req.body.videos); // 将演出视频的 JSON 字符串解析为对象
        }

        // 获取并转换抽卡（Gacha）的缩略图路径
        const packThumbnailFile = files.find((file) => file.fieldname === "pack_thumbnail"); // 查找上传的缩略图文件

        let packThumbnailFilePath; // 用于存储 S3 文件的公共访问 URL

        // 如果有上传文件，则上传到 S3 并获取公共访问 URL
        if (packThumbnailFile) {
            try {
                const s3Params = {
                    Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
                    Key: `${Date.now()}-${packThumbnailFile.originalname}`, // 文件名（时间戳 + 原始文件名）
                    Body: packThumbnailFile.buffer, // 文件内容（存储在内存中的 Buffer，已经是 binary 数据）
                    ContentType: packThumbnailFile.mimetype, // 文件类型
                };

                const s3Result = await s3.upload(s3Params).promise(); // 上传文件到 S3
                packThumbnailFilePath = s3Result.Location; // 获取文件的公共访问 URL

                // 将 S3 返回的 URL 用于后续逻辑
                console.log("缩略图已上传，URL:", packThumbnailFilePath);
            } catch (error) {
                console.error("S3 文件上传失败:", error);
                throw new Error("无法上传缩略图到 S3，请稍后重试");
            }
        } else {
            console.log("未上传缩略图，跳过 S3 上传步骤");
        }
        // 获取抽卡的基本信息
        const packName = req.body["pack_name"]; // 抽卡名称
        const desc = req.body["desc"]; // 抽卡名称
        const andMore = req.body["andMore"]; // 抽卡名称
        const packPoint = req.body["pack_point"]; // 抽卡点数
        const packReduction = req.body["pack_reduction"]; // 抽卡折扣
        const packRest = req.body["pack_rest"]; // 抽卡剩余数量
        const packAllRest = req.body["pack_all_rest"]; // 全部抽卡剩余数量
        const packCat = req.body["pack_cat"]; // 抽卡类别
        const dayOnly = req.body["day_only"] === "true"; // 是否仅限当天
        const allDraw = req.body["all_draw"] === "true"; // 是否允许一次性抽取全部
        const rankOrder = req.body["pack_rank"]; // 抽卡排名顺序
        const displayDays = req.body["display_days"]; // 显示天数
        const limitValue = req.body["limit_value"] ? req.body["limit_value"] : null; // 限制值
        const timerDate = req.body["timer_date"]; // 定时器日期
        const mysteryPack = req.body["mystery_pack"]; // 是否为神秘抽卡
        const resaleStatus = req.body["resale_status"]; // 再售状态

        // 检查抽卡名称是否包含表情符号
        TextCheck.emojiChecker(packName, "抽卡名称中不能使用表情符号。");

        // 检查抽卡是否存在
        const packCheck = await PackModel.packCheck(connection, packId);

        // 根据抽卡是否存在，决定是更新还是插入新记录
        if (packCheck.length > 0) {
            // 如果存在，且上传了缩略图，则更新缩略图
            if (packThumbnailFile) {
                await PackModel.thumbnailYesUpdatePack(
                    connection,
                    packName,
                    desc,
                    andMore,
                    packPoint,
                    packThumbnailFilePath,
                    packReduction,
                    packRest,
                    packCat,
                    packAllRest,
                    dayOnly,
                    rankOrder,
                    displayDays,
                    allDraw,
                    packId,
                    limitValue,
                    timerDate,
                    mysteryPack,
                    resaleStatus
                );
            } else {
                await PackModel.thumbnailNotUpdatePack(
                    connection,
                    packName,
                    desc,
                    andMore,
                    packPoint,
                    packReduction,
                    packRest,
                    packCat,
                    packAllRest,
                    dayOnly,
                    rankOrder,
                    displayDays,
                    allDraw,       // 补全缺失的参数
                    limitValue,
                    packId,        // 现在正确对应第13位
                    timerDate,
                    mysteryPack,
                    resaleStatus
                );
            }
        } else {
            // 如果不存在，则创建新记录
            const minSortNumber = await PackModel.getPackSortNumber(connection); // 获取最小排序号
            const sortNumber = minSortNumber - 1; // 新记录的排序号

            const packResult = await PackModel.insertPackInfo(
                connection,
                packName,
                desc,
                andMore,
                packPoint,
                packThumbnailFilePath,
                packReduction,
                packRest,
                packCat,
                packAllRest,
                dayOnly,
                rankOrder,
                displayDays,
                limitValue,
                allDraw,
                timerDate,
                mysteryPack,
                sortNumber,
                resaleStatus
            );

            packId = packResult; // 使用插入后返回的 packId
        }

        // 获取已存在的物品 ID 列表
        const existingItems = await PackModel.existingItems(connection, packId);

        // 存储更新或新增的物品 ID，最后删除未涉及的物品
        const updatedItemIds = [];

        // 更新 S/A/B 奖项的物品信息
        const itemRanks = ["S", "A", "B"]; // 奖项等级
        for (const rank of itemRanks) {
            // 找到对应奖项的物品 ID
            const itemIds = Object.keys(req.body)
                .filter((key) => key.startsWith(`items${rank}_`) && key.endsWith("_name"))
                .map((key) => key.split("_")[1]); // 提取 itemId，例如 "itemsS_123_name" -> "123"
            for (const itemId of itemIds) {
                // 从请求中提取物品信息
                const itemName = req.body[`items${rank}_${itemId}_name`]; // 物品名称
                const itemPrice = req.body[`items${rank}_${itemId}_price`]; // 物品价格
                const itemRestValue = req.body[`items${rank}_${itemId}_rest`]; // 物品剩余数量
                const itemType = req.body[`items${rank}_${itemId}_type`]; // 物品类别
                const itemOrder = req.body[`items${rank}_${itemId}_order`]; // 物品顺序
                const itemDisplayRest = req.body[`items${rank}_${itemId}_displayRest`]; // 是否显示剩余数量
                const itemShipOnly = req.body[`items${rank}_${itemId}_ship_only`]; // 是否仅限配送
                TextCheck.emojiChecker(itemName, "物品名称中不能使用表情符号。");

                // 查找上传的图片文件
                const imageFile = req.files ? req.files.find((file) => file.fieldname === `items${rank}_${itemId}_imgFile`) : null;

                // 检查物品是否存在
                const itemCheck = await PackModel.itemCheck(connection, itemId, packId, `items${rank}`);

                if (itemCheck.length > 0) {
                    // 如果存在，则更新物品信息
                    let imagePath;
                    if (imageFile) {
                        // 如果有上传的图片文件，则上传到 S3 并获取公共访问 URL
                        const s3Params = {
                            Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
                            Key: `${Date.now()}-${imageFile.originalname}`, // 文件名（时间戳 + 原始文件名）
                            Body: imageFile.buffer, // 文件内容（存储在内存中的 Buffer，已经是 binary 数据）
                            ContentType: imageFile.mimetype, // 文件类型
                        };

                        try {
                            const s3Result = await s3.upload(s3Params).promise(); // 上传文件到 S3
                            imagePath = s3Result.Location; // 获取文件的公共访问 URL
                        } catch (error) {
                            console.error("S3 文件上传失败:", error);
                            throw new Error("无法上传图片到 S3，请稍后重试");
                        }

                        await PackModel.itemImageYesUpdate(
                            connection,
                            itemName,
                            itemPrice,
                            imagePath,
                            itemRestValue,
                            itemType,
                            itemOrder ? itemOrder : null,
                            itemDisplayRest,
                            itemShipOnly,
                            itemId,
                            packId
                        );
                    } else {
                        await PackModel.itemImageNoUpdate(
                            connection,
                            itemName,
                            itemPrice,
                            itemRestValue,
                            itemType,
                            itemOrder ? itemOrder : null,
                            itemDisplayRest,
                            itemShipOnly,
                            itemId,
                            packId
                        );
                    }
                } else {
                    let imagePath;
                    // 如果不存在，则插入新物品
                    if (imageFile) {
                        // 如果有上传的图片文件，则上传到 S3 并获取公共访问 URL
                        const s3Params = {
                            Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
                            Key: `${Date.now()}-${imageFile.originalname}`, // 文件名（时间戳 + 原始文件名）
                            Body: imageFile.buffer, // 文件内容（存储在内存中的 Buffer，已经是 binary 数据）
                            ContentType: imageFile.mimetype, // 文件类型
                        };

                        try {
                            const s3Result = await s3.upload(s3Params).promise(); // 上传文件到 S3
                            imagePath = s3Result.Location; // 获取文件的公共访问 URL
                        } catch (error) {
                            console.error("S3 文件上传失败:", error);
                            throw new Error("无法上传图片到 S3，请稍后重试");
                        }
                        await PackModel.ImageYesInsertItem(
                            connection,
                            rank,
                            itemName,
                            itemPrice,
                            imagePath,
                            itemRestValue,
                            packId,
                            itemType,
                            itemOrder ? itemOrder : null,
                            itemDisplayRest,
                            itemShipOnly
                        );
                    } else {
                        await PackModel.ImageNoInsertItem(
                            connection,
                            rank,
                            itemName,
                            itemPrice,
                            itemRestValue,
                            packId,
                            itemType,
                            itemOrder ? itemOrder : null,
                            itemDisplayRest,
                            itemShipOnly
                        );
                    }
                }

                updatedItemIds.push(parseInt(itemId, 10)); // 记录已更新或新增的物品 ID
            }
        }

        // 更新 C 奖项的物品信息（使用 for-of 循环按顺序处理，而非并行）
        /*for (const item of cItems) {
            // 查找上传的 C 奖项文件
            const uploadedCItemFile = req.files.find((file) => {
                const [prefix, cItemId] = file.fieldname.split("_"); // 提取字段名的前缀和 ID
                return prefix === "itemsC" && cItemId === String(item.id); // 确保字段名匹配当前 C 奖项 ID
            });

            const itemImagePath = uploadedCItemFile ? uploadedCItemFile.path : item.imgFile; // 如果有上传文件，则使用文件路径；否则使用现有路径

            // 检查 C 奖项是否已存在
            const cItemCheck = await PackModel.cItemCheck(connection, item.id, packId, "C");

            // 构造图片的完整 URL 路径
            const baseURL = `${process.env.NODE_ENV === "production"
                ? process.env.PRODUCTION_DOMAIN
                : req.protocol + "://" + req.get("host")}/`;

            if (cItemCheck.length > 0) {
                // 如果已存在 -> 更新
                if (itemImagePath !== "") {
                    await PackModel.cItemImageYesUpdate(
                        connection,
                        item.nameValue,
                        item.priceValue,
                        baseURL + itemImagePath,
                        item.restValue,
                        item.exchange_only,
                        item.displayRestValue,
                        item.id
                    );
                } else {
                    await PackModel.cItemImageNoUpdate(
                        connection,
                        item.nameValue,
                        item.priceValue,
                        item.restValue,
                        item.exchange_only,
                        item.displayRestValue,
                        item.id
                    );
                }
            } else {
                // 如果不存在 -> 插入新记录
                if (itemImagePath !== "") {
                    await PackModel.cItemImageYesInsert(
                        connection,
                        item.nameValue,
                        item.priceValue,
                        baseURL + itemImagePath,
                        item.restValue,
                        packId,
                        item.exchange_only,
                        item.displayRestValue
                    );
                } else {
                    await PackModel.cItemImageNoInsert(
                        connection,
                        item.nameValue,
                        item.priceValue,
                        item.restValue,
                        packId,
                        item.exchange_only,
                        item.displayRestValue
                    );
                }
            }

            updatedItemIds.push(parseInt(item.id, 10)); // 记录已更新或新增的物品 ID
        }
*/

        for (const item of cItems) {
            // 查找上传的 C 奖项文件
            const uploadedCItemFile = req.files.find((file) => {
                const [prefix, cItemId] = file.fieldname.split("_"); // 提取字段名的前缀和 ID
                return prefix === "itemsC" && cItemId === String(item.id); // 确保字段名匹配当前 C 奖项 ID
            });

            let itemImagePath;
            if (uploadedCItemFile) {
                // 如果有上传的图片文件，则上传到 S3 并获取公共访问 URL
                const s3Params = {
                    Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
                    Key: `${Date.now()}-${uploadedCItemFile.originalname}`, // 文件名（时间戳 + 原始文件名）
                    Body: uploadedCItemFile.buffer, // 文件内容（存储在内存中的 Buffer，已经是 binary 数据）
                    ContentType: uploadedCItemFile.mimetype, // 文件类型
                };

                try {
                    const s3Result = await s3.upload(s3Params).promise(); // 上传文件到 S3
                    itemImagePath = s3Result.Location; // 获取文件的公共访问 URL
                } catch (error) {
                    console.error("S3 文件上传失败:", error);
                    throw new Error("无法上传图片到 S3，请稍后重试");
                }
            } else {
                // 如果没有上传新图片，则保留现有图片路径
                itemImagePath = item.imgFile;
            }

            // 检查 C 奖项是否已存在
            const cItemCheck = await PackModel.cItemCheck(connection, item.id, packId, "C");

            if (cItemCheck.length > 0) {
                // 如果已存在 -> 更新
                if (itemImagePath) {
                    await PackModel.cItemImageYesUpdate(
                        connection,
                        item.nameValue,
                        item.priceValue,
                        itemImagePath, // 使用 S3 返回的 URL 或现有路径
                        item.restValue,
                        item.exchange_only,
                        item.displayRestValue,
                        item.id
                    );
                } else {
                    await PackModel.cItemImageNoUpdate(
                        connection,
                        item.nameValue,
                        item.priceValue,
                        item.restValue,
                        item.exchange_only,
                        item.displayRestValue,
                        item.id
                    );
                }
            } else {
                // 如果不存在 -> 插入新记录
                if (itemImagePath) {
                    await PackModel.cItemImageYesInsert(
                        connection,
                        item.nameValue,
                        item.priceValue,
                        itemImagePath, // 使用 S3 返回的 URL 或现有路径
                        item.restValue,
                        packId,
                        item.exchange_only,
                        item.displayRestValue
                    );
                } else {
                    await PackModel.cItemImageNoInsert(
                        connection,
                        item.nameValue,
                        item.priceValue,
                        item.restValue,
                        packId,
                        item.exchange_only,
                        item.displayRestValue
                    );
                }
            }

            updatedItemIds.push(parseInt(item.id, 10)); // 记录已更新或新增的物品 ID
        }
        // 更新最后一项（Last One）的信息
        const lastOneItemName = req.body["last_one_name"]; // 最后一项的名称
        const lastOneItemThumbnailFile = req.files.find((file) => file.fieldname === "last_one_img"); // 查找上传的最后一项缩略图文件

        let lastOneItemThumbnailFilePath;
        if (lastOneItemThumbnailFile) {
            // 如果有上传的缩略图文件，则上传到 S3 并获取公共访问 URL
            const s3Params = {
                Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
                Key: `${Date.now()}-${lastOneItemThumbnailFile.originalname}`, // 文件名（时间戳 + 原始文件名）
                Body: lastOneItemThumbnailFile.buffer, // 文件内容（存储在内存中的 Buffer，已经是 binary 数据）
                ContentType: lastOneItemThumbnailFile.mimetype, // 文件类型
            };

            try {
                const s3Result = await s3.upload(s3Params).promise(); // 上传文件到 S3
                lastOneItemThumbnailFilePath = s3Result.Location; // 获取文件的公共访问 URL
            } catch (error) {
                console.error("S3 文件上传失败:", error);
                throw new Error("无法上传最后一项缩略图到 S3，请稍后重试");
            }
        } else {
            // 如果没有上传新缩略图，则设置为 null
            lastOneItemThumbnailFilePath = null;
        }

        const lastOneItemPrice = req.body["last_one_price"]; // 最后一项的价格
        const lastOneItemType = req.body["last_one_type"]; // 最后一项的类型

        if (lastOneItemName && lastOneItemPrice) {
            // 检查最后一项是否已存在
            const lastOneItemCheck = await PackModel.lastOneItemCheck(connection, packId);

            if (lastOneItemCheck.length > 0) {
                // 如果已存在 -> 更新
                if (lastOneItemThumbnailFile) {
                    await PackModel.lastOneItemImageYesUpdate(
                        connection,
                        lastOneItemName,
                        lastOneItemPrice,
                        lastOneItemThumbnailFilePath,
                        lastOneItemType,
                        packId
                    );
                } else {
                    await PackModel.lastOneItemImageNoUpdate(
                        connection,
                        lastOneItemName,
                        lastOneItemPrice,
                        lastOneItemType,
                        packId
                    );
                }
                updatedItemIds.push(lastOneItemCheck[0].id); // 记录已更新的最后一项 ID
            } else {
                // 如果不存在 -> 插入新记录
                const insertResult = await PackModel.insertLastOneItem(
                    connection,
                    lastOneItemName,
                    lastOneItemPrice,
                    lastOneItemThumbnailFilePath,
                    lastOneItemType,
                    packId
                );
                updatedItemIds.push(insertResult); // 记录插入的新记录 ID
            }
        }

        // 更新番号奖项（Round Number）的信息（使用 for-of 循环按顺序处理，而非并行）
        const roundNumKeys = Object.keys(req.body).filter((key) =>
            key.startsWith("itemsRoundNum_") && key.endsWith("_name")
        ); // 找到所有番号奖项相关的键

        for (const key of roundNumKeys) {
            const itemId = key.split("_")[1]; // 提取番号奖项的 ID，例如 "itemsRoundNum_{id}_name"
            const itemName = req.body[`itemsRoundNum_${itemId}_name`]; // 番号奖项的名称
            const itemPrice = req.body[`itemsRoundNum_${itemId}_price`]; // 番号奖项的价格
            const itemRoundNum = req.body[`itemsRoundNum_${itemId}_roundNum`]; // 番号奖项的编号
            const itemRestValue = req.body[`itemsRoundNum_${itemId}_rest`]; // 番号奖项的剩余数量
            const itemType = req.body[`itemsRoundNum_${itemId}_type`]; // 番号奖项的类型

            // 查找上传的番号奖项文件
            const roundNumFile = req.files.find((file) => file.fieldname === `itemsRoundNum_${itemId}_imgFile`);
            let roundNumFilePath;

            if (roundNumFile) {
                // 如果有上传的图片文件，则上传到 S3 并获取公共访问 URL
                const s3Params = {
                    Bucket: process.env.AWS_BUCKET_NAME, // S3 存储桶名称
                    Key: `${Date.now()}-${roundNumFile.originalname}`, // 文件名（时间戳 + 原始文件名）
                    Body: roundNumFile.buffer, // 文件内容（存储在内存中的 Buffer，已经是 binary 数据）
                    ContentType: roundNumFile.mimetype, // 文件类型
                };

                try {
                    const s3Result = await s3.upload(s3Params).promise(); // 上传文件到 S3
                    roundNumFilePath = s3Result.Location; // 获取文件的公共访问 URL
                } catch (error) {
                    console.error("S3 文件上传失败:", error);
                    throw new Error("无法上传番号奖项图片到 S3，请稍后重试");
                }
            } else {
                // 如果没有上传新图片，则保留现有图片路径或设置为 null
                roundNumFilePath = req.body[`itemsRoundNum_${itemId}_imgFile`] || null;
            }

            // 检查番号奖项是否已存在
            const roundNumItemCheck = await PackModel.checkRoundNumItem(connection, itemId, packId, "roundNum");

            if (roundNumItemCheck.length > 0) {
                // 如果已存在 -> 更新
                if (roundNumFile) {
                    await PackModel.roundNumItemYesImageUpdate(
                        connection,
                        itemName,
                        itemPrice,
                        roundNumFilePath,
                        itemRestValue,
                        itemRoundNum,
                        itemType,
                        itemId,
                        packId
                    );
                } else {
                    await PackModel.roundNumItemNoImageUpdate(
                        connection,
                        itemName,
                        itemPrice,
                        itemRestValue,
                        itemRoundNum,
                        itemType,
                        itemId,
                        packId
                    );
                }
                updatedItemIds.push(parseInt(itemId, 10)); // 记录已更新的番号奖项 ID
            } else {
                // 如果不存在 -> 插入新记录
                if (roundNumFile) {
                    const insertRes = await PackModel.roundNumItemYesImageInsert(
                        connection,
                        itemName,
                        itemPrice,
                        roundNumFilePath,
                        itemRestValue,
                        packId,
                        itemRoundNum,
                        itemType
                    );
                    updatedItemIds.push(parseInt(insertRes, 10)); // 记录插入的新记录 ID
                } else {
                    const insertRes = await PackModel.roundNumItemNoImageInsert(
                        connection,
                        itemName,
                        itemPrice,
                        itemRestValue,
                        packId,
                        itemRoundNum,
                        itemType
                    );
                    updatedItemIds.push(parseInt(insertRes, 10)); // 记录插入的新记录 ID
                }
            }
        }

        // 删除不再需要的物品
        const itemsToDelete = existingItems.filter((id) => !updatedItemIds.includes(id)); // 找出未涉及更新或新增的物品 ID
        console.log("existing:", existingItems, "updated:", updatedItemIds, "toDelete:", itemsToDelete);
        if (itemsToDelete.length > 0) {
            await PackModel.deleteItemsByIds(connection, itemsToDelete); // 删除这些物品
        }

        // ビデオ（pack_draw_videos）の挿入・更新・削除 (並列なし)
        if (req.body.videos && req.body.videos !== "undefined") {
            // まず既存のビデオID一覧を取得
            const existingVideos = await PackModel.getPackDrawVideosByPackId(connection, packId);
            const newVideoIds = Object.keys(videoIds).flatMap((rank) => videoIds[rank]);

            // 削除対象を絞り込み
            const videosToDelete = existingVideos.filter((video) => !newVideoIds.includes(video.video_id));
            if (videosToDelete.length > 0) {
                const videoIdsToDelete = videosToDelete.map((v) => v.db_id);
                await PackModel.deletePackDrawVideosByIds(connection, videoIdsToDelete, packId);
            }

            // 新しいビデオの挿入 or 更新
            for (const rank of Object.keys(videoIds)) {
                for (const id of videoIds[rank]) {
                    const packVideos = await PackModel.getPackVideoById(connection, id);
                    if (packVideos.length > 0) {
                        for (const v of packVideos) {
                            const existingVideo = await PackModel.checkPackDrawVideoExists(connection, v.id, packId);
                            if (existingVideo.length > 0) {
                                // update
                                await PackModel.updatePackDrawVideo(connection, v.video_path, rank, v.id, packId);
                            } else {
                                // insert
                                await PackModel.insertPackDrawVideo(connection, v.video_path, packId, rank, v.id);
                            }
                        }
                    }
                }
            }
        }

        await new Promise((resolve, reject) => {
            connection.commit((err) => {
                if (err) return reject(err);
                resolve();
            });
        });

        res.status(200).json({packId});
        logAdminPut(`ガチャが更新されました。 - update-pack`);

        // ロック解放
        await lock.release();
        console.log(`锁释放成功: ${lockKey}`);
    } catch (error) {
        if (connection) {
            await new Promise((resolve) => {
                connection.rollback(() => resolve());
            });
        }

        console.error("Failed to update pack and items:", error);
        logErr(`ガチャの更新ができませんでした。エラー内容:${error} - controller_adminPackController_updatePack`);

        if (error && error.code === "ER_LOCK_DEADLOCK") {
            // ログには残す
            console.error("Deadlock occurred:", error);
            // ユーザーにはやわらかい文言を返す
            return res.status(500).json({
                message: "混雑のため処理に失敗しました。しばらくしてからお試しください。(ポイント等は減りません。)",
            });
        }

        if (lock) {
            try {
                await lock.release();
                console.log(`ロック解放成功: ${lockKey}`);
            } catch (releaseErr) {
                // release に失敗してもサーバは落とさずログを残す
                console.error("ロック解放失敗:", releaseErr);
            }
        }

        return res.status(500).json({message: error.message});
    } finally {
        if (connection) connection.release();
    }
};


/* ---------------------------------------------------------------------
該当のパックを管理画面上で非表示にする
-----------------------------------------------------------------------*/
exports.adminDisplayChange = async (req, res) => {
    const packId = req.body.id;

    try {
        await PackModel.deletePackInfo(packId);
        res.status(200).json({message: "該当ガチャを削除しました。"});
    } catch (error) {
        console.error("Failed to delete pack:", error);
        res.status(500).send("Failed to delete pack");
        logErr(`ガチャの削除に失敗しました。エラー内容: ${error} - controller_adminPackController_adminDisplayChange`);
    }
};

/* ---------------------------------------------------------------------
パックの並び順を変更する
-----------------------------------------------------------------------*/
exports.changeSortPackList = async (req, res) => {
    const data = req.body;

    try {
        data.sort_number.forEach(async (pack, index) => {
            await PackModel.updateSortNumber(pack.sort_number, pack.id);
        });
        res.status(200).json({message: "パックの並び順を変更しました。"});
    } catch (error) {
        console.error("Failed to get sorted pack list:", error);
    }
};

/* ---------------------------------------------------------------------
登録済みのアイテムを取得する
-----------------------------------------------------------------------*/
exports.getAddedItems = async (req, res) => {
    const {searchWord} = req.query;

    try {
        const items = await PackModel.getAddedItems(searchWord);
        const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;

        const newItems = Object.values(
            items.reduce((acc, item) => {
                if (!acc[item.name] || acc[item.name].id < item.id) {
                    acc[item.name] = {
                        id: item.id,
                        name: item.name,
                        price: item.price,
                        type: item.type,
                        image: baseURL + item.image,
                    };
                }
                return acc;
            }, {})
        );

        res.status(200).json(newItems);
    } catch (error) {
        console.error("Failed to get added items:", error);
        res.status(500).send("Failed to get added items");
    }
};
