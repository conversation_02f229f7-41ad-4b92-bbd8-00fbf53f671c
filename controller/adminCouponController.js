const BuyItem = require("../model/adminBuyItem");
const { logAdminPut, logErr } = require("../logger");
const Res = require("../util/responseHelpers");
const Coupon = require("../model/adminCoupon");

/* --------------------------------------------------------------------- 
課金用の割引クーポンを作成
-----------------------------------------------------------------------*/
exports.createBuyItemDiscountCoupon = async (req, res) => {
  const { data, name, rest, term } = req.body;

  try {
    // クーポンコードの生成
    const generateCouponKey = () => {
      return Math.random().toString(36).substring(2, 18);
    };

    let code;
    let isDuplicate = true;

    while (isDuplicate) {
      // 新しいコードを生成
      code = generateCouponKey();

      // 重複チェックを実行
      const codeCheck = await Coupon.checkCouponCode(code);

      // 重複がなければループを抜ける
      if (codeCheck.length === 0) {
        isDuplicate = false;
      }
    }

    // データを追加
    for (const item of data) {
      await Coupon.addDiscountCoupon(code, item.buy_item_id, item.discount, rest, name, term);
    }

    Res.resJsonFn(res, 200, null, "The coupon has been created.");
  } catch (error) {
    console.log(error);
    logErr(`課金用の割引クーポンを作成できませんでした。エラー内容:${error} - adminCouponController_createBuyItemDiscountCoupon`);
  }
};

/* --------------------------------------------------------------------- 
課金用のクーポン取得
-----------------------------------------------------------------------*/
exports.getDiscountCoupons = async (req, res) => {
  console.log("叩かれた");
  try {
    const coupons = await Coupon.getDiscountCoupons();
    Res.resJsonDataFn(res, 200, null, "Retrieved the list of coupons.", coupons);
  } catch (error) {
    logErr(`課金用のクーポンを取得できませんでした。エラー内容:${error} - adminCouponController_getDiscountCoupons`);
  }
};

/* --------------------------------------------------------------------- 
課金用のクーポン削除
-----------------------------------------------------------------------*/
exports.removeDiscountCoupon = async (req, res) => {
  const { code } = req.body;

  try {
    await Coupon.removeDiscountCoupon(code);
    Res.resJsonFn(res, 200, null, "The coupon has been removed.");
  } catch (error) {
    logErr(`課金用のクーポンを削除できませんでした。エラー内容:${error} - adminCouponController_removeDiscountCoupon`);
  }
};
