const adminUser = require("../model/adminUser");
const Res = require("../util/responseHelpers");
const { logUserPut, logErr, logPaymentPut } = require("../logger");
const { formatNumber } = require("../util/formatNumber");

/* --------------------------------------------------------------------- 
ユーザー情報を検索
-----------------------------------------------------------------------*/
exports.searchUsers = async (req, res) => {
  // getメソッドで取得されるtermを格納
  const term = req.query.term;

  try {
    const result = await adminUser.searchUsers(term);
    Res.resJsonDataFn(res, 200, null, "User information has been retrieved", result);
  } catch (err) {
    logErr(`ユーザー情報を検索できませんでした。エラー内容:${err} - controller_adminUser_searchUsers`);
    console.log(err);
  }
};

/* --------------------------------------------------------------------- 
該当IDのユーザー情報を取得
-----------------------------------------------------------------------*/
exports.getIdUser = async (req, res) => {
  // getメソッドで取得されるidを格納
  const id = req.query.id;

  console.log(id);

  try {
    const result = await adminUser.getIdUser(id);
    Res.resJsonDataFn(res, 200, null, "User information has been retrieved", result);
  } catch (err) {
    logErr(`ユーザー情報を取得できませんでした。エラー内容:${err} - controller_adminUser_getIdUser`);
    console.log(err);
  }
};

/* ---------------------------------------------------------------------- 
該当IDのユーザーのポイント情報を更新
-----------------------------------------------------------------------*/
exports.updateUserPoint = async (req, res) => {
  // postメソッドで取得されるid, pointを格納
  const id = req.body.id;
  const point = req.body.point;
  const gp = req.body.gp;

  try {
    const userPastPoint = await adminUser.getUserPoint(id);
    const userPastGp = await adminUser.getUserGp(id);
    const result = await adminUser.updateUserPoint(id, point, gp);
    const userNewPoint = await adminUser.getUserPoint(id);
    const userNewGp = await adminUser.getUserGp(id);

    logUserPut(`ユーザー[${id}]のポイント情報を更新しました。更新内容:${point} - controller_adminUser_updateUserPoint`);
    logPaymentPut(`ユーザー[${id}]のポイント情報を更新しました。PT更新前:${formatNumber(userPastPoint)} PT更新後:${formatNumber(userNewPoint)} GP更新前:${formatNumber(userPastGp)} GP更新後:${formatNumber(userNewGp)} - controller_adminUser_updateUserPoint`);

    Res.resJsonDataFn(res, 200, null, "User's point information has been updated", result);
  } catch (err) {
    logErr(`ユーザーのポイント情報を更新できませんでした。エラー内容:${err} - controller_adminUser_updateUserPoint`);
    console.log(err);
  }
};

/* --------------------------------------------------------------------- 
該当ユーザーの状態を非表示にする
-----------------------------------------------------------------------*/
exports.removeUser = async (req, res) => {
  // postメソッドで取得されるidを格納
  const id = req.body.removeNumber;

  try {
    const result = await adminUser.removeUser(id);
    Res.resJsonDataFn(res, 200, null, "User has been deleted from the admin panel", result);
  } catch (err) {
    logErr(`ユーザーを非表示にできませんでした。エラー内容:${err} - controller_adminUser_hideUser`);
    console.log(err);
  }
};

/* --------------------------------------------------------------------- 
管理者のメールアドレスを取得
-----------------------------------------------------------------------*/
exports.getAdminUser = async (req, res) => {
  const user = req.user;
  console.log("admin_user" + user);
  try {
    Res.resJsonDataFn(res, 200, null, "Admin email address has been retrieved", user.mail);
  } catch (err) {
    logErr(`管理者のメールアドレスを取得できませんでした。エラー内容:${err} - controller_adminUser_getAdminEmail`);
    console.log(err);
  }
};
