const UserTransaction = require("../model/userTransaction");

exports.getTransactionRecords = async (req, res) => {
    try {
    
        const userId = req.user.userId;
        const type = req.query.type; // 动态传入
        if (!type) {
            return res.status(400).json({ message: "type parameter cannot be empty" });
        }
        const pageNum = parseInt(req.query.pageNum) || 1;
        const pageSize = parseInt(req.query.pageSize) || 10;
        const offset = pageSize * (pageNum - 1);

        console.log(`查询交易/支付记录 userId: ${userId}, type: ${type}, pageNum: ${pageNum}, offset: ${offset}`);

        const [records, totalCount] = await Promise.all([
            UserTransaction.getRecordsByType(userId, type, offset, pageSize),
            UserTransaction.getRecordsCountByType(userId, type)
        ]);
        const totalPage = Math.ceil(totalCount / pageSize);

        res.status(200).json({
            records,
            totalPage
        });
    } catch (err) {
        console.error("查询交易/支付记录失败:", err);
        res.status(500).json({ message: "Failed to query records" });
    }
};

exports.getRechargeAndConsumeRecords = async (req, res) => {
    try {
        const userId = req.user.userId;
        const records = await UserTransaction.getRechargeAndConsumeRecords(userId);
        res.status(200).json({
            records
        });
    } catch (err) {
        console.error("查询充值和消费记录失败:", err);
        res.status(500).json({ message: "Failed to query records" });
    }
}

exports.getRechargeRecordsForAdmin = async (req, res) => {
    try {
        const { userId } = req.params;
        if (!userId) {
            return res.status(400).json({ message: "userId parameter cannot be empty" });
        }
        const records = await UserTransaction.getRechargeRecords(userId);
        res.status(200).json({
            records
        });
    } catch (err) {
        console.error("查询指定用户的充值记录失败:", err);
        res.status(500).json({ message: "Failed to query records" });
    }
};

exports.getLatestPointChange = async (req, res) => {
    try {
        const userId = req.user.userId;
        const records = await UserTransaction.getLatestTwoRecords(userId);

        if (records.length < 2) {
            return res.status(200).json({
                before: 0,
                after: records.length === 1 ? records[0].current_balance : 0
            });
        }

        const after = records[0].current_balance;
        const before = records[1].current_balance;

        res.status(200).json({
            before,
            after
        });
    } catch (err) {
        console.error("查询最新积分变化失败:", err);
        res.status(500).json({ message: "Failed to query latest point change" });
    }
};