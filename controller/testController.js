const User = require("../model/user");
const db = require("../db/db");
const {logErr} = require("../logger");


// exports.test = async (req, res) => {
//     let connection = await new Promise((resolve, reject) => {
//         db.getConnection((err, conn) => {
//             if (err) return reject(err);
//             resolve(conn);
//         });
//     });
//     const userRankGp = await User.getGpRate(connection, 1, 7);
//     console.log(userRankGp)
// }

exports.test = async (req, res) => {
    // 从查询参数中获取 pointId
    const pointId = req.query.pointId;

    if (!pointId) {
        return res.status(400).json({ error: "pointId is required" });
    }
    // loginBonusPointsテーブルのidカラムと引数のpointIdが一致するレコードのpointカラムを取得するクエリ
    const getLoginBonusPointQuery = "SELECT * FROM loginBonusPoints WHERE id = ?";

    try {
        return new Promise((resolve, reject) => {
            db.query(getLoginBonusPointQuery, [pointId], (err, results) => {
                if (err) {
                    logErr(`ログインボーナスポイント[${pointId}]を取得できませんでした。エラー内容:${err} - model_user_getLoginBonusPoint`);
                    reject(err);
                }

                console.log(results[0]);

                resolve(results[0]);
            });
        });
    } catch (error) {
        console.log(error);
    }
};
