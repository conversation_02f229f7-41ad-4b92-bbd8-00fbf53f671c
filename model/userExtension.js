const db = require('../db/db');

exports.getOrCreateByUserId = async (connection, userId) => {
    const queryRunner = connection || db;
    return new Promise((resolve, reject) => {
        queryRunner.query('SELECT * FROM user_extension WHERE id = ?', [userId], async (err, results) => {
            if (err) return reject(err);
            if (results.length > 0) {
                resolve(results[0]);
            } else {
                queryRunner.query(
                    'INSERT INTO user_extension (id, exp, level, total_recharge, vip_level) VALUES (?, 0, 0, 0, 0)',
                    [userId],
                    (insertErr, insertResult) => {
                        if (insertErr) return reject(insertErr);
                        resolve({ id: userId, exp: 0, level: 0, total_recharge: 0, vip_level: 0 });
                    }
                );
            }
        });
    });
};

exports.updateExperienceAndLevel = async (connection, userId, newExp, newLevel) => {
    const queryRunner = connection || db;
    return new Promise((resolve, reject) => {
        queryRunner.query(
            'UPDATE user_extension SET exp = ?, level = ? WHERE id = ?',
            [newExp, newLevel, userId],
            (err, result) => {
                if (err) return reject(err);
                resolve(result);
            }
        );
    });
};

exports.updateRechargeAndVipLevel = async (userId, rechargeAmount) => {
    try {
        // 1. 获取当前的用户扩展信息
        const userExtension = await exports.getOrCreateByUserId(null, userId);
        const currentTotalRecharge = userExtension.total_recharge || 0;
        const currentVipLevel = userExtension.vip_level || 0;

        // 2. 计算新的总充值金额
        const newTotalRecharge = Number(currentTotalRecharge) + Number(rechargeAmount);

        // 3. 获取所有VIP等级定义
        const allLevels = await require('./level').getAll(null);
        const vipLevels = allLevels.filter(l => l.type === 'vip');

        // 4. 确定新的VIP等级
        let newVipLevel = currentVipLevel;
        for (let i = vipLevels.length - 1; i >= 0; i--) {
            if (newTotalRecharge >= vipLevels[i].value) {
                newVipLevel = vipLevels[i].level;
                break;
            }
        }

        // 5. 更新数据库
        return new Promise((resolve, reject) => {
            db.query(
                'UPDATE user_extension SET total_recharge = ?, vip_level = ? WHERE id = ?',
                [newTotalRecharge, newVipLevel, userId],
                (err, result) => {
                    if (err) return reject(err);
                    resolve(result);
                }
            );
        });
    } catch (error) {
        // 在这里处理或记录错误
        console.error(`更新用户[${userId}]充值与VIP等级失败:`, error);
        throw error; // 将错误重新抛出，以便调用者可以捕获它
    }
};