const db = require('../db/db');
const { logErr } = require("../logger");

// Query row by data_id
exports.getByDataId = async (connection, dataId) => {
    const query = 'SELECT * FROM dynamic_config WHERE data_id = ?';
    const queryRunner = connection || db;
    return new Promise((resolve, reject) => {
        queryRunner.query(query, [dataId], (err, results) => {
            if (err) {
                logErr(`dynamic_config getByDataId error: ${err}`);
                return reject(err);
            }
            resolve(results[0]);
        });
    });
};

// Update config by id (write native JSON, not stringified)
exports.updateConfigById = async (id, config) => {
    const query = 'UPDATE dynamic_config SET config = ? WHERE id = ?';
    return new Promise((resolve, reject) => {
        db.query(query, [config, id], (err, result) => {
            if (err) {
                logErr(`dynamic_config updateConfigById error: ${err}`);
                return reject(err);
            }
            resolve(result);
        });
    });
}; 