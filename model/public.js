const Res = require("../util/responseHelpers");
const db = require("../db/db");
require("dotenv").config();
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const { logUserPut, logErr } = require("../logger");

exports.getPackSettings = async () => {
  const settingsQuery = `
    SELECT random_order_days, sold_out_display_days
    FROM pack_settings
    WHERE id = 1
  `;
  return new Promise((resolve, reject) => {
    db.query(settingsQuery, (err, results) => {
      if (err) {
        logErr(`ガチャ設定の取得に失敗しました。エラー内容: ${err}`);
        reject(err);
      }
      resolve(results[0]);
    });
  });
};

exports.getPacks = async (statusFilter, category) => {
  let packsQuery;
  let queryParams;

  if (category === undefined) {
    packsQuery = `
      SELECT * 
      FROM pack_info 
      WHERE status IN (?, 'sold-out')
    `;
    queryParams = [statusFilter];
  } else {
    packsQuery = `
      SELECT * 
      FROM pack_info 
      WHERE pack_category LIKE ? AND status IN (?, 'sold-out')
    `;
    queryParams = [`%${category}%`, statusFilter];
  }

  return new Promise((resolve, reject) => {
    db.query(packsQuery, queryParams, (err, results) => {
      if (err) reject(err);
      resolve(results);
    });
  });
};

exports.getSpecialItemsForPack = async (packId) => {
  const itemsQuery = `
    SELECT * FROM items 
    WHERE packId = ? AND (category = 'itemsS' OR category = 'itemsA')
  `;
  return new Promise((resolve, reject) => {
    db.query(itemsQuery, [packId], (err, items) => {
      if (err) {
        console.error("Failed to fetch items for pack:", packId, err);
        reject(err);
      } else {
        resolve(items || []);
      }
    });
  });
};

exports.getJackpot = async (req) => {
  // pack_infoテーブルのstatusがinvalidとsold-out以外のデータで、そのidを取得して、itemsテーブルのpackIdがそのidのデータで、categoryがitemsSのデータを各種一つずつを全て取得するクエリ
  const jackpotQuery = `
    SELECT i.packId, i.image
    FROM items AS i
    INNER JOIN pack_info AS p ON i.packId = p.id
    WHERE p.status NOT IN ('invalid', 'sold-out')
      AND i.category = 'itemsS'
      AND i.id = (
        SELECT MIN(id)
        FROM items AS sub_i
        WHERE sub_i.packId = i.packId
          AND sub_i.category = 'itemsS'
      );
  `;

  const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;

  return new Promise((resolve, reject) => {
    db.query(jackpotQuery, (err, jackpot) => {
      if (err) {
        console.error("Failed to fetch jackpot:", err);
        reject(err);
      } else {
        const data = jackpot.map((item) => {
          return {
            ...item,
            packId: item.packId,
            image: baseURL + item.image,
          };
        });
        resolve(data);
      }
    });
  });
};

exports.getPackById = async (packId) => {
  const query = "SELECT * FROM pack_info WHERE id = ?";
  return new Promise((resolve, reject) => {
    db.query(query, [packId], (err, results) => {
      if (err) {
        logErr(`Failed to get pack by id [${packId}]. Error: ${err}`);
        reject(err);
      }
      resolve(results[0]);
    });
  });
};

exports.getPackItems = async (packId) => {
  const query = "SELECT * FROM items WHERE packId = ?";
  return new Promise((resolve, reject) => {
    db.query(query, [packId], (err, results) => {
      if (err) {
        logErr(`該当のアイテム詳細[${packId}]を取得できませんでした。エラー内容:${err} - detail-items`);
        reject(err);
      }
      resolve(results);
    });
  });
};

exports.getPackStatus = async (packId) => {
  const query = "SELECT status FROM pack_info WHERE id = ?";
  return new Promise((resolve, reject) => {
    db.query(query, [packId], (err, results) => {
      if (err) {
        logErr(`該当のパックのステータス[${packId}]を取得できませんでした。エラー内容:${err} - detail-items`);
        reject(err);
      }
      resolve(results[0]);
    });
  });
};