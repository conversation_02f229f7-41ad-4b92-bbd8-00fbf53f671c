const db = require('../db/db');

// 添加一条交易记录
const add = async (connection, userId, type, value, relatedId, remark, currentBalance) => {
    const queryRunner = connection || db; // 如果传入 connection，则使用它，否则使用默认 db 连接
    const fields = ['user_id', 'type', 'value', 'related_id', 'remark'];
    const params = [userId, type, value, relatedId, remark];

    // 如果提供了当前余额，则记录
    if (currentBalance !== undefined && currentBalance !== null) {
        fields.push('current_balance');
        params.push(currentBalance);
    }

    const placeholders = params.map(() => '?').join(', ');
    const sql = `INSERT INTO user_transactions (${fields.join(', ')}) VALUES (${placeholders})`;

    return new Promise((resolve, reject) => {
        queryRunner.query(sql, params, (err, result) => {
            if (err) return reject(err);
            resolve(result);
        });
    });
};

const getRecordsByType = async (userId, type, offset, limit) => {
    const sql = `
        SELECT id, value, current_balance, related_id, remark, created_at
        FROM user_transactions
        WHERE user_id = ? AND type = ?
        ORDER BY created_at DESC
        LIMIT ? OFFSET ?
    `;
    return new Promise((resolve, reject) => {
        db.query(sql, [userId, type, limit, offset], (err, results) => {
            if (err) return reject(err);
            resolve(results);
        });
    });
};

// 查询指定类型的记录总数
const getRecordsCountByType = async (userId, type) => {
    const sql = `
        SELECT COUNT(*) as count
        FROM user_transactions
        WHERE user_id = ? AND type = ?
    `;
    return new Promise((resolve, reject) => {
        db.query(sql, [userId, type], (err, results) => {
            if (err) return reject(err);
            resolve(results[0].count);
        });
    });
};


const getRechargeAndConsumeRecords = async (userId) => {
    const sql = `
        SELECT id, value, current_balance, related_id, remark, created_at, type
        FROM user_transactions
        WHERE user_id = ? AND type IN ('recharge', 'consume')
        ORDER BY created_at DESC
    `;
    return new Promise((resolve, reject) => {
        db.query(sql, [userId], (err, results) => {
            if (err) return reject(err);
            resolve(results);
        });
    });
};

const getRechargeRecords = async (userId) => {
    const sql = `
        SELECT *
        FROM user_transactions
        WHERE user_id = ? AND type = 'recharge'
        ORDER BY created_at DESC
    `;
    return new Promise((resolve, reject) => {
        db.query(sql, [userId], (err, results) => {
            if (err) return reject(err);
            resolve(results);
        });
    });
};

const getLatestTwoRecords = async (userId) => {
    const sql = `
        SELECT current_balance
        FROM user_transactions
        WHERE user_id = ?
        ORDER BY id DESC
        LIMIT 2
    `;
    return new Promise((resolve, reject) => {
        db.query(sql, [userId], (err, results) => {
            if (err) return reject(err);
            resolve(results);
        });
    });
};

module.exports = {
    add,
    getRecordsByType,
    getRecordsCountByType,
    getRechargeAndConsumeRecords,
    getRechargeRecords,
    getLatestTwoRecords
};