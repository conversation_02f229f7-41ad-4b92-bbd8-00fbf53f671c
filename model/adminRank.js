// RankModel.js
const { log } = require("console");
const db = require("../db/db");
const { logAdminPut, logErr } = require("../logger");
require("dotenv").config();

/* ---------------------------------------------------------------------
ランクを追加
-----------------------------------------------------------------------*/
// exports.insertRank = async (name, point, gpPercent, bonusPoint, backImg, order, buyAddPointPercent) => {
//   const query = "INSERT INTO `rank` (name, point, gp, bonus, back_img, `order`, buy_add_point_percent) VALUES (?, ?, ?, ?, ?, ?, ?)";
//   try {
//     const result = await db.query(query, [name, point, gpPercent, bonusPoint, backImg, order, buyAddPointPercent]);
//     logAdminPut("ランクアイテムがデータベースに正常に挿入されました。");
//     return result;
//   } catch (error) {
//     logErr(`データベースへのランクアイテムの挿入に失敗しました。エラー内容: ${error} - model_adminRank_insertRank`);
//     throw error;
//   }
// };

exports.insertRank = async (name, point, gpPercent, bonusPoint, backImg, order, buyAddPointPercent) => {
  const query = "INSERT INTO `rank` (name, point, gp, bonus, back_img, `order`, buy_add_point_percent) VALUES (?, ?, ?, ?, ?, ?, ?)";

  try {
    // 打印完整的 SQL 查询（包括参数值）
    const formattedQuery = formatQuery(query, [name, point, gpPercent, bonusPoint, backImg, order, buyAddPointPercent]);
    console.log("Executing full SQL query:", formattedQuery);

    // 执行查询
    const result = await db.query(query, [name, point, gpPercent, bonusPoint, backImg, order, buyAddPointPercent]);
    logAdminPut("ランクアイテムがデータベースに正常に挿入されました。");
    return result;
  } catch (error) {
    console.error("Error during database insertion:", error); // 打印详细错误信息
    logErr(`データベースへのランクアイテムの挿入に失敗しました。エラー内容: ${error} - model_adminRank_insertRank`);
    throw error;
  }
};

// 辅助函数：格式化 SQL 查询
function formatQuery(query, params) {
  return query.replace(/\?/g, () => {
    const value = params.shift();
    if (typeof value === "string") {
      return `'${value}'`; // 字符串需要加引号
    }
    return value; // 数字、布尔值等直接返回
  });
}

/* ---------------------------------------------------------------------
ランクを取得
-----------------------------------------------------------------------*/
exports.getAllRanks = (callback) => {
  const query = "SELECT * FROM `rank`";
  db.query(query, (error, results) => {
    if (error) {
      logErr(`データベースからランクデータの取得に失敗しました。エラー内容: ${error} - model_adminRank_getAllRanks`);
      callback(error, null);
    } else {
      callback(null, results);
    }
  });
};

/* ---------------------------------------------------------------------
IDで指定したランク情報取得
-----------------------------------------------------------------------*/
exports.getRank = async (id, req) => {
  const query = "SELECT * FROM `rank` WHERE id = ?";

  return new Promise((resolve, reject) => {
    db.query(query, [id], (error, result) => {
      if (error) {
        logErr(`データベースから指定したIDのランクデータの取得に失敗しました。エラー内容: ${error} - model_adminRank_getRank`);
        reject(error);
      } else {
        // back_imgについてはbaseURLとくっつけて返す
        const baseURL = `${process.env.NODE_ENV === "production" ? process.env.PRODUCTION_DOMAIN : req.protocol + "://" + req.get("host")}/`;
        resolve({ ...result[0], back_img: baseURL + result[0].back_img });
      }
    });
  });
};

/* ---------------------------------------------------------------------
指定したIDのランク情報を更新（画像が存在する場合）
-----------------------------------------------------------------------*/
exports.updateRankYesImage = async (id, name, point, gpPercent, bonusPoint, backImg, order, buyAddPointPercent) => {
  const query = "UPDATE `rank` SET name = ?, point = ?, gp = ?, bonus = ?, back_img = ?, `order` = ?, buy_add_point_percent = ? WHERE id = ?";
  return new Promise((resolve, reject) => {
    db.query(query, [name, point, gpPercent, bonusPoint, backImg, order, buyAddPointPercent, id], (error, result) => {
      if (error) {
        logErr(`データベースへのランクアイテムの更新に失敗しました。エラー内容: ${error} - model_adminRank_updateRankYesImage`);
        reject(error);
      } else {
        logAdminPut("ランクアイテムがデータベースに正常に更新されました。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
指定したIDのランク情報を更新（画像が存在しない場合）
-----------------------------------------------------------------------*/
exports.updateRankNoImage = async (id, name, point, gpPercent, bonusPoint, order, buyAddPointPercent) => {
  const query = "UPDATE `rank` SET name = ?, point = ?, gp = ?, bonus = ?, `order` = ?, buy_add_point_percent = ? WHERE id = ?";
  return new Promise((resolve, reject) => {
    db.query(query, [name, point, gpPercent, bonusPoint, order, buyAddPointPercent, id], (error, result) => {
      if (error) {
        logErr(`データベースへのランクアイテムの更新に失敗しました。エラー内容: ${error} - model_adminRank_updateRankNoImage`);
        reject(error);
      } else {
        logAdminPut("ランクアイテムがデータベースに正常に更新されました。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
ランクの順序を更新
-----------------------------------------------------------------------*/
exports.changeOrder = async (items) => {
  try {
    const queries = items.map((item) => {
      const { id, order } = item;
      return db.query("UPDATE `rank` SET `order` = ? WHERE id = ?", [order, id]);
    });

    await Promise.all(queries);
    return { success: true, message: "Orders updated successfully." };
  } catch (error) {
    console.error("Failed to update orders:", error);
    logErr(`ランクの順序の更新に失敗しました。エラー内容: ${error} - model_adminRank_changeOrder`);
    return { success: false, message: "Error updating orders." };
  }
};

/* ---------------------------------------------------------------------
指定したIDのランクを削除
-----------------------------------------------------------------------*/
exports.removeRank = async (removeId) => {
  try {
    const query = "DELETE FROM `rank` WHERE id = ?";
    return db.query(query, [removeId]);
  } catch (error) {
    logErr(`データベースから指定したIDのランクデータの削除に失敗しました。エラー内容: ${error} - model_adminRank_removeRank`);
    console.log(error);
  }
};
