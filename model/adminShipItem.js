const db = require("../db/db");
const { logAdminPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
該当アイテムのポイントを取得
-----------------------------------------------------------------------*/
exports.getShipItemPoint = async (connection, itemId) => {
  // user_itemsテーブルのidカラムとitemIdが一致するレコードのpointカラムの情報を取得するクエリ
  const query = `
    SELECT 
      price 
    FROM 
      user_items 
    WHERE 
      id = ?;
  `;

  return new Promise((resolve, reject) => {
    connection.query(query, [itemId], (err, result) => {
      if (err) {
        logErr(`該当アイテムのポイントを取得できませんでした。エラー内容:${err} - model_adminShipItem_getShipItemPoint`);
        reject(err);
      } else {
        resolve(result[0].price);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当アイテムの削除
-----------------------------------------------------------------------*/
exports.deleteShipItem = async (connection, itemId) => {
  // shipping_statusesテーブルから、itemIdと一致するuser_item_idカラムのレコードを削除するクエリ
  const query = `
    DELETE FROM 
      shipping_statuses 
    WHERE 
      user_item_id = ?;
  `;

  return new Promise((resolve, reject) => {
    connection.query(query, [itemId], (err, result) => {
      if (err) {
        logErr(`該当アイテムの削除に失敗しました。エラー内容:${err} - model_adminShipItem_deleteShipItem`);
        reject(err);
      } else {
        resolve();
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当アイテムの所持数を1減らす
-----------------------------------------------------------------------*/
exports.decrementUserItem = async (connection, itemId) => {
  // user_itemsテーブルのidカラムとitemIdが一致するレコードのquantityカラムを1減らすクエリ
  const query = `
    UPDATE 
      user_items 
    SET 
      quantity = quantity - 1 
    WHERE 
      id = ?;
  `;

  return new Promise((resolve, reject) => {
    connection.query(query, [itemId], (err, result) => {
      if (err) {
        logErr(`該当アイテムの所持数を1減らすに失敗しました。エラー内容:${err} - model_adminShipItem_decrementUserItem`);
        reject(err);
      } else {
        resolve();
      }
    });
  });
};
