// RankModel.js
const db = require("../db/db");
const { logAdminPut, logErr } = require("../logger");

/* ---------------------------------------------------------------------
パックの取得
-----------------------------------------------------------------------*/
exports.getPack = (packId) => {
  const packInfoQuery = "SELECT * FROM pack_info WHERE id = ?";

  return new Promise((resolve, reject) => {
    db.query(packInfoQuery, [Number(packId)], (error, results) => {
      if (error) {
        logErr(`データベースからパックデータの取得に失敗しました。エラー内容: ${error} - model_adminPack_getPack`);
        return reject(error);
      }
      resolve(results);
    });
  });
};

/* ---------------------------------------------------------------------
パックのアイテム取得
-----------------------------------------------------------------------*/
exports.getPackItems = (packId) => {
  const packItemsQuery = "SELECT * FROM items WHERE packId = ?";

  return new Promise((resolve, reject) => {
    db.query(packItemsQuery, [Number(packId)], (error, results) => {
      if (error) {
        logErr(`データベースからパックアイテムの取得に失敗しました。エラー内容: ${error} - model_adminPack_getPackItems`);
        return reject(error);
      }
      resolve(results);
    });
  });
};

/* ---------------------------------------------------------------------
パックの動画取得
-----------------------------------------------------------------------*/
exports.getPackVideos = (packId) => {
  const packVideosQuery = "SELECT * FROM pack_draw_videos WHERE pack_id = ?";

  return new Promise((resolve, reject) => {
    db.query(packVideosQuery, [Number(packId)], (error, results) => {
      if (error) {
        logErr(`データベースからパック動画の取得に失敗しました。エラー内容: ${error} - model_adminPack_getPackVideos`);
        return reject(error);
      }
      resolve(results);
    });
  });
};

/* ---------------------------------------------------------------------
パックが存在するか確認
-----------------------------------------------------------------------*/
exports.packCheck = (connection, packId) => {
  const packCheckQuery = "SELECT id FROM pack_info WHERE id = ?";

  return new Promise((resolve, reject) => {
    connection.query(packCheckQuery, [packId], (err, result) => {
      if (err) {
        logErr(`データベースにパックが存在するかを確認するのに失敗しました。エラー内容: ${err} - model_adminPack_packCheck`);
        reject(err);
      } else {
        console.log("パックの確認が終わりましたよ");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
サムネイル画像がアップロードされている場合の基本情報のアップデート
-----------------------------------------------------------------------*/
exports.thumbnailYesUpdatePack = (connection, packName, desc,andMore,packPoint, packThumbnailFilePath, packReduction, packRest, packCat, packAllRest, dayOnly, rankOrder, displayDays, allDraw, packId, limitValue, timerDate, mysteryPack, resaleStatus) => {
  const statusChangeQuery = "UPDATE pack_info SET name = ?, pack_desc = ?,and_more = ?,point = ?, image = ?, reduction = ?, rest = ?, pack_category = ?, allRest = ?, day_only = ?, `rank` = ?, display_days = ?, all_draw = ?, limit_value = ?, timer_date = ?, mystery_pack = ?, resale = ? WHERE id = ?";

  return new Promise((resolve, reject) => {
    connection.query(statusChangeQuery, [packName, desc,andMore,packPoint, packThumbnailFilePath, packReduction, packRest, packCat, packAllRest, dayOnly, rankOrder === "" ? null : rankOrder, displayDays, allDraw, limitValue, timerDate, mysteryPack, resaleStatus, packId], (err, result) => {
      if (err) {
        logErr(`サムネイルがある場合のパックの更新に失敗しました。エラー内容: ${err} - model_adminPack_thumbnailYesUpdatePack`);
        reject(err);
      } else {
        console.log("サムネイルがある場合のパックの更新が終わりましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
サムネイル画像がアッロードされてない場合の基本情報のアップデート
-----------------------------------------------------------------------*/
// exports.thumbnailNotUpdatePack = (connection, packName, packPoint, packReduction, packRest, packCat, packAllRest, dayOnly, rankOrder, displayDays, allDraw, limitValue, packId, timerDate, mysteryPack, resaleStatus) => {
//   const statusChangeQuery = "UPDATE pack_info SET name = ?, point = ?, reduction = ?, rest = ?, pack_category = ?, allRest = ?, day_only = ?, `rank` = ?, display_days = ?, all_draw = ?, limit_value = ?, timer_date = ?, mystery_pack = ?, resale = ? WHERE id = ?";
//
//   return new Promise((resolve, reject) => {
//     connection.query(statusChangeQuery, [packName, packPoint, packReduction, packRest, packCat, packAllRest, dayOnly, rankOrder === "" ? null : rankOrder, displayDays, allDraw, limitValue, timerDate, mysteryPack, resaleStatus, packId], (err, result) => {
//       if (err) {
//         logErr(`サムネイルがない場合のパックの更新に失敗しました。エラー内容: ${err} - model_adminPack_thumbnailNotUpdatePack`);
//         reject(err);
//       } else {
//         console.log("サムネイルがない場合のパックの更新が終わりましたよ。");
//         resolve(result);
//       }
//     });
//   });
// };

exports.thumbnailNotUpdatePack = (connection, packName,desc,andMore, packPoint, packReduction, packRest, packCat, packAllRest, dayOnly, rankOrder, displayDays, allDraw, limitValue, packId, timerDate, mysteryPack, resaleStatus) => {
  // 检查必要参数是否存在
  if (!packId) {
    const errorMsg = `[错误] 卡包ID未提供，无法更新！`;
    console.error(errorMsg);
    return Promise.reject(new Error(errorMsg));
  }

  const statusChangeQuery = "UPDATE pack_info SET name = ?,pack_desc = ?,and_more = ?, point = ?, reduction = ?, rest = ?, pack_category = ?, allRest = ?, day_only = ?, `rank` = ?, display_days = ?, all_draw = ?, limit_value = ?, timer_date = ?, mystery_pack = ?, resale = ? WHERE id = ?";

  console.log(`[SQL执行] 准备更新无缩略图的卡包信息:
  SQL: ${statusChangeQuery}
  参数:
  - 卡包ID: ${packId || "undefined（错误！必须提供）"}
  - 卡包名称: ${packName}
  - 点数: ${packPoint}
  - 转售状态: ${resaleStatus || "undefined（可能未设置）"}`);

  return new Promise((resolve, reject) => {
    connection.query(
        statusChangeQuery,
        [packName, desc,andMore, packPoint, packReduction, packRest, packCat, packAllRest, dayOnly, rankOrder === "" ? null : rankOrder, displayDays, allDraw, limitValue, timerDate, mysteryPack, resaleStatus, packId],
        (err, result) => {
          if (err) {
            const errorMsg = `[操作失败] 卡包更新失败，ID: ${packId}，错误: ${err.message}`;
            console.error(errorMsg);
            reject(new Error(errorMsg));
          } else if (result.affectedRows === 0) {
            const warningMsg = `[操作警告] 卡包更新成功，但未影响任何行！可能ID不存在: ${packId}`;
            console.warn(warningMsg);
            resolve(result); // 仍然 resolve，但日志警告
          } else {
            const successMsg = `[操作成功] 卡包更新完成:
          受影响行数: ${result.affectedRows}
          卡包ID: ${packId}
          新名称: ${packName}
          新点数: ${packPoint}
          新转售状态: ${resaleStatus}`;
            console.log(successMsg);
            resolve(result);
          }
        }
    );
  });
};
/* ---------------------------------------------------------------------
新規パックの基本情報を登録
-----------------------------------------------------------------------*/
exports.insertPackInfo = (
    connection,
    packName,
    desc,
    andMore,
    packPoint,
    packThumbnailFilePath,
    packReduction,
    packRest,
    packCat,
    packAllRest,
    dayOnly,
    rankOrder,
    displayDays,
    limitValue,
    allDraw,
    timerDate,
    mysteryPack,
    sortNumber,
    resaleStatus
) => {
  // 插入 SQL 语句
  const statusChangeQuery = `
    INSERT INTO pack_info (
      name, 
      pack_desc, 
      and_more,
      point, 
      image, 
      reduction, 
      rest, 
      pack_category, 
      allRest, 
      status, 
      day_only, 
      \`rank\`, 
      display_days, 
      all_draw, 
      limit_value, 
      timer_date, 
      mystery_pack, 
      sort_number, 
      resale
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;

  return new Promise((resolve, reject) => {
    // 执行 SQL 查询
    connection.query(
        statusChangeQuery,
        [
          packName,
          desc,
          andMore,
          packPoint,
          packThumbnailFilePath,
          packReduction,
          packRest,
          packCat,
          packAllRest,
          "invalid", // 状态默认值
          dayOnly,
          rankOrder === "" ? null : rankOrder, // 如果 rankOrder 是空字符串，则设置为 NULL
          displayDays,
          allDraw,
          limitValue,
          timerDate,
          mysteryPack,
          sortNumber,
          resaleStatus,
        ],
        (err, result) => {
          if (err) {
            // 错误日志（改为中文）
            logErr(`新增卡包基本信息失败。错误内容: ${err} - model_adminPack_insertPackInfo`);
            reject(err);
          } else {
            // 成功日志（改为中文）
            console.log("新增卡包基本信息成功。");
            resolve(result.insertId); // 返回插入记录的 ID
          }
        }
    );
  });
};

/* ---------------------------------------------------------------------
そのパックのIDを持つアイテムを全てを取得
-----------------------------------------------------------------------*/
exports.existingItems = (connection, packId) => {
  const existingItemsQuery = "SELECT id FROM items WHERE packId = ?";

  return new Promise((resolve, reject) => {
    connection.query(existingItemsQuery, [packId], (err, results) => {
      if (err) {
        logErr(`そのパックのIDを持つアイテムを全て取得できませんでした。エラー内容: ${err} - model_adminPack_existingItems`);
        reject(err);
      } else {
        console.log("そのパックのIDを持つアイテムを全て取得しましたよ。");
        resolve(results.map((item) => item.id));
      }
    });
  });
};

/* ---------------------------------------------------------------------
そのアイテムが該当のパックに存在するかを確認
-----------------------------------------------------------------------*/
exports.itemCheck = (connection, itemId, packId, category) => {
  const itemCheckQuery = "SELECT id FROM items WHERE id = ? AND packId = ? AND category = ?";

  return new Promise((resolve, reject) => {
    connection.query(itemCheckQuery, [itemId, packId, category], (err, result) => {
      if (err) {
        logErr(`そのアイテムが該当のパックに存在するかを確認できませんでした。エラー内容: ${err} - model_adminPack_itemCheck`);
        reject(err);
      } else {
        console.log(`カテゴリー${category}アイテムが該当のパックに存在するかを確認しましたよ。`);
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
アイテムに画像が存在する場合のアイテムのアップデート
-----------------------------------------------------------------------*/
exports.itemImageYesUpdate = (connection, itemName, itemPrice, imagePath, itemRest, itemType, itemOrder, itemDisplayRest, itemShipOnly, itemId, packId) => {
  const itemUpdateQuery = "UPDATE items SET name = ?, price = ?, image = ?, rest = ?, type = ?, `order` = ?, display_rest = ?, is_ship_only = ? WHERE id = ? AND packId = ?";

  return new Promise((resolve, reject) => {
    connection.query(itemUpdateQuery, [itemName, itemPrice, imagePath, itemRest, itemType, itemOrder, itemDisplayRest, itemShipOnly, itemId, packId], (err, result) => {
      if (err) {
        logErr(`アイテム画像がある場合のアイテムの更新に失敗しました。エラー内容: ${err} - model_adminPack_itemImageYesUpdate`);
        reject(err);
      } else {
        console.log("アイテムの更新が終わりましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
アイテムに画像が存在する場合のアイテムのアップデート
-----------------------------------------------------------------------*/
exports.itemImageNoUpdate = (connection, itemName, itemPrice, itemRest, itemType, itemOrder, itemDisplayRest, itemShipOnly, itemId, packId) => {
  const itemUpdateQuery = "UPDATE items SET name = ?, price = ?, rest = ?, type = ?, `order` = ?, display_rest = ?, is_ship_only = ? WHERE id = ? AND packId = ?";

  return new Promise((resolve, reject) => {
    connection.query(itemUpdateQuery, [itemName, itemPrice, itemRest, itemType, itemOrder, itemDisplayRest, itemShipOnly, itemId, packId], (err, result) => {
      if (err) {
        logErr(`アイテム画像がない場合のアイテムの更新に失敗しました。エラー内容: ${err} - model_adminPack_itemImageNoUpdate`);
        reject(err);
      } else {
        console.log("画像がない場合のアイテムの更新が終わりましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
新規アイテムに画像が存在している場合の新規アイテム登録
-----------------------------------------------------------------------*/
exports.ImageYesInsertItem = (connection, rank, itemName, itemPrice, imagePath, itemRest, packId, itemType, itemOrder, itemDisplayRest, itemShipOnly) => {
  const itemInsertQuery = "INSERT INTO items (category, name, price, image, rest, packId, type, `order`, display_rest, is_ship_only) VALUES (?,?,?,?,?,?,?,?,?,?)";

  return new Promise((resolve, reject) => {
    connection.query(itemInsertQuery, [`items${rank}`, itemName, itemPrice, imagePath, itemRest, packId, itemType, itemOrder, itemDisplayRest, itemShipOnly], (err, result) => {
      if (err) {
        logErr(`画像がある新規アイテムの登録に失敗しました。エラー内容: ${err} - model_adminPack_ImageYesInsertItem`);
        reject(err);
      } else {
        console.log("画像がある新規アイテムの登録が終わりましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
新規アイテムに画像が存在していない場合の新規アイテム登録
-----------------------------------------------------------------------*/
exports.ImageNoInsertItem = (connection, rank, itemName, itemPrice, itemRest, packId, itemType, itemOrder, itemDisplayRest, itemShipOnly) => {
  const itemInsertQuery = "INSERT INTO items (category, name, price, rest, packId, type, `order`, display_rest, is_ship_only) VALUES (?,?,?,?,?,?,?,?,?)";

  return new Promise((resolve, reject) => {
    connection.query(itemInsertQuery, [`items${rank}`, itemName, itemPrice, itemRest, packId, itemType, itemOrder, itemDisplayRest, itemShipOnly], (err, result) => {
      if (err) {
        logErr(`画像がない新規アイテムの登録に失敗しました。エラー内容: ${err} - model_adminPack_ImageNoInsertItem`);
        reject(err);
      } else {
        console.log("画像がない新規アイテムの登録が終わりましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
C賞のアイテムがそのパックに存在するかを確認
-----------------------------------------------------------------------*/
exports.cItemCheck = (connection, itemId, packId, category) => {
  const itemCheckQuery = "SELECT id FROM items WHERE id = ? AND packId = ? AND category = ?";

  return new Promise((resolve, reject) => {
    connection.query(itemCheckQuery, [itemId, packId, category], (err, result) => {
      if (err) {
        logErr(`C賞のアイテムがそのパックに存在するかを確認できませんでした。エラー内容: ${err} - model_adminPack_cItemCheck`);
        reject(err);
      } else {
        console.log("C賞のアイテムがそのパックに存在するかを確認しましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
C賞のアイテムが存在して、画像がある場合のアイテムのアップデート
-----------------------------------------------------------------------*/
exports.cItemImageYesUpdate = (connection, itemName, itemPrice, imagePath, itemRest, itemExchange, itemDisplayRest, itemId) => {
  const itemUpdateQuery = "UPDATE items SET name = ?, price = ?, image = ?, rest = ?, is_exchange_only = ?, display_rest = ? WHERE id = ?";

  return new Promise((resolve, reject) => {
    connection.query(itemUpdateQuery, [itemName, itemPrice, imagePath, itemRest, itemExchange, itemDisplayRest, itemId], (err, result) => {
      if (err) {
        logErr(`C賞の画像があるアイテムの更新に失敗しました。エラー内容: ${err} - model_adminPack_cItemImageYesUpdate`);
        reject(err);
      } else {
        console.log("C賞の画像があるアイテムの更新が終わりましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
C賞のアイテムが存在して、画像がない場合のアイテムのアップデート
-----------------------------------------------------------------------*/
exports.cItemImageNoUpdate = (connection, itemName, itemPrice, itemRest, itemExchange, itemDisplayRest, itemId) => {
  const itemUpdateQuery = "UPDATE items SET name = ?, price = ?, rest = ?, is_exchange_only = ?, display_rest = ? WHERE id = ?";

  return new Promise((resolve, reject) => {
    connection.query(itemUpdateQuery, [itemName, itemPrice, itemRest, itemExchange, itemDisplayRest, itemId], (err, result) => {
      if (err) {
        logErr(`C賞の画像がないアイテムの更新に失敗しました。エラー内容: ${err} - model_adminPack_cItemImageNoUpdate`);
        reject(err);
      } else {
        console.log("C賞の画像がないアイテムの更新が終わりましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
新規のC賞のアイテムが存在して、画像がある場合の新規アイテム登録
-----------------------------------------------------------------------*/
exports.cItemImageYesInsert = (connection, itemName, itemPrice, imagePath, itemRest, packId, itemExchange, itemDisplayRest) => {
  const itemInsertQuery = "INSERT INTO items (category, name, price, image, rest, packId, is_exchange_only, display_rest) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

  return new Promise((resolve, reject) => {
    connection.query(itemInsertQuery, ["C", itemName, itemPrice, imagePath, itemRest, packId, itemExchange, itemDisplayRest], (err, result) => {
      if (err) {
        logErr(`C賞の画像がある新規アイテムの登録に失敗しました。エラー内容: ${err} - model_adminPack_cItemImageYesInsert`);
        reject(err);
      } else {
        console.log("C賞の画像がある新規アイテムの登録が終わりましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
新規のC賞のアイテムが存在して、画像がない場合の新規アイテム登録
-----------------------------------------------------------------------*/
exports.cItemImageNoInsert = (connection, itemName, itemPrice, itemRest, packId, itemExchange, itemDisplayRest) => {
  const itemInsertQuery = "INSERT INTO items (category, name, price, rest, packId, is_exchange_only, display_rest) VALUES (?, ?, ?, ?, ?, ?, ?)";

  return new Promise((resolve, reject) => {
    connection.query(itemInsertQuery, ["C", itemName, itemPrice, itemRest, packId, itemExchange, itemDisplayRest], (err, result) => {
      if (err) {
        logErr(`C賞の画像がない新規アイテムの登録に失敗しました。エラー内容: ${err} - model_adminPack_cItemImageNoInsert`);
        reject(err);
      } else {
        console.log("C賞の画像なし新規アイテムの登録が終わりましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
ラストワン賞のアイテムがそのパックに存在するかを確認
-----------------------------------------------------------------------*/
exports.lastOneItemCheck = (connection, packId) => {
  const selectQuery = "SELECT id FROM items WHERE category = ? AND packId = ?";

  return new Promise((resolve, reject) => {
    connection.query(selectQuery, ["last_one", packId], (err, result) => {
      if (err) {
        logErr(`ラストワン賞のアイテムがそのパックに存在するかを確認できませんでした。エラー内容: ${err} - model_adminPack_lastOneItemCheck`);
        reject(err);
      } else {
        console.log("ラストワン賞のアイテムがそのパックに存在するかを確認しましたよ。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
ラストワン賞のアイテムが存在して、画像がある場合のアイテムのアップデート
-----------------------------------------------------------------------*/
exports.lastOneItemImageYesUpdate = (connection, lastOneItemName, lastOneItemPrice, lastOneItemThumbnailFilePath, lastOneItemType, packId) => {
  const updateQuery = "UPDATE items SET name = ?, price = ?, image = ?, type = ? WHERE category = ? AND packId = ?";

  return new Promise((resolve, reject) => {
    connection.query(updateQuery, [lastOneItemName, lastOneItemPrice, lastOneItemThumbnailFilePath, lastOneItemType, "last_one", packId], (err, result) => {
      if (err) {
        logErr(`ラストワン賞のアイテムの更新に失敗しました。エラー内容: ${err} - model_adminPack_lastOneItemImageYesUpdate`);
        reject(err);
      } else {
        console.log("ラストワンアイテムの更新が完了しました。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
ラストワン賞のアイテムが存在して、画像がない場合のアイテムのアップデート
-----------------------------------------------------------------------*/
exports.lastOneItemImageNoUpdate = (connection, lastOneItemName, lastOneItemPrice, lastOneItemType, packId) => {
  const updateQuery = "UPDATE items SET name = ?, price = ?, type = ? WHERE category = ? AND packId = ?";

  return new Promise((resolve, reject) => {
    connection.query(updateQuery, [lastOneItemName, lastOneItemPrice, lastOneItemType, "last_one", packId], (err, result) => {
      if (err) {
        logErr(`ラストワン賞のアイテムの更新（画像なし）に失敗しました。エラー内容: ${err} - model_adminPack_lastOneItemImageNoUpdate`);
        reject(err);
      } else {
        console.log("ラストワンアイテムの更新（画像なし）が完了しました。");
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
ラストワン賞のアイテムの新規アイテム登録
-----------------------------------------------------------------------*/
exports.insertLastOneItem = (connection, lastOneItemName, lastOneItemPrice, lastOneItemThumbnailFilePath, lastOneItemType, packId) => {
  const insertQuery = "INSERT INTO items (category, name, price, image, rest, packId, type, display_rest) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

  return new Promise((resolve, reject) => {
    connection.query(insertQuery, ["last_one", lastOneItemName, lastOneItemPrice, lastOneItemThumbnailFilePath, 1, packId, lastOneItemType, 1], (err, result) => {
      if (err) {
        logErr(`ラストワン賞のアイテムの新規アイテム登録に失敗しました。エラー内容: ${err} - model_adminPack_insertLastOneItem`);
        reject(err);
      } else {
        console.log("ラストワンアイテムの挿入が完了しました。");
        resolve(result.insertId); // 挿入されたアイテムのIDを返す
      }
    });
  });
};

/* ---------------------------------------------------------------------
キリ番賞のアイテムがそのパックに存在するかを確認
-----------------------------------------------------------------------*/
exports.checkRoundNumItem = (connection, itemId, packId, category) => {
  const checkQuery = "SELECT id FROM items WHERE id = ? AND packId = ? AND category = ?";

  return new Promise((resolve, reject) => {
    connection.query(checkQuery, [itemId, packId, category], (err, result) => {
      if (err) {
        logErr(`キリ番賞のアイテムがそのパックに存在するかを確認できませんでした。エラー内容: ${err} - model_adminPack_checkRoundNumItem`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
キリ番賞のアイテムが存在して、画像がある場合のアイテムのアップデート
-----------------------------------------------------------------------*/
exports.roundNumItemYesImageUpdate = (connection, itemName, itemPrice, roundNumFilePath, itemRest, itemRoundNum, itemType, itemId, packId) => {
  const updateQuery = "UPDATE items SET name = ?, price = ?, image = ?, rest = ?, round_num = ?, type = ?, display_rest = ? WHERE id = ? AND packId = ?";

  return new Promise((resolve, reject) => {
    connection.query(updateQuery, [itemName, itemPrice, roundNumFilePath, itemRest, itemRoundNum, itemType, 1, itemId, packId], (err, result) => {
      if (err) {
        logErr(`キリ番賞のアイテムの更新（画像あり）に失敗しました。エラー内容: ${err} - model_adminPack_roundNumItemYesImageUpdate`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
キリ番賞のアイテムが存在して、画像がない場合のアイテムのアップデート
-----------------------------------------------------------------------*/
exports.roundNumItemNoImageUpdate = (connection, itemName, itemPrice, itemRest, itemRoundNum, itemType, itemId, packId) => {
  const updateQuery = "UPDATE items SET name = ?, price = ?, rest = ?, round_num = ?, type = ?, display_rest = ? WHERE id = ? AND packId = ?";

  return new Promise((resolve, reject) => {
    connection.query(updateQuery, [itemName, itemPrice, itemRest, itemRoundNum, itemType, 1, itemId, packId], (err, result) => {
      if (err) {
        logErr(`キリ番賞のアイテムの更新（画像なし）に失敗しました。エラー内容: ${err} - model_adminPack_roundNumItemNoImageUpdate`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
キリ番賞の新規アイテムが存在して、画像がある場合の新規アイテム登録
-----------------------------------------------------------------------*/
exports.roundNumItemYesImageInsert = (connection, itemName, itemPrice, roundNumFilePath, itemRest, packId, itemRoundNum, itemType) => {
  const insertQuery = "INSERT INTO items (category, name, price, image, rest, packId, round_num, type, display_rest) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";

  return new Promise((resolve, reject) => {
    connection.query(insertQuery, ["roundNum", itemName, itemPrice, roundNumFilePath, itemRest, packId, itemRoundNum, itemType, 1], (err, result) => {
      if (err) {
        logErr(`キリ番賞の新規アイテムの登録（画像あり）に失敗しました。エラー内容: ${err} - model_adminPack_roundNumItemYesImageInsert`);
        reject(err);
      } else {
        resolve(result.insertId);
      }
    });
  });
};

/* ---------------------------------------------------------------------
キリ番賞の新規アイテムが存在して、画像がない場合の新規アイテム登録
-----------------------------------------------------------------------*/
exports.roundNumItemNoImageInsert = (connection, itemName, itemPrice, itemRest, packId, itemRoundNum, itemType) => {
  const insertQuery = "INSERT INTO items (category, name, price, rest, packId, round_num, type, display_rest) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";

  return new Promise((resolve, reject) => {
    connection.query(insertQuery, ["roundNum", itemName, itemPrice, itemRest, packId, itemRoundNum, itemType, 1], (err, result) => {
      if (err) {
        logErr(`キリ番賞の新規アイテムの登録（画像なし）に失敗しました。エラー内容: ${err} - model_adminPack_roundNumItemNoImageInsert`);
        reject(err);
      } else {
        console.log("キリ番賞の新規アイテムの登録が完了しました。");
        resolve(result.insertId);
      }
    });
  });
};

/* ---------------------------------------------------------------------
更新で削除されたアイテムを削除
-----------------------------------------------------------------------*/
exports.deleteItemsByIds = (connection, itemsToDelete) => {
  const deleteQuery = "DELETE FROM items WHERE id IN (?)";

  return new Promise((resolve, reject) => {
    connection.query(deleteQuery, [itemsToDelete], (err, result) => {
      if (err) {
        logErr(`削除されたアイテムの削除に失敗しました。エラー内容: ${err} - model_adminPack_deleteItemsByIds`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
既存のビデオIDを取得
-----------------------------------------------------------------------*/
exports.getPackDrawVideosByPackId = (connection, packId) => {
  const selectQuery = "SELECT video_id, id FROM pack_draw_videos WHERE pack_id = ?";

  return new Promise((resolve, reject) => {
    connection.query(selectQuery, [packId], (err, results) => {
      if (err) {
        logErr(`既存のビデオIDの取得に失敗しました。エラー内容: ${err} - model_adminPack_getPackDrawVideosByPackId`);
        reject(err);
      } else {
        // 既存のビデオIDとDB内のIDをオブジェクトの配列として返す
        resolve(results.map((row) => ({ video_id: row.video_id, db_id: row.id })));
      }
    });
  });
};

/* ---------------------------------------------------------------------
不要なビデオを削除
-----------------------------------------------------------------------*/
exports.deletePackDrawVideosByIds = (connection, videoIdsToDelete, packId) => {
  const deleteQuery = "DELETE FROM pack_draw_videos WHERE id IN (?) AND pack_id = ?";

  return new Promise((resolve, reject) => {
    connection.query(deleteQuery, [videoIdsToDelete, packId], (err, result) => {
      if (err) {
        logErr(`ビデオの削除ができませんでした。エラー内容: ${err} - model_adminPack_deletePackDrawVideosByIds`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
pack_videoテーブルからビデオ情報を取得
-----------------------------------------------------------------------*/
exports.getPackVideoById = (connection, id) => {
  const query = "SELECT * FROM pack_video WHERE id = ?";
  return new Promise((resolve, reject) => {
    connection.query(query, [id], (err, results) => {
      if (err) {
        logErr(`ビデオ情報の取得に失敗しました。エラー内容: ${err} - model_adminPack_getPackVideoById`);
        reject(err);
      } else {
        resolve(results);
      }
    });
  });
};

// pack_draw_videosテーブルでビデオの存在確認
exports.checkPackDrawVideoExists = (connection, videoId, packId) => {
  const query = "SELECT * FROM pack_draw_videos WHERE video_id = ? AND pack_id = ?";
  return new Promise((resolve, reject) => {
    connection.query(query, [videoId, packId], (err, result) => {
      if (err) {
        logErr(`ビデオ情報の確認に失敗しました。エラー内容: ${err} - model_adminPack_checkPackDrawVideoExists`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
ビデオ情報の更新
-----------------------------------------------------------------------*/
exports.updatePackDrawVideo = (connection, videoPath, rank, videoId, packId) => {
  const query = "UPDATE pack_draw_videos SET video_path = ?, `rank` = ? WHERE video_id = ? AND pack_id = ?";
  return new Promise((resolve, reject) => {
    connection.query(query, [videoPath, rank, videoId, packId], (err, result) => {
      if (err) {
        logErr(`ビデオ情報の更新に失敗しました。エラー内容: ${err} - model_adminPack_updatePackDrawVideo`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
ビデオ情報の新規挿入
-----------------------------------------------------------------------*/
exports.insertPackDrawVideo = (connection, videoPath, packId, rank, videoId) => {
  const query = "INSERT INTO pack_draw_videos (video_path, pack_id, `rank`, video_id) VALUES (?, ?, ?, ?)";
  return new Promise((resolve, reject) => {
    connection.query(query, [videoPath, packId, rank, videoId], (err, result) => {
      if (err) {
        logErr(`ビデオ情報の挿入に失敗しました。エラー内容: ${err} - model_adminPack_insertPackDrawVideo`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
該当IDのpack_infoの表示状態をinvalidにする
-----------------------------------------------------------------------*/
exports.deletePackInfo = (packId) => {
  const query = "UPDATE pack_info SET admin_display = ? WHERE id = ?";
  return new Promise((resolve, reject) =>
    db.query(query, ["invalid", packId], (err, result) => {
      if (err) {
        logErr(`パック情報の削除に失敗しました。エラー内容: ${err} - model_adminPack_deletePackInfo`);
        reject(err);
        console.log(err);
      } else {
        resolve(result);
      }
    })
  );
};

/* ---------------------------------------------------------------------
該当アイテムのsort_numberを更新
-----------------------------------------------------------------------*/
exports.updateSortNumber = (sortNumber, itemId) => {
  const query = "UPDATE pack_info SET sort_number = ? WHERE id = ?";
  return new Promise((resolve, reject) => {
    db.query(query, [sortNumber, itemId], (err, result) => {
      if (err) {
        logErr(`sort_numberの更新に失敗しました。エラー内容: ${err} - model_adminPack_updateSortNumber`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* ---------------------------------------------------------------------
ソートナンバーを取得する
-----------------------------------------------------------------------*/
exports.getPackSortNumber = (connection) => {
  // pack_infoテーブルのstatusがvalidityのものからsort_numberの一番低い数値を取得するクエリ
  const getMinSortNumberQuery = "SELECT MIN(sort_number) as min_sort_number FROM pack_info";

  return new Promise((resolve, reject) => {
    connection.query(getMinSortNumberQuery, (err, results) => {
      if (err) {
        logErr(`sort_numberの取得ができませんでした。エラー内容:${err} - pack-status-change`);
        return reject(err);
      }
      resolve(results[0].min_sort_number);
    });
  });
};

/* ---------------------------------------------------------------------
引数のテキストから、itemsテーブルのnameカラムと一致するかを検索し、登録されているアイテムを取得
-----------------------------------------------------------------------*/
exports.getAddedItems = (text) => {
  const searchQuery = `
    SELECT id, name, price, type, image, MATCH(name) AGAINST (? IN BOOLEAN MODE) AS score
    FROM items
    WHERE MATCH(name) AGAINST (? IN BOOLEAN MODE)
    ORDER BY score DESC
    LIMIT 20;
  `;

  return new Promise((resolve, reject) => {
    db.query(searchQuery, [`*${text}*`, `*${text}*`], (err, results) => {
      if (err) {
        logErr(`アイテムの検索に失敗しました。エラー内容: ${err} - model_adminPack_searchItems`);
        reject(err);
      } else {
        resolve(results);
      }
    });
  });
};
