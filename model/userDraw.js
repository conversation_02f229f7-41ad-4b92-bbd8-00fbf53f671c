const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
課金アイテム一覧を取得
-----------------------------------------------------------------------*/
exports.getDrawResult = async (resultId) => {
  // user_itemsテーブルからresult_idが一致するレコードを取得するクエリ
  const query = `SELECT * FROM user_items WHERE result_id = ?`;

  return new Promise((resolve, reject) => {
    db.query(query, resultId, (err, result) => {
      if (err) {
        logErr(`ガチャ結果取得エラー: ${err} model_userDraw_getDrawResult`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
リザルトID（乱数）が重複していないか確認
-----------------------------------------------------------------------*/
exports.searchDrawResultId = async (connection, drawNumber) => {
  // user_itemsテーブルからresult_idが一致するレコードを取得するクエリ
  const query = `SELECT * FROM user_items WHERE result_id = ?`;

  return new Promise((resolve, reject) => {
    connection.query(query, [drawNumber], (err, results) => {
      if (err) {
        logErr(`ガチャ結果の取得ができませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results[0]);
    });
  });
};

/* --------------------------------------------------------------------- 
クーポンが存在するか確認
-----------------------------------------------------------------------*/
exports.coupon = async (connection, couponId, userId) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT * FROM userCoupons WHERE id = ? AND userId = ? AND status = 'active'", [couponId, userId], (err, results) => {
      if (err) {
        logErr(`クーポンの検証ができませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results[0]);
    });
  });
};

/* --------------------------------------------------------------------- 
クーポンを使用済みにする
-----------------------------------------------------------------------*/
exports.useCoupon = async (connection, couponId, userId) => {
  return new Promise((resolve, reject) => {
    connection.query("UPDATE userCoupons SET status = 'expired' WHERE id = ?", [couponId], (err, results) => {
      if (err) {
        logErr(`クーポンの使用ができませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      logUserPut(`クーポンの使用ができました。ユーザー[${userId}]`);
      resolve(results);
    });
  });
};

/* --------------------------------------------------------------------- 
ユーザーの情報を取得
-----------------------------------------------------------------------*/
exports.getUser = async (connection, userId) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT * FROM user WHERE id = ?", [userId], (err, results) => {
      if (err) {
        logErr(`ユーザーの取得ができませんでした。ユーザーID[${userId}]エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results[0]);
    });
  });
};

/* --------------------------------------------------------------------- 
パック情報の取得
-----------------------------------------------------------------------*/
exports.getPackInfo = async (connection, packInfoId, forUpdate = false) => {
  return new Promise((resolve, reject) => {
    // forUpdate が true の場合は SELECT ... FOR UPDATE にする
    const sql = forUpdate ? "SELECT * FROM pack_info WHERE id = ? FOR UPDATE" : "SELECT * FROM pack_info WHERE id = ?";

    connection.query(sql, [packInfoId], (err, results) => {
      if (err) {
        logErr(`パックの情報取得ができませんでした。パックID[${packInfoId}], forUpdate=${forUpdate}. エラー: ${err}`);
        return reject(err);
      }

      if (!results || results.length === 0) {
        return reject(new Error("No matching pack information found."));
      }

      resolve(results[0]);
    });
  });
};

/* --------------------------------------------------------------------- 
ガチャがその日に引かれたか確認
-----------------------------------------------------------------------*/
exports.checkDrawHistory = async (connection, userId, packInfoId, today) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT * FROM pack_draw_history WHERE user_id = ? AND pack_info_id = ? AND draw_time >= ?", [userId, packInfoId, today], (err, results) => {
      if (err) {
        logErr(`1日1回ガチャの取得ができませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results);
    });
  });
};

/* --------------------------------------------------------------------- 
ユーザーが該当ガチャを何回回したかを確認
-----------------------------------------------------------------------*/
exports.checkDrawCount = async (connection, userId, packInfoId) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT * FROM pack_draw_history WHERE user_id = ? AND pack_info_id = ? AND round_num IS NULL", [userId, packInfoId], (err, results) => {
      if (err) {
        logErr(`ユーザー[${userId}]がガチャ[${packInfoId}]を回した回数が取得できませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results);
    });
  });
};

/* --------------------------------------------------------------------- 
残数が0じゃない場合に、該当ガチャのアイテムを取得
-----------------------------------------------------------------------*/
exports.getPackItems = async (connection, packInfoId, forUpdate = false) => {
  return new Promise((resolve, reject) => {
    // 必要に応じて「AND rest > 0」を付けるかどうか調整してください
    // （0以下の在庫でもロックしたいケースがあれば外す）
    const baseSql = "SELECT * FROM items WHERE packId = ? AND rest > 0";
    const sql = forUpdate ? `${baseSql} FOR UPDATE` : baseSql;

    connection.query(sql, [packInfoId], (err, results) => {
      if (err) {
        logErr(`パックID[${packInfoId}], forUpdate=${forUpdate} のアイテム取得失敗: ${err}`);
        return reject(err);
      }
      resolve(results);
    });
  });
};

/* --------------------------------------------------------------------- 
ラストワンアイテムをユーザーに付与
-----------------------------------------------------------------------*/
exports.giveLastOneItem = async (connection, userId, lastOneItemId, lastOneItemCategory, lastOneItemName, lastOneItemPrice, imageUrl, drawNumber) => {
  return new Promise((resolve, reject) => {
    connection.query("INSERT INTO user_items (user_id, item_id, category, name, price, image, quantity, result_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", [userId, lastOneItemId, lastOneItemCategory, lastOneItemName, lastOneItemPrice, imageUrl, 1, drawNumber], (err, results) => {
      if (err) {
        logErr(`ユーザー[${userId}]にラストワンアイテム[${lastOneItemId}]の挿入ができませんでした。 - draw`);
        return reject(err);
      }
      const userItemId = results.insertId;
      resolve({
        userItemId: userItemId,
        id: lastOneItemId,
        category: lastOneItemCategory,
        name: lastOneItemName,
        price: lastOneItemPrice,
        image: imageUrl,
      });
    });
  });
};

/* --------------------------------------------------------------------- 
アイテムの残数を取得
-----------------------------------------------------------------------*/
exports.getItemRest = async (connection, itemId) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT rest FROM items WHERE id = ?", [itemId], (err, results) => {
      if (err) {
        logErr(`アイテム[${itemId}]の残数が取得できませんでした。 - draw`);
        return reject(err);
      }
      resolve(results[0].rest);
    });
  });
};

/* --------------------------------------------------------------------- 
キリ番アイテムをユーザーに付与
-----------------------------------------------------------------------*/
exports.giveRoundNumItem = async (connection, userId, roundNumItemId, roundNumItemCategory, roundNumItemName, roundNumItemPrice, imageUrl, drawNumber) => {
  return new Promise((resolve, reject) => {
    connection.query("INSERT INTO user_items (user_id, item_id, category, name, price, image, quantity, result_id) VALUES (?, ?, ?, ?, ?, ?, ?, ?)", [userId, roundNumItemId, roundNumItemCategory, roundNumItemName, roundNumItemPrice, imageUrl, 1, drawNumber], (err, results) => {
      if (err) {
        logErr(`ユーザー[${userId}]にキリ番アイテム[${roundNumItemId}]の挿入ができませんでした。 - draw`);
        return reject(err);
      }
      const userItemId = results.insertId; // 挿入されたアイテムのIDを取得
      resolve({
        userItemId: userItemId,
        id: roundNumItemId,
        category: roundNumItemCategory,
        name: roundNumItemName,
        price: roundNumItemPrice,
        image: imageUrl,
      });
    });
  });
};

/* --------------------------------------------------------------------- 
該当アイテムの残数を減らす
-----------------------------------------------------------------------*/
exports.decreaseItemRest = async (connection, itemId) => {
  return new Promise((resolve, reject) => {
    const sql = `
      UPDATE items
      SET rest = rest - 1
      WHERE id = ?
        AND rest > 0
    `;
    connection.query(sql, [itemId], (err, results) => {
      if (err) {
        logErr(`アイテム[${itemId}]の残数を減らせませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results);
      // results.affectedRows === 1 → 在庫を1減らせた
      // results.affectedRows === 0 → もともと rest が 0 だった
    });
  });
};

/* --------------------------------------------------------------------- 
ユーザーにアイテムを挿入
-----------------------------------------------------------------------*/
exports.giveItem = async (connection, userId, selectedItemId, selectedItemCategory, selectedItemName, selectedItemPrice, imageUrl, selectedItemIsExChangeOnly, drawNumber, selectedItemRest, selectedItemIsShipOnly) => {
  return new Promise((resolve, reject) => {
    connection.query("INSERT INTO user_items (user_id, item_id, category, name, price, image, quantity, is_exchange_only, expiration_at, result_id, is_ship_only) VALUES (?, ?, ?, ?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 10 DAY), ?, ?)", [userId, selectedItemId, selectedItemCategory, selectedItemName, selectedItemPrice, imageUrl, 1, selectedItemIsExChangeOnly, drawNumber, selectedItemIsShipOnly], (err, results) => {
      if (err) {
        logErr(`ユーザー[${userId}]にアイテム[${selectedItemId}]を付与できませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }

      // 挿入されたアイテムのIDを取得
      const userItemId = results.insertId;

      resolve({
        userItemId: userItemId,
        id: selectedItemId,
        category: selectedItemCategory,
        name: selectedItemName,
        price: selectedItemPrice,
        image: imageUrl,
        rest: selectedItemRest,
      });
    });
  });
};

/* --------------------------------------------------------------------- 
パックの残数を減らす
-----------------------------------------------------------------------*/
exports.decreasePackRest = async (connection, packInfoId, quantity) => {
  return new Promise((resolve, reject) => {
    const sql = `
      UPDATE pack_info
      SET rest = rest - ?
      WHERE id = ?
        AND rest >= ?
    `;
    connection.query(sql, [quantity, packInfoId, quantity], (err, results) => {
      if (err) {
        logErr(`パック[${packInfoId}]の在庫更新エラー: ${err} - draw`);
        return reject(err);
      }
      resolve(results);
      // results.affectedRows === 1 → 更新成功
      // results.affectedRows === 0 → 在庫不足
    });
  });
};

/* --------------------------------------------------------------------- 
ガチャの残数を取得
-----------------------------------------------------------------------*/
exports.getPackRest = async (connection, packInfoId) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT rest FROM pack_info WHERE id = ?", [packInfoId], (err, results) => {
      if (err) {
        logErr(`ガチャ[${packInfoId}]の残数が取得されませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results[0]);
    });
  });
};

/* --------------------------------------------------------------------- 
ガチャのステータスを売り切れに変更
-----------------------------------------------------------------------*/
exports.updatePackStatus = async (connection, packInfoId) => {
  return new Promise((resolve, reject) => {
    connection.query("UPDATE pack_info SET status = 'sold-out', sold_out_at = NOW() WHERE id = ?", [packInfoId], (err, results) => {
      if (err) {
        logErr(`ガチャ[${packInfoId}]の残数が0になったのにsold-outに変更されませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results);
    });
  });
};

/* --------------------------------------------------------------------- 
ガチャの履歴を保存
-----------------------------------------------------------------------*/
exports.saveDrawHistory = async (connection, recordPackInfoId, recordUserId, recordItemId, round_num) => {
  return new Promise((resolve, reject) => {
    connection.query("INSERT INTO pack_draw_history (pack_info_id, user_id, item_id, draw_time, round_num) VALUES (?, ?, ?, ?, ?)", [recordPackInfoId, recordUserId, recordItemId, new Date(), round_num > 0 ? round_num : null], (err, results) => {
      if (err) {
        logErr(`引いたガチャ[${recordPackInfoId}]の排出履歴が保存されませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results);
    });
  });
};

/* --------------------------------------------------------------------- 
ユーザーのポイントを減らす
-----------------------------------------------------------------------*/
exports.decreaseUserPoint = async (connection, packInfoId, totalDiscountedPoints, userId) => {
  return new Promise((resolve, reject) => {
    const sql = `
      UPDATE user
      SET point = point - ?
      WHERE id = ?
        AND point >= ?
    `;
    connection.query(sql, [totalDiscountedPoints, userId, totalDiscountedPoints], (err, results) => {
      if (err) {
        logErr(`ユーザー[${userId}]が引いたガチャ[${packInfoId}]のポイント減算失敗:${err} - draw`);
        return reject(err);
      }
      resolve(results);
      // results.affectedRows === 1 → ポイント減算成功
      // 0 → ポイント不足
    });
  });
};

/* --------------------------------------------------------------------- 
ユーザーの使用済ポイントを更新
-----------------------------------------------------------------------*/
exports.updateUserUsedPoint = async (connection, packInfoId, totalDiscountedPoints, userId) => {
  return new Promise((resolve, reject) => {
    connection.query("UPDATE user SET use_point = use_point + ? WHERE id = ?", [totalDiscountedPoints, userId], (err, results) => {
      if (err) {
        logErr(`ユーザー[${userId}]が引いたガチャ[${packInfoId}]のポイントがユーザーのuse_pointカラムに追加されませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results);
    });
  });
};