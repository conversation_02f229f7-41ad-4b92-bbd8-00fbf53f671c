const { logUserPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
該当のクーポンが存在するか確認
-----------------------------------------------------------------------*/
exports.checkBuyDiscountCoupon = async (connection, couponCode) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT * FROM buy_discount_coupon WHERE coupon_code = ? FOR UPDATE", [couponCode], (err, couponResults) => {
      if (err) {
        console.error("Error fetching buy discount coupon:", err);
        logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - userCoupons-checkBuyDiscountCoupon`);
        reject(err);
      }
      resolve(couponResults);
    });
  });
};

/* --------------------------------------------------------------------- 
既にユーザーにクーポンが取得されているかを確認
-----------------------------------------------------------------------*/
exports.userCouponCheck = async (connection, couponCode, userId) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT * FROM userCoupons WHERE buy_code = ? AND userId = ?", [couponCode, userId], (err, userCouponResults) => {
      if (err) {
        logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
        reject(err);
      }
      resolve(userCouponResults);
    });
  });
};

/* --------------------------------------------------------------------- 
該当クーポンの残数をデクリメント
-----------------------------------------------------------------------*/
exports.couponDecrement = async (connection, couponCode) => {
  return new Promise((resolve, reject) => {
    connection.query("UPDATE buy_discount_coupon SET rest = rest - 1 WHERE coupon_code = ?", [couponCode], (err) => {
      if (err) {
        console.error("Error decrementing buy discount coupon rest:", err);
        logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
        reject(err);
      }
      resolve();
    });
  });
};

/* --------------------------------------------------------------------- 
課金用クーポンをユーザーに付与
-----------------------------------------------------------------------*/
exports.assignUserBuyDiscountCoupon = async (connection, userId, name, term, discount, couponCode) => {
  return new Promise((resolve, reject) => {
    connection.query("INSERT INTO userCoupons (userId, name, type, term, date, percentage, status, couponId, buy_code) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)", [userId, name, "buyItemDiscount", term, new Date(), discount, "expired", null, couponCode], (err) => {
      if (err) {
        console.error("Error assigning buy discount coupon:", err);
        logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
        reject(err);
      }
      resolve();
    });
  });
};

/* ---------------------------------------------------------------------
通常クーポンが存在するか確認
-----------------------------------------------------------------------*/
exports.checkNormalCoupon = async (connection, couponCode) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT * FROM coupons WHERE coupon_key = ? FOR UPDATE", [couponCode], (err, couponResults) => {
      if (err) {
        console.error("Error fetching coupon:", err);
        logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
        reject(err);
      }
      resolve(couponResults);
    });
  });
};

/* ---------------------------------------------------------------------
通常クーポンが既に付与されているか確認
-----------------------------------------------------------------------*/
exports.userNormalCouponCheck = async (connection, userId, couponCode) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT * FROM userCoupons WHERE userId = ? AND couponId = ?", [userId, couponCode], (err, userCouponResults) => {
      if (err) {
        logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
        reject(err);
      }
      resolve(userCouponResults);
    });
  });
};

/* ---------------------------------------------------------------------
通常クーポンの残数をデクリメント
-----------------------------------------------------------------------*/
exports.normalCouponDecrement = async (connection, couponId) => {
  return new Promise((resolve, reject) => {
    connection.query("UPDATE coupons SET rest = rest - 1 WHERE id = ?", [couponId], (err) => {
      if (err) {
        console.error("Error decrementing coupon rest:", err);
        logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
        reject(err);
      }
      resolve();
    });
  });
};

/* ---------------------------------------------------------------------
ユーザーに通常クーポンを付与
-----------------------------------------------------------------------*/
exports.assignUserNormalCoupon = async (connection, userId, name, type, term, couponPercentage, userCouponStatus, couponId) => {
  return new Promise((resolve, reject) => {
    connection.query("INSERT INTO userCoupons (userId, name, type, term, percentage, status, couponId) VALUES (?, ?, ?, ?, ?, ?, ?)", [userId, name, type, term, couponPercentage, userCouponStatus, couponId], (err) => {
      if (err) {
        console.error("Error assigning coupon:", err);
        logErr(`該当クーポンの取得ができませんでした。エラー内容:${err} - user-coupons`);
        reject(err);
      }
      resolve();
    });
  });
};