const db = require("../db/db");
const { logAdminPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
ソート番号を変更する
-----------------------------------------------------------------------*/
exports.changeSortNumber = async (id, sortNum) => {
  // infoテーブルのsort_numberカラムを更新するクエリ
  const query = "UPDATE info SET sort_number = ? WHERE id = ?";

  return new Promise((resolve, reject) => {
    db.query(query, [sortNum, id], (err, result) => {
      if (err) {
        logErr(`ソート番号を変更できませんでした。エラー内容:${err} - model_adminInfo_changeSortNumber`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};
