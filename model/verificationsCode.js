const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

/**
 * 获取所有的验证码记录
 * @param {Object} connection - 数据库连接对象
 */
exports.getVerificationCode = async (connection) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT * FROM `verification_codes`", (err, results) => {
      if (err) {
        logErr(`Failed to query verification codes: ${err.message}`);
        return reject(err);
      }
      console.log("Fetched verification codes:", results);
      resolve(results);
    });
  });
};


