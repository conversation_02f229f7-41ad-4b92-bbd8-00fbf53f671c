const db = require("../db/db");
const { logAdminPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
クーポンのコードが既に存在するかを確認
-----------------------------------------------------------------------*/
exports.checkCouponCode = async (code) => {
  // buy_discount_couponテーブルのcoupon_codeカラムと、couponsテーブルのcoupon_keyカラムとcodeが一致するレコードを取得するプリペアードステートメントを用いたクエリ
  const query = `
    SELECT 
      bdc.*
      FROM 
      buy_discount_coupon bdc
      JOIN 
      coupons c ON bdc.coupon_code = c.coupon_key
      WHERE 
      bdc.coupon_code = ?;
  `;

  return new Promise((resolve, reject) => {
    db.query(query, [code], (err, result) => {
      if (err) {
        logErr(`クーポンのコードが既に存在するかを確認できませんでした。エラー内容:${err} - model_adminCoupon_checkCouponCode`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
課金用の割引クーポンを追加
-----------------------------------------------------------------------*/
exports.addDiscountCoupon = async (code, itemId, discount, rest, name, term) => {
  // buy_discount_couponテーブルに、code,itemId,discount,restをインサートするプリペアードステートメントを用いたクエリ
  const query = "INSERT INTO buy_discount_coupon (coupon_code,item_id,discount,rest,name,term) VALUES (?,?,?,?,?,?)";

  return new Promise((resolve, reject) => {
    db.query(query, [code, itemId, discount, rest, name, term], (err, result) => {
      if (err) {
        logErr(`課金用の割引クーポンを追加できませんでした。エラー内容:${err} - model_adminCoupon_addDiscountCoupon`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
課金用の割引クーポンを取得
-----------------------------------------------------------------------*/
exports.getDiscountCoupons = async () => {
  // buy_discount_coupon と buy_items を結合して point を含めたデータを取得するクエリ
  const query = `
    SELECT 
      bdc.*, 
      bi.point 
    FROM 
      buy_discount_coupon bdc
    LEFT JOIN 
      buy_items bi ON bdc.item_id = bi.id
  `;

  return new Promise((resolve, reject) => {
    db.query(query, (err, result) => {
      if (err) {
        logErr(`課金用の割引クーポンを取得できませんでした。エラー内容:${err} - model_adminCoupon_getDiscountCoupons`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
課金用の割引クーポンを削除
-----------------------------------------------------------------------*/
exports.removeDiscountCoupon = async (code) => {
  // buy_discount_couponテーブルからcodeに一致するレコードを削除するクエリ
  const query = "DELETE FROM buy_discount_coupon WHERE coupon_code = ?";

  return new Promise((resolve, reject) => {
    db.query(query, [code], (err, result) => {
      if (err) {
        logErr(`課金用の割引クーポンを削除できませんでした。エラー内容:${err} - model_adminCoupon_removeDiscountCoupon`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};
