const Res = require("../util/responseHelpers");
const db = require("../db/db");
require("dotenv").config();
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const { logUserPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
オーダーIDを保存する
-----------------------------------------------------------------------*/
exports.paymentInfoInsert = async (userId, order_id, amount, status, type) => {
  // paymentテーブルのorder_id,user_id,amountをインサートするプリペアステートメントを用いたクエリ
  const query = `INSERT INTO payment (order_id, user_id, amount, status, type) VALUES (?, ?, ?, ?, ?)`;

  return new Promise((resolve, reject) => {
    db.query(query, [order_id, userId, amount, status, type], (err, result) => {
      if (err) {
        logErr(`${type}の決済情報を保存できませんでした。エラー内容：${err} - model_payment_paymentInfoInsert`);
        reject();
      } else {
        resolve();
      }
    });
  });
};

/* --------------------------------------------------------------------- 
オーダーIDを保存する
-----------------------------------------------------------------------*/
exports.paymentInfoInsertNoPromise = async (userId, order_id, amount, status, type) => {
  // paymentテーブルのorder_id,user_id,amountをインサートするプリペアステートメントを用いたクエリ
  const query = `INSERT INTO payment (order_id, user_id, amount, status, type) VALUES (?, ?, ?, ?, ?)`;

  db.query(query, [order_id, userId, amount, status, type], (err, result) => {
    if (err) {
      logErr(`${type}の決済情報を保存できませんでした。エラー内容：${err} - model_payment_paymentInfoInsert`);
    }
  });
};

/* --------------------------------------------------------------------- 
ポイントを付与したかどうかを確認する
-----------------------------------------------------------------------*/
exports.checkPointAdded = async (order_id) => {
  // paymentテーブルの該当のorder_idのレコードのcredit_comp_statusがaddPaymentのものを取得するクエリ
  const query = `SELECT * FROM payment WHERE order_id = ? AND credit_comp_status = 'addPayment'`;

  return new Promise((resolve, reject) => {
    db.query(query, [order_id], (err, result) => {
      if (err) {
        logErr(`ポイント付与の確認ができませんでした。エラー内容：${err} - model_payment_checkPointAdded`);
        reject();
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
ポイント付与した場合にcredit_comp_statusをaddPaymentに更新する
-----------------------------------------------------------------------*/
exports.updateCreditCompStatus = async (order_id) => {
  // paymentテーブルのcredit_comp_statusをaddPaymentに更新するクエリ
  const query = `UPDATE payment SET credit_comp_status = 'addPayment' WHERE order_id = ?`;

  return new Promise((resolve, reject) => {
    db.query(query, [order_id], (err, result) => {
      if (err) {
        logErr(`credit_comp_statusを更新できませんでした。エラー内容：${err} - model_payment_updateCreditCompStatus`);
        reject();
      } else {
        resolve();
      }
    });
  });
};
