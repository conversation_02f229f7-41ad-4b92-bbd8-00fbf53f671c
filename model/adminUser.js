// adminUser.js
const db = require("../db/db");
const { logAdminPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
ユーザー情報を検索
-----------------------------------------------------------------------*/
exports.searchUsers = async (searchTerm) => {
  // userテーブルのnameカラムとmailカラムから、searchTermが含まれるレコードのid, name, mail, point, gp, tel, address,zip_code,pref,kanaカラムの情報を取得するクエリ
  const query = `
    SELECT 
      id, 
      name, 
      mail, 
      point, 
      gp, 
      tel, 
      address,
      zip_code,
      pref,
      kana 
    FROM 
      user 
    WHERE 
      name LIKE ? OR 
      mail LIKE ?;
  `;

  const searchValue = `%${searchTerm}%`;

  return new Promise((resolve, reject) => {
    db.query(query, [searchValue, searchValue], (err, result) => {
      if (err) {
        logErr(`ユーザー情報を検索できませんでした。エラー内容:${err} - model_adminUser_searchUsers`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当IDのユーザー情報を取得
-----------------------------------------------------------------------*/
exports.getIdUser = async (id) => {
  // userテーブルから、指定したidのid, name, mail, point, gp, tel, address,zip_code,pref,kanaカラムの情報を取得するクエリ
  const query = `
    SELECT 
      id, 
      name, 
      mail, 
      point, 
      gp, 
      tel, 
      address,
      zip_code,
      pref,
      kana 
    FROM 
      user 
    WHERE 
      id = ?;
  `;

  return new Promise((resolve, reject) => {
    db.query(query, [id], (err, result) => {
      if (err) {
        logErr(`ユーザー情報を取得できませんでした。エラー内容:${err} - model_adminUser_getIdUser`);
        reject(err);
      } else {
        resolve(result[0]);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当IDのユーザー情報を更新
-----------------------------------------------------------------------*/
exports.updateUserPoint = async (id, point, gp) => {
  // userテーブルの指定したidのpointカラムとgpカラムのデータを更新するクエリ
  const query = "UPDATE user SET point = ?, gp = ? WHERE id = ?";

  return new Promise((resolve, reject) => {
    db.query(query, [point, gp, id], (err, result) => {
      if (err) {
        logErr(`ユーザー情報を更新できませんでした。エラー内容:${err} - model_adminUser_updateUserPoint`);
        reject(err);
      } else {
        logAdminPut("ユーザー情報がデータベースに正常に更新されました。");
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当IDユーザーのポイント情報を取得
-----------------------------------------------------------------------*/
exports.getUserPoint = async (id) => {
  // userテーブルから指定したidのpointカラムのデータを取得するクエリ
  const query = "SELECT point FROM user WHERE id = ?";

  return new Promise((resolve, reject) => {
    db.query(query, [id], (err, result) => {
      if (err) {
        logErr(`ユーザーのポイント情報を取得できませんでした。エラー内容:${err} - model_adminUser_getUserPoint`);
        reject(err);
      } else {
        resolve(result[0].point);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当IDユーザーのGP情報を取得
-----------------------------------------------------------------------*/
exports.getUserGp = async (id) => {
  // userテーブルから指定したidのgpカラムのデータを取得するクエリ
  const query = "SELECT gp FROM user WHERE id = ?";

  return new Promise((resolve, reject) => {
    db.query(query, [id], (err, result) => {
      if (err) {
        logErr(`ユーザーのGP情報を取得できませんでした。エラー内容:${err} - model_adminUser_getUserGp`);
        reject(err);
      } else {
        resolve(result[0].gp);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当IDのユーザー情報を非表示
-----------------------------------------------------------------------*/
exports.removeUser = async (id) => {
  // userテーブルの指定したidのdisplayカラムのデータをinvalidに更新するクエリ
  const query = "UPDATE user SET display = 'invalid' WHERE id = ?";
  return new Promise((resolve, reject) => {
    db.query(query, [id], (err, result) => {
      if (err) {
        logErr(`ユーザー情報を非表示にできませんでした。エラー内容:${err} - model_adminUser_removeUser`);
        reject(err);
      } else {
        logAdminPut("ユーザー情報が非表示にされました。");
        resolve(result);
      }
    });
  });
};
