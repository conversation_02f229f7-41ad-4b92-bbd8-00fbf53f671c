const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
課金アイテム一覧を取得
-----------------------------------------------------------------------*/
exports.getBuyItems = async () => {
  // buy_itemsテーブルから全て取得するクエリ
  const query = `SELECT * FROM buy_items`;

  return new Promise((resolve, reject) => {
    db.query(query, (err, result) => {
      if (err) {
        logErr(`課金アイテム一覧取得エラー: ${err} model_userBuyItem_getBuyItems`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当アイテムのポイントを取得
-----------------------------------------------------------------------*/
exports.getPoint = async (itemId) => {
  // buy_itemsテーブルから該当アイテムのポイントを取得するクエリ
  const query = `SELECT point FROM buy_items WHERE id = ?`;

  return new Promise((resolve, reject) => {
    db.query(query, itemId, (err, result) => {
      if (err) {
        logErr(`課金アイテムポイント取得エラー: ${err} model_userBuyItem_getPoint`);
        reject(err);
      } else {
        resolve(result[0]);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当ユーザーが取得している課金アイテムのクーポンを取得
-----------------------------------------------------------------------*/
exports.getCoupon = async (userId) => {
  // userCouponsテーブルのIDからuserIdのレコードを取得し、その中で、buy_codeカラムの値が存在する一番新しいレコードを取得するクエリ
  const query = `SELECT buy_code 
  FROM userCoupons 
  WHERE userId = ? AND buy_code IS NOT NULL 
  ORDER BY id DESC 
  LIMIT 1`;

  return new Promise((resolve, reject) => {
    db.query(query, userId, (err, result) => {
      if (err) {
        logErr(`クーポン取得エラー: ${err} model_userBuyItem_getCoupon`);
        console.log(err);
        reject(err);
      } else {
        resolve(result[0]);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当クーポンがuserCouponsテーブルでusedじゃないかチェック
-----------------------------------------------------------------------*/
exports.checkCoupon = async (couponCode, userId) => {
  // userCouponsテーブルから該当のcoupon_codeが存在するレコードのbuy_statusがusedのものを取得するクエリ
  const query = `SELECT * FROM userCoupons WHERE buy_code = ? AND buy_status = 'used' AND userId = ?`;

  return new Promise((resolve, reject) => {
    db.query(query, [couponCode, userId], (err, result) => {
      if (err) {
        logErr(`クーポンチェックエラー: ${err} model_userBuyItem_checkCoupon`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
コードを元にクーポンを取得
-----------------------------------------------------------------------*/
exports.getCouponByCode = async (couponCode) => {
  // buy_discount_couponテーブルからcoupon_codeカラムと同一のレコードを取得するクエリ
  const query = `SELECT * FROM buy_discount_coupon WHERE coupon_code = ?`;

  return new Promise((resolve, reject) => {
    db.query(query, couponCode, (err, result) => {
      if (err) {
        console.log(err);
        logErr(`クーポン取得エラー: ${err} model_userBuyItem_getCouponByCode`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当クーポンを使用済みにする
-----------------------------------------------------------------------*/
exports.useCoupon = async (couponCode, userId) => {
  // userCouponsテーブルのbuy_codeがcouponCodeのレコードのbuy_statusをusedにするクエリ
  const query = `UPDATE userCoupons SET buy_status = 'used' WHERE buy_code = ? AND userId = ?`;

  return new Promise((resolve, reject) => {
    db.query(query, [couponCode, userId], (err, result) => {
      if (err) {
        logErr(`クーポン使用済みエラー: ${err} model_userBuyItem_useCoupon`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};
