const db = require("../db/db");

/* --------------------------------------------------------------------- 
ユーザーのメールアドレスを取得する
-----------------------------------------------------------------------*/
exports.getUserMail = async (userId) => {
  // userテーブルのidカラムとuserIdが一致するレコードのmailカラムの情報を取得するクエリ
  const query = `
    SELECT 
      mail 
    FROM 
      user 
    WHERE 
      id = ?;
  `;

  return new Promise((resolve, reject) => {
    db.query(query, [userId], (err, result) => {
      if (err) {
        reject(err);
      } else if (!result || result.length === 0) {
        resolve(null); // データが存在しない場合
      } else {
        resolve(result[0].mail);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
管理者ユーザーのメールアドレスを取得する
-----------------------------------------------------------------------*/
exports.getAdminUserMail = async (userId) => {
  // userテーブルのidカラムとuserIdが一致するレコードのmailカラムの情報を取得するクエリ
  const query = `
    SELECT 
      mail 
    FROM 
      admin_accounts 
    WHERE 
      id = ?;
  `;

  return new Promise((resolve, reject) => {
    db.query(query, [userId], (err, result) => {
      if (err) {
        reject(err);
      } else {
        if (result.length === 0) {
          resolve(undefined);
        } else {
          resolve(result[0].mail);
        }
      }
    });
  });
};
