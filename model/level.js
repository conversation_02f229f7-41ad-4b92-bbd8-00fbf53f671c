const db = require('../db/db');

exports.getAll = async (connection) => {
    const queryRunner = connection || db;
    return new Promise((resolve, reject) => {
        queryRunner.query('SELECT * FROM level ORDER BY level ASC', [], (err, results) => {
            if (err) return reject(err);
            resolve(results);
        });
    });
};

exports.insert = async ({ type, level, value }) => {
    return new Promise((resolve, reject) => {
        db.query(
            'INSERT INTO level (type, level, value) VALUES (?, ?, ?)',
            [type, level, value],
            (err, result) => {
                if (err) return reject(err);
                resolve(result);
            }
        );
    });
};

exports.deleteById = async (id) => {
    return new Promise((resolve, reject) => {
        db.query('DELETE FROM level WHERE id = ?', [id], (err, result) => {
            if (err) return reject(err);
            resolve(result);
        });
    });
}; 