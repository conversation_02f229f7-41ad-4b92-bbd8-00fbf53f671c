const Res = require("../util/responseHelpers");
const db = require("../db/db");
require("dotenv").config();
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const { logUserPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
設定テーブルからサービス名を取得する
-----------------------------------------------------------------------*/
exports.getServiceName = async () => {
  // pack_settingsテーブルの1行目のservice_nameを取得するクエリ
  const settingsQuery = `
    SELECT service_name
    FROM pack_settings
    WHERE id = 1
  `;

  // クエリの実行
  return new Promise((resolve, reject) => {
    db.query(settingsQuery, (err, results) => {
      if (err) {
        logErr(`サービス名の取得に失敗しました。エラー内容: ${err}`);
        reject(err);
      }
      resolve(results);
    });
  });
};
