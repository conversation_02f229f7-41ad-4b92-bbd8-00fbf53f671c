const db = require("../db/db");
require("dotenv").config();
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const { logUserPut, logErr } = require("../logger");
const { log } = require("console");
const { zonedTimeToUtc, utcToZonedTime } = require("date-fns-tz");
const { startOfWeek, endOfWeek, subWeeks } = require("date-fns");

/* --------------------------------------------------------------------- 
  Check if an LP gacha item exists
-----------------------------------------------------------------------*/
exports.getLpItem = async (lpItemId) => {
  // lpItemIdと一致するIDを持つレコードをitems_lpテーブルから取得するクエリ
  const sql = `SELECT * FROM items_lp WHERE id = ?`;

  return new Promise((resolve, reject) => {
    db.query(sql, lpItemId, (err, result) => {
      if (err) {
        logErr(`LPガチャのアイテムを確認できませんでした。エラー内容:${err} - model_userItem_getLpItem`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
  Check if an LP gacha item has already been obtained
-----------------------------------------------------------------------*/
exports.checkLpItem = async (userId) => {
  // user_itemsテーブルのcategoryがLPで、user_idが一致するレコードを取得するクエリ
  const sql = `SELECT * FROM user_items WHERE category = 'LP' AND user_id = ?`;

  return new Promise((resolve, reject) => {
    db.query(sql, userId, (err, result) => {
      if (err) {
        logErr(`LPガチャのアイテムがすでに該当ユーザーに付与されているか、確認できませんでした。エラー内容:${err} - model_userItem_checkLpItem`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
  Add an LP gacha item to the corresponding user
-----------------------------------------------------------------------*/
exports.addLpItem = async (userId, lpItemId, lpItemName, lpItemPrice, image) => {
  // ユーザーIDとアイテムIDをitems_userテーブルに追加するクエリ
  const sql = `INSERT INTO user_items (user_id, item_id, category, name, price, image, quantity, is_exchange_only, expiration_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, DATE_ADD(NOW(), INTERVAL 365 DAY))`;

  return new Promise((resolve, reject) => {
    db.query(sql, [userId, lpItemId, "LP", lpItemName, lpItemPrice, image, 1, 0], (err, result) => {
      if (err) {
        logErr(`LPガチャのアイテムをユーザー[${userId}]に付与できませんでした。エラー内容:${err} - model_userItem_addLpItem`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
  Get items for the corresponding user
-----------------------------------------------------------------------*/
exports.getUserItems = async (userId) => {
  // user_itemsテーブルのuser_idが一致していて、quantityが1以上のレコードを取得するクエリ
  const sql = `SELECT * FROM user_items WHERE user_id = ? AND quantity > 0`;

  return new Promise((resolve, reject) => {
    db.query(sql, userId, (err, result) => {
      if (err) {
        logErr(`ユーザー[${userId}]のアイテムを取得できませんでした。エラー内容:${err} - model_userItem_getUserItems`);
        reject(err);
        console.log(`ユーザー[${userId}]のアイテムを取得できませんでした。エラー内容:${err} - model_userItem_getUserItems`);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
  Decrement item quantity and add points to the user
-----------------------------------------------------------------------*/
exports.exchangeItemToPoint = async (itemId, userId) => {
  return new Promise((resolve, reject) => {
    db.getConnection((err, connection) => {
      if (err) {
        logErr(`データベース接続エラー: ${err} - model_userItem_exchangeItemToPoint`);
        return reject(err);
      }

      connection.beginTransaction(async (transactionErr) => {
        if (transactionErr) {
          logErr(`トランザクション開始エラー: ${transactionErr} - model_userItem_exchangeItemToPoint`);
          connection.release();
          return reject(transactionErr);
        }

        try {
          // 1. user_itemsのquantityをデクリメント
          await new Promise((resolveQuery, rejectQuery) => {
            connection.query(`UPDATE user_items SET quantity = quantity - 1 WHERE id = ?`, [itemId], (err, results) => {
              if (err) return rejectQuery(err);
              resolveQuery(results);
            });
          });

          // 2. userテーブルのポイントを更新
          await new Promise((resolveQuery, rejectQuery) => {
            connection.query(`UPDATE user SET point = point + (SELECT price FROM user_items WHERE id = ?) WHERE id = ?`, [itemId, userId], (err, results) => {
              if (err) return rejectQuery(err);
              resolveQuery(results);
            });
          });

          // コミット
          connection.commit((commitErr) => {
            if (commitErr) {
              connection.rollback(() => {
                logErr(`トランザクションコミットエラー: ${commitErr} - model_userItem_exchangeItemToPoint`);
                connection.release();
                return reject(commitErr);
              });
            }
            connection.release();
            resolve({ message: "ポイント交換が成功しました。" });
          });
        } catch (queryErr) {
          connection.rollback(() => {
            logErr(`トランザクション内クエリエラー: ${queryErr} - model_userItem_exchangeItemToPoint`);
            connection.release();
            reject(queryErr);
          });
        }
      });
    });
  });
};

/* --------------------------------------------------------------------- 
  Get user items (pagination support)
-----------------------------------------------------------------------*/
exports.getPagedUserItems = async (userId, status, pageSize, offset) => {
  // ベースとなるクエリとパラメータを定義
  let baseQuery = `FROM user_items ui LEFT JOIN user u ON ui.user_id = u.id WHERE ui.user_id = ?`;
  const params = [userId];

  // ステータスに応じたフィルタを適用
  if (status === "pending") {
    baseQuery += ` AND ui.quantity > 0`;
  }
  if (status && status !== "all") {
    const subQuery = `(SELECT ss.status FROM shipping_statuses ss WHERE ss.user_item_id = ui.id ORDER BY ss.updated_at DESC LIMIT 1)`;
    if (status === "pending") {
      baseQuery += ` AND ${subQuery} IS NULL`;
    } else {
      baseQuery += ` AND ${subQuery} = ?`;
      params.push(status); // SQLインジェクションを防ぐためにパラメータ化
    }
  }

  // 総数取得クエリとデータ取得クエリを構築
  const countQuery = `SELECT COUNT(*) as count ${baseQuery}`;
  const dataQuery = `SELECT ui.*, (SELECT ss.status FROM shipping_statuses ss WHERE ss.user_item_id = ui.id ORDER BY ss.updated_at DESC LIMIT 1) AS shipping_status, u.address AS user_address ${baseQuery} ORDER BY ui.id DESC LIMIT ? OFFSET ?`;

  const dataParams = [...params, pageSize, offset];

  return new Promise((resolve, reject) => {
    // 最初に総数を取得
    db.query(countQuery, params, (err, countResult) => {
      if (err) {
        logErr(`ユーザー[${userId}]のアイテム総数が取得できませんでした。エラー内容:${err} - model_userItem_getPagedUserItems`);
        return reject(err);
      }

      const totalItems = countResult[0].count;
      if (totalItems === 0) {
        return resolve({ userItems: [], totalPages: 0 });
      }

      // 次にページ指定されたデータを取得
      db.query(dataQuery, dataParams, (err, itemsResult) => {
        if (err) {
          logErr(`ユーザー[${userId}]の所持アイテムが取得できませんでした。エラー内容:${err} - model_userItem_getPagedUserItems`);
          return reject(err);
        }

        const totalPages = Math.ceil(totalItems / pageSize);
        resolve({ userItems: itemsResult, totalPages });
      });
    });
  });
};

/* ---------------------------------------------------------------------
  Get top spenders from last week
-----------------------------------------------------------------------*/
exports.getTopSpendersLastWeek = async (limit = 20) => {
  const timeZone = "America/New_York";
  const now = new Date();
  const zonedNow = utcToZonedTime(now, timeZone);

  const lastWeek = subWeeks(zonedNow, 1);
  const startOfLastWeek = startOfWeek(lastWeek, { weekStartsOn: 0 }); // Sunday
  const endOfLastWeek = endOfWeek(lastWeek, { weekStartsOn: 0 }); // Saturday

  const startOfLastWeekUTC = zonedTimeToUtc(startOfLastWeek, timeZone);
  const endOfLastWeekUTC = zonedTimeToUtc(endOfLastWeek, timeZone);

  const sql = `
    SELECT
      u.id,
      u.name,
      u.mail as email,
      u.user_image as avatar,
      SUM(ui.price) as total_spent
    FROM user_items ui
    JOIN user u ON ui.user_id = u.id
    WHERE ui.created_at >= ? AND ui.created_at <= ?
    GROUP BY u.id
    ORDER BY total_spent DESC
    LIMIT ?;
  `;

  return new Promise((resolve, reject) => {
    db.query(sql, [startOfLastWeekUTC, endOfLastWeekUTC, limit], (err, result) => {
      if (err) {
        logErr(`Could not get top spenders from last week. Error: ${err} - model_userItem_getTopSpendersLastWeek`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};