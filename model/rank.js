const db = require("../db/db");
const { logUserPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
全ての登録されているランクを取得
-----------------------------------------------------------------------*/
exports.getAllRank = async (connection) => {
  return new Promise((resolve, reject) => {
    connection.query("SELECT * FROM `rank` ORDER BY point ASC", (err, results) => {
      if (err) {
        logErr(`全てのランクが取得できませんでした。エラー内容:${err} - draw`);
        return reject(err);
      }
      resolve(results);
      console.log("全てのランク" + results);
    });
  });
};
