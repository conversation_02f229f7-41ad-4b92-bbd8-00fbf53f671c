const Res = require("../util/responseHelpers");
const db = require("../db/db");
require("dotenv").config();
const bcrypt = require("bcrypt");
const jwt = require("jsonwebtoken");
const {logUserPut, logErr} = require("../logger");
const {log} = require("console");
const exp = require("constants");

/* ---------------------------------------------------------------------
電話番号認証後、電話番号を登録
-----------------------------------------------------------------------*/
exports.telAuth = async (tel, userId, res) => {
    const updateTelQuery = "UPDATE user SET tel = ? WHERE id = ?";
    db.query(updateTelQuery, [tel, userId], (error, result) => {
        if (error) {
            console.log(error);
            logErr(`ユーザー[${userId}]の電話番号を登録できませんでした。エラー内容:${error} - model_user_telAuth`);
            Res.resJsonFn(res, 400, null, "電話番号の登録ができませんでした。");
            return;
        }

        return result;
    });
};

/* ---------------------------------------------------------------------
電話番号の重複チェック
-----------------------------------------------------------------------*/
exports.telCheck = async (tel) => {
    return new Promise((resolve, reject) => {
        const checkPhoneQuery = "SELECT tel FROM user WHERE tel = ?";
        db.query(checkPhoneQuery, [tel], (error, results) => {
            if (error) {
                console.error("Database error:", error);
                logErr(`電話番号[${tel}]の重複チェックができませんでした。エラー内容:${error} - model_user_telCheck`);
                reject(new Error("Database query error"));
            } else {
                resolve(results);
            }
        });
    });
};

/* ---------------------------------------------------------------------
ユーザーをデータベースに登録
-----------------------------------------------------------------------*/
exports.userInsert = (email, pass) => {
    return new Promise((resolve, reject) => {
        let password = "";

        // ラインログインの場合はパスワードを自動生成
        if (pass === "line") {
            password = bcrypt.hashSync(email, 10);
        } else {
            password = pass;
        }

        const insertUserQuery = "INSERT INTO user (mail, pass, `rank`, day_point, weekly_point, month_point ,allPoint) VALUES (?, ?, ? ,?, ?, ?, ?)";

        db.query(insertUserQuery, [email, password, 0, 0, 0, 0, 0], (error, results) => {
            if (error) {
                console.error("Database error:", error);
                logErr(`ユーザー[${email}]をデータベースに登録できませんでした。エラー内容:${error} - model_user_userInsert`);
                return reject("Database insert error");
            }

            const userId = results.insertId;
            const token = jwt.sign({userId, mail: email}, process.env.USER_JWT_SECRET_KEY, {expiresIn: "30d"});

            resolve({userId, token}); // userId と token をコントローラーに返す
        });
    });
};

/* ---------------------------------------------------------------------
すでにメールアドレスが登録されているかをチェック
-----------------------------------------------------------------------*/
exports.checkEmailDb = async (email) => {
    return new Promise((resolve, reject) => {
        const checkUserQuery = "SELECT * FROM user WHERE mail = ?";
        db.query(checkUserQuery, [email], (err, results) => {
            if (err) {
                console.error("Database error:", err);
                logErr(`メールアドレス[${email}]のチェックができませんでした。エラー内容:${err} - model_user_checkEmailDb`);
                reject(new Error("Database query error"));
            } else {
                resolve(results);
            }
        });
    });
};

/* ---------------------------------------------------------------------
ログイン情報をloginHistoryテーブルに登録
-----------------------------------------------------------------------*/
exports.insertLoginHistory = async (userId) => {
    function calculateDateDifference(today, lastLoginDay) {
        const todayDate = new Date(today);
        const lastLoginDate = new Date(lastLoginDay);

        // 年、月、日付を取得
        const todayYear = todayDate.getFullYear();
        const todayMonth = todayDate.getMonth();
        const todayDay = todayDate.getDate();

        console.log("今日の日付:", todayYear, todayMonth, todayDay);

        const lastYear = lastLoginDate.getFullYear();
        const lastMonth = lastLoginDate.getMonth();
        const lastDay = lastLoginDate.getDate();

        console.log("最後のログイン日付:", lastYear, lastMonth, lastDay);

        let diffDays = 0;

        if (todayYear === lastYear && todayMonth === lastMonth) {
            // 同じ年・同じ月の場合は、日付の差を直接計算
            diffDays = todayDay - lastDay;
            console.log("ss日付の差:", diffDays);
        } else {
            // 月が違う場合は2日以上の差があると判定
            diffDays = 2;
            console.log("dd日付の差:", diffDays);
        }

        return diffDays;
    }

    return new Promise((resolve, reject) => {
        const checkLoginQuery = `
            SELECT *
            FROM loginHistory
            WHERE userId = ?
            ORDER BY loginDate DESC LIMIT 1
        `;
        const insertLoginQuery = `
            INSERT INTO loginHistory (userId, dayCount, loginDate, fastLogin)
            VALUES (?, ?, CURRENT_TIMESTAMP, ?)
        `;

        db.query(checkLoginQuery, [userId], (err, results) => {
            if (err) {
                console.error("Database error in checkLoginQuery:", err);
                logErr(`ユーザー[${userId}]のログイン情報を取得できませんでした。エラー内容:${err} - model_user_insertLoginHistory`);
                return reject(new Error("Database query error in checkLoginQuery"));
            }

            // 日本標準時（JST）に変換
            const now = new Date(new Date().getTime() + 9 * 60 * 60 * 1000);
            const today = now.toISOString().split("T")[0]; // 今日の日付（YYYY-MM-DD形式）
            console.log("現在の日本の日付:", today);

            let fastLogin = 0;
            let dayCount = 1;

            if (results.length > 0) {
                console.log("最後のログイン情報が見つかりました:", results[0]);

                const lastLogin = results[0];
                const lastLoginDate = new Date(lastLogin.loginDate);
                const lastLoginDay = new Date(lastLoginDate.getTime() + 9 * 60 * 60 * 1000).toISOString().split("T")[0];
                console.log("今日のログイン日付（日本時刻）:", today);
                console.log("最後のログイン日付（日本時刻）:", lastLoginDay);

                // 今日すでにログインしているかを確認
                if (today === lastLoginDay) {
                    console.log("今日すでにログインしています。インサートは行いません。");
                    return resolve({fastLogin: false});
                }

                // 日付ベースでの日数差を計算
                const diffDays = calculateDateDifference(today, lastLoginDay);

                if (diffDays === 1) {
                    dayCount = lastLogin.dayCount + 1;
                    console.log("連続ログインです。dayCountを増加:", dayCount);
                } else {
                    dayCount = 1; // 連続していない場合はリセット
                    console.log("連続ログインではありません。dayCountをリセット:", dayCount);
                }

                // 月の最初のログインかどうかをチェック
                const lastLoginMonth = lastLoginDate.getMonth();
                const currentMonth = now.getMonth();
                if (lastLoginMonth !== currentMonth) {
                    fastLogin = 1;
                    console.log("今月の最初のログインです。fastLoginを1に設定。");
                }
            } else {
                fastLogin = 1;
                console.log("初回のログインです。fastLoginを1に設定。");
            }

            db.query(insertLoginQuery, [userId, dayCount, fastLogin], (err, results) => {
                if (err) {
                    console.error("Database error in insertLoginQuery:", err);
                    return reject(new Error("Database query error in insertLoginQuery"));
                }

                console.log("ログイン情報が正常にインサートされました:", {userId, dayCount, fastLogin});
                resolve({fastLogin: !!fastLogin});
            });
        });
    });
};

/* ---------------------------------------------------------------------
課金の場合のユーザーポイントの更新
-----------------------------------------------------------------------*/
exports.updateUserPoints = async (userId, points, baseURL) => {
    // ユーザーのポイントを更新するクエリ
    const updateUserPointsQuery = "UPDATE user SET point = point + ?, day_point = day_point + ?, weekly_point = weekly_point + ?, month_point = month_point + ?, allPoint = allPoint + ? WHERE id = ?";

    return new Promise((resolve, reject) => {
        // ユーザーのポイントを更新
        db.query(updateUserPointsQuery, [points, points, points, points, points, userId], (err, results) => {
            if (err) {
                logErr(`ユーザー[${userId}]のポイントを更新できませんでした。エラー内容:${err} - model_user_updateUserPoints`);
                return reject(err);
            }

            logUserPut(`ユーザー[${userId}]のポイント${points}を付与しました。 - model_user_updateUserPoints`);
            return resolve(results);
        });
    });
};

/* ---------------------------------------------------------------------
ユーザーのランクを取得（ランク数値のみを取得）
-----------------------------------------------------------------------*/
exports.getUserRankValue = async (connection, userId) => {
    return new Promise((resolve, reject) => {
        connection.query("SELECT `rank` FROM user WHERE id = ?", [userId], (err, results) => {
            if (err) {
                logErr(`ユーザー[${userId}]のランクが取得できませんでした。エラー内容:${err} - user_model_getUserRankValue`);
                return reject(err);
            }
            resolve(results[0].rank);
            console.log("ユーザーの現在のランク" + results[0].rank);
        });
    });
};

/* ---------------------------------------------------------------------
ユーザーのランクを更新
-----------------------------------------------------------------------*/
exports.updateUserRank = async (connection, newRank, userId) => {
    return new Promise((resolve, reject) => {
        connection.query("UPDATE user SET `rank` = ? WHERE id = ?", [newRank, userId], (err, results) => {
            if (err) {
                logErr(`ユーザー[${userId}]のランクが更新されませんでした。エラー内容:${err} - draw`);
                return reject(err);
            }
            resolve(results);
        });
    });
};

/* ---------------------------------------------------------------------
获取用户的GP赋予率
-----------------------------------------------------------------------*/
exports.getGpRate = async (connection, userRank, userId) => {
    return new Promise((resolve, reject) => {
        connection.query("SELECT gp FROM `rank` WHERE `order` = ?", [userRank], (err, results) => {
            if (err) {
                // 数据库查询错误日志
                logErr(`用户[${userId}]的等级[${userRank}]的GP点数获取失败。错误内容: ${err} - user_model_getGpRate`);
                return reject(err);
            }

            // 打印查询结果，方便排查问题
            console.log(`用户[${userId}]的等级[${userRank}]查询结果:`, results);

            // 检查 results 是否为空或长度为 0
            if (!results || results.length === 0) {
                const errorMessage = `用户[${userId}]的等级[${userRank}]未找到对应的数据。`;
                logErr(`${errorMessage} - user_model_getGpRate`);
                return reject(new Error(errorMessage));
            }

            // 检查 results[0].gp 是否存在
            if (!results[0].gp) {
                const errorMessage = `用户[${userId}]的等级[${userRank}]的GP点数未设置。`;
                logErr(`${errorMessage} - user_model_getGpRate`);
                return reject(new Error(errorMessage));
            }

            // 返回 GP 值
            resolve(results[0].gp);
        });
    });
};

/* ---------------------------------------------------------------------
ユーザーの使用ポイントのみを取得
-----------------------------------------------------------------------*/
exports.getUserUsePoint = async (connection, userId) => {
    return new Promise((resolve, reject) => {
        connection.query("SELECT use_point FROM user WHERE id = ?", [userId], (err, results) => {
            if (err) {
                logErr(`ユーザー[${userId}]のuse_pointが取得できませんでした。エラー内容:${err} - user_model_getUserUsePoint`);
                return reject(err);
            }
            resolve(results[0].use_point);
            console.log("ユーザーのuse_point" + results[0].use_point);
        });
    });
};

/* ---------------------------------------------------------------------
ユーザーのGPを更新
-----------------------------------------------------------------------*/
exports.updateUserGp = async (connection, gpEarned, userId, packInfoId) => {
    return new Promise((resolve, reject) => {
        connection.query("UPDATE user SET gp = gp + ? WHERE id = ?", [gpEarned, userId], (err, results) => {
            if (err) {
                logErr(`ユーザー[${userId}]が引いたガチャ[${packInfoId}]のGPポイントがユーザーの持ちGPポイントに反映されませんでした。エラー内容:${err} - user_model_updateUserGp`);
                return reject(err);
            }
            resolve(results);
        });
    });
};


/**
 * 扣除用户当前积分（point），如果扣除后小于0，则设为0
 * @param {number} userId - 用户ID
 * @param {number} pointsToDeduct - 要扣除的积分数
 * @returns {Promise<void>}
 */
exports.deductUserPoints = async (userId, pointsToDeduct) => {
    return new Promise((resolve, reject) => {
        // 使用 GREATEST 确保 point 不会小于 0
        const deductPointsQuery = `
            UPDATE user 
            SET point = GREATEST(point - ?, 0)
            WHERE id = ?`;

        db.query(deductPointsQuery, [pointsToDeduct, userId], (err, results) => {
            if (err) {
                console.error(`用户[${userId}]扣除积分失败。错误内容: ${err}`);
                logErr(`用户[${userId}]扣除积分失败。错误内容:${err} - deductUserPoints`);
                return reject(err);
            }

            // 扣除成功，记录日志（可选查询当前值）
            logUserPut(`用户[${userId}]扣除积分${pointsToDeduct}点成功（如有不足则设为0）。 - deductUserPoints`);

            resolve(results);
        });
    });
};


/**
 * 记录发货时的积分扣除明细
 * @param {number} userId - 用户ID
 * @param {Array} itemIds - 商品ID数组
 * @param {number} deductedPoints - 扣除的积分数（如100）
 * @param {number} totalPrice - 所有商品总价
 * @returns {Promise<void>}
 */
exports.recordShippingPointDeduction = async (userId, itemIds, deductedPoints, totalPrice) => {
    const query = `
        INSERT INTO shipping_point_records (user_id, item_ids, deducted_points, total_price)
        VALUES (?, ?, ?, ?)
    `;

    return new Promise((resolve, reject) => {
        db.query(query, [userId, JSON.stringify(itemIds), deductedPoints, totalPrice], (err, results) => {
            if (err) {
                console.error(`记录用户[${userId}]积分扣除日志失败。错误内容: ${err}`);
                logErr(`记录用户[${userId}]积分扣除日志失败。错误内容:${err} - recordShippingPointDeduction`);
                return reject(err);
            }

            logUserPut(`用户[${userId}]积分扣除记录已保存。扣除积分: ${deductedPoints}, 总价: ${totalPrice} - recordShippingPointDeduction`);
            resolve(results);
        });
    });
};

/* ---------------------------------------------------------------------
ユーザーの所持ポイントを取得
-----------------------------------------------------------------------*/
exports.getUserPoint = async (connection, userId) => {
    return new Promise((resolve, reject) => {
        connection.query("SELECT point FROM user WHERE id = ?", [userId], (err, results) => {
            if (err) {
                logErr(`ユーザー[${userId}]のポイントが取得できませんでした。エラー内容:${err} - draw`);
                return reject(err);
            }
            resolve(results[0].point);
        });
    });
};

/* ---------------------------------------------------------------------
获取用户的等级信息
-----------------------------------------------------------------------*/
exports.getUserRank = async (userId, baseURL) => {
    // 查询 user 表中的 rank 列
    const getUserRankQuery = "SELECT `rank` FROM user WHERE id = ?";
    // 查询 user 表中的 use_point 列
    const getUserRankPointQuery = "SELECT use_point FROM user WHERE id = ?";
    // 根据 user 表的 rank 列查询 rank 表中的 point 列
    const getRankPointQuery = "SELECT point FROM `rank` WHERE `order` = ?";
    // 根据 user 表的 rank 列查询 rank 表中的完整记录
    const getRankQuery = "SELECT * FROM `rank` WHERE `order` = ?";
    // 查询 rank 表中倒数第二个记录的 order 列
    const getRankQueryLast = "SELECT * FROM `rank` ORDER BY `order` DESC LIMIT 1 OFFSET ?";

    return new Promise((resolve, reject) => {
        // 查询用户当前的 rank 值
        db.query(getUserRankQuery, [userId], (err, userResults) => {
            if (err) {
                logErr(`无法获取用户[${userId}]的等级信息。错误内容:${err} - model_user_getUserRank`);
                return reject(err);
            }

            if (!userResults || userResults.length === 0) {
                logErr(`未找到用户[${userId}]的等级信息。 - model_user_getUserRank`);
                return reject(new Error("未找到用户等级信息"));
            }

            // 查询用户的 use_point 值
            db.query(getUserRankPointQuery, [userId], (err, userRankPointResults) => {
                if (err) {
                    logErr(`无法获取用户[${userId}]的等级积分。错误内容:${err} - model_user_getUserRank`);
                    return reject(err);
                }

                // 如果查询结果为空，返回错误
                if (userRankPointResults.length === 0) {
                    logErr(`未找到用户[${userId}]的等级积分。 - model_user_getUserRank`);
                    return reject(new Error("未找到等级积分"));
                }

                logUserPut(`已获取用户[${userId}]的等级积分[${JSON.stringify(userRankPointResults, null, 0)}]。 - model_user_getUserRank`);

                const offsetValue = 1;

                // 查询 rank 表中倒数第二个记录的信息
                db.query(getRankQueryLast, [offsetValue], (err, lastRankResults) => {
                    if (err) {
                        logErr(`无法获取主等级表中倒数第二个记录的信息。错误内容:${err} - model_user_getUserRank`);
                        return reject(err);
                    }

                    console.log(lastRankResults);
                    logUserPut(`已获取用户[${userId}]的最后等级信息[${userResults}]。 - model_user_getUserRank`);
                    logUserPut(`正在排查用户[${userId}]崩溃的原因的日志 - model_user_getUserRank`);
                    logUserPut(userResults);

                    // 查询当前用户的等级信息
                    db.query(getRankQuery, [Number(userResults[0].rank)], (err, nowRankResults) => {
                        if (err) {
                            logErr(`无法获取用户[${userId}]的当前等级信息。错误内容:${err} - model_user_getUserRank`);
                            return reject(err);
                        }

                        // 如果用户处于最高等级
                        if (lastRankResults[0].order === userResults[0].rank) {
                            resolve({
                                nowRankPoint: userRankPointResults[0].use_point,
                                nowRankName: nowRankResults[0].name,
                                nowRankImg: baseURL + nowRankResults[0].back_img,
                                nextRankPoint: 0,
                                lastRank: true,
                            });
                        } else {
                            // 查询下一等级所需的积分
                            db.query(getRankPointQuery, [Number(userResults[0].rank) + 1], (err, nextRankPointResults) => {
                                if (err) {
                                    logErr(`无法获取用户[${userId}]的下一等级积分。错误内容:${err} - model_user_getUserRank`);
                                    return reject(err);
                                }

                                if (nextRankPointResults.length === 0) {
                                    resolve({
                                        nowRankPoint: userRankPointResults[0].use_point,
                                        nowRankName: nowRankResults[0].name,
                                        nowRankImg: baseURL + nowRankResults[0].back_img,
                                        nextRankPoint: 0,
                                        lastRank: false,
                                    });
                                } else {
                                    resolve({
                                        nowRankPoint: userRankPointResults[0].use_point,
                                        nowRankName: nowRankResults[0].name,
                                        nowRankImg: baseURL + nowRankResults[0].back_img,
                                        nextRankPoint: nextRankPointResults[0].point,
                                        lastRank: false,
                                    });
                                }
                            });
                        }
                    });
                });
            });
        });
    });
};

exports.getBuyAddPointPercent = async (userId) => {
    // 查询 user 表中的 rank 列的 SQL 语句
    const getUserRankQuery = "SELECT `rank` FROM user WHERE id = ?";
    // 根据 getUserRankQuery 的结果，查询 rank 表中的 order 列，并获取对应的记录
    const getRankQuery = "SELECT * FROM `rank` WHERE `order` = ?";

    return new Promise((resolve, reject) => {
        db.query(getUserRankQuery, [userId], (err, userResults) => {
            if (err) {
                logErr(`无法获取用户[${userId}]的等级信息。错误内容:${err} - model_user_getBuyAddPointPercent`);
                return reject(err);
            }

            // 防止崩溃，进行日志记录
            logUserPut(`已获取用户[${userId}]的最后等级信息[${userResults}]。 - model_user_getBuyAddPointPercent`);
            logUserPut(userResults);

            db.query(getRankQuery, [Number(userResults[0].rank)], (err, nowRankResults) => {
                if (err) {
                    logErr(`无法获取用户[${userId}]的当前等级信息。错误内容:${err} - model_user_getBuyAddPointPercent`);
                    return reject(err);
                }

                resolve({ percent: nowRankResults[0].buy_add_point_percent, rank: userResults[0].rank });
            });
        });
    });
};

/* ---------------------------------------------------------------------
确认用户是否存在登录奖励
-----------------------------------------------------------------------*/
exports.searchUserLoginDays = async (userId) => {
    // 从 loginHistory 表中获取与 userId 匹配的最新记录的 dayCount 列
    const searchUserLoginBonusQuery = "SELECT dayCount FROM loginHistory WHERE userId = ? ORDER BY loginDate DESC LIMIT 1";

    try {
        // 确认用户的登录奖励是否存在
        return await new Promise((resolve, reject) => {
            db.query(searchUserLoginBonusQuery, [userId], (err, results) => {
                if (err) {
                    logErr(`无法确认用户[${userId}]的登录奖励是否存在。错误内容: ${err} - model_user_searchUserLoginDays`);
                    console.error(`查询用户[${userId}]的登录奖励时出错：`, err);
                    reject(err);
                }

                if (results.length === 0) {
                    console.warn(`未找到用户[${userId}]的登录奖励记录。`);
                    resolve(false); // 返回 false 表示无记录
                } else {
                    console.log(`成功获取用户[${userId}]的登录奖励天数：`, results[0].dayCount);
                    resolve(results[0].dayCount); // 返回登录天数
                }
            });
        });
    } catch (error) {
        console.error(`执行确认用户[${userId}]的登录奖励时发生异常：`, error);
    }
};

/* ---------------------------------------------------------------------
日付から該当のログインボーナスを取得
-----------------------------------------------------------------------*/
exports.getLoginBonus = async (dayCount) => {
    // loginBonusテーブルのdayカラムと引数のdayCountが一致するレコードを取得するクエリ
    const getLoginBonusQuery = "SELECT * FROM loginBonus WHERE day = ?";

    try {
        return await new Promise((resolve, reject) => {
            db.query(getLoginBonusQuery, [dayCount], (err, results) => {
                if (err) {
                    logErr(`日付[${dayCount}]から該当のログインボーナスを取得できませんでした。エラー内容:${err} - model_user_getLoginBonus`);
                    reject(err);
                }

                resolve(results);
            });
        });
    } catch (error) {
        console.error("Error fetching user rank:", error);
    }
};

/* ---------------------------------------------------------------------
ユーザーの最終ログインボーナス取得日時を取得
-----------------------------------------------------------------------*/
exports.getLastBonusClaimDate = async (userId) => {
    // ユーザーidと一致するuserテーブルのlast_bonus_claim_dateカラムを取得するクエリ
    const getLastBonusClaimDateQuery = "SELECT last_bonus_claim_date FROM user WHERE id = ?";

    try {
        return new Promise((resolve, reject) => {
            db.query(getLastBonusClaimDateQuery, [userId], (err, results) => {
                if (err) {
                    logErr(`ユーザー[${userId}]の最終ログインボーナス取得日時を取得できませんでした。エラー内容:${err} - model_user_getLastBonusClaimDate`);
                    reject(err);
                }

                if (!results || results.length === 0) {
                    logErr(`ユーザー[${userId}]の最終ログインボーナス取得日時が見つかりませんでした。 - model_user_getLastBonusClaimDate`);
                    return reject(new Error("最終ログインボーナス取得日時が見つかりません"));
                }

                resolve(results[0].last_bonus_claim_date);
            });
        });
    } catch (error) {
        console.error("Error fetching user rank:", error);
    }
};

/* ---------------------------------------------------------------------
获取对应的登录奖励点数的ID
-----------------------------------------------------------------------*/
exports.getLoginBonusPointId = async (bonusId) => {
    // 查询 loginBonusPoints 表中与 bonusId 匹配的记录的 point 列
    const getLoginBonusPointQuery = "SELECT id FROM loginBonusPoints WHERE login_bonus_id = ?";

    try {
        return new Promise((resolve, reject) => {
            db.query(getLoginBonusPointQuery, [bonusId], (err, results) => {
                if (err) {
                    logErr(`无法获取登录奖励[${bonusId}]的点数ID。错误内容：${err} - model_user_getLoginBonusPointId`);
                    console.error(`查询登录奖励[${bonusId}]的点数ID时出错：`, err);
                    reject(err);
                }

                console.log(`成功获取登录奖励[${bonusId}]的点数ID列表：`, results);
                resolve(results);
            });
        });
    } catch (error) {
        console.error(`执行获取登录奖励[${bonusId}]的点数ID时发生异常：`, error);
    }
};

/* ---------------------------------------------------------------------
获取对应的登录奖励点数
-----------------------------------------------------------------------*/
exports.getLoginBonusPoint = async (pointId) => {
    // 查询 loginBonusPoints 表中与 pointId 匹配的记录的 point 列
    const getLoginBonusPointQuery = "SELECT * FROM loginBonusPoints WHERE id = ?";

    try {
        return new Promise((resolve, reject) => {
            db.query(getLoginBonusPointQuery, [pointId], (err, results) => {
                if (err) {
                    logErr(`无法获取登录奖励点数[${pointId}]。错误内容: ${err} - model_user_getLoginBonusPoint`);
                    console.error(`查询登录奖励点数[${pointId}]时出错：`, err);
                    reject(err);
                }

                if (!results || results.length === 0) {
                    console.warn(`未找到登录奖励点数[${pointId}]的记录。`);
                    resolve(null); // 明确返回 null 表示未找到记录
                } else {
                    console.log(`成功获取登录奖励点数[${pointId}]的记录：`, results[0]);
                    resolve(results[0]); // 返回查询结果
                }
            });
        });
    } catch (error) {
        console.error(`执行获取登录奖励点数[${pointId}]时发生异常：`, error);
    }
};

/* ---------------------------------------------------------------------
ユーザーポイントの更新（ランク更新を含まない・ログインボーナス用）
-----------------------------------------------------------------------*/
exports.updateUserPointsNoRank = async (userId, points) => {
    // userIdとuserテーブルのidで一致するレコードのpointカラムにpointsを加算するクエリ
    const updateUserPointsQuery = "UPDATE user SET point = point + ?, last_bonus_claim_date = NOW() WHERE id = ?";

    try {
        return new Promise((resolve, reject) => {
            db.query(updateUserPointsQuery, [Number(points), userId], (err, results) => {
                if (err) {
                    logErr(`ユーザー[${userId}]のポイントを更新できませんでした。エラー内容:${err} - model_user_updateUserPointsNoRank`);
                    reject(err);
                }

                logUserPut(`ユーザー[${userId}]のポイント${points}を付与しました。 - model_user_updateUserPointsNoRank`);
                resolve(results);
            });
        });
    } catch (error) {
        console.error("Error updating user points:", error);
    }
};

/* --------------------------------------------------------------------------------
ユーザーポイントの更新（ランク更新を含まない・ログインボーナスではない・管理者からのアイテム交換・ランクアップボーナス用）
----------------------------------------------------------------------------------*/
exports.updateUserPointsChange = async (connection, userId, points) => {
    // userIdとuserテーブルのidで一致するレコードのpointカラムにpointsを加算するクエリ
    const updateUserPointsQuery = "UPDATE user SET point = point + ? WHERE id = ?";

    try {
        return new Promise((resolve, reject) => {
            connection.query(updateUserPointsQuery, [Number(points), userId], (err, results) => {
                if (err) {
                    logErr(`ユーザー[${userId}]のポイントを更新できませんでした。エラー内容:${err} - model_user_updateUserPointsChange`);
                    reject(err);
                }

                logUserPut(`ユーザー[${userId}]のポイント${points}を付与しました。 - model_user_updateUserPointsChange`);
                resolve(results);
            });
        });
    } catch (error) {
        console.error("Error updating user points:", error);
    }
};

/* ---------------------------------------------------------------------
line_login_stateテーブルに同じステートがあるか確認
-----------------------------------------------------------------------*/
exports.checkLineState = async (state) => {
    // line_login_stateテーブルにステートを確認するクエリ
    const insertLineStateQuery = "SELECT * FROM line_login_state WHERE state = ?";

    try {
        return new Promise((resolve, reject) => {
            db.query(insertLineStateQuery, [state], (err, results) => {
                if (err) {
                    logErr(`LINEステート[${state}]を確認できませんでした。エラー内容:${err} - model_user_checkLineState`);
                    reject(err);
                }

                resolve(results);
            });
        });
    } catch (error) {
        console.error("Error inserting line state:", error);
    }
};

/* ---------------------------------------------------------------------
line_login_stateテーブルにステートを登録
-----------------------------------------------------------------------*/
exports.insertLineState = async (state) => {
    // line_login_stateテーブルにステートを登録するクエリ
    const insertLineStateQuery = "INSERT INTO line_login_state (state) VALUES (?)";

    try {
        return new Promise((resolve, reject) => {
            db.query(insertLineStateQuery, [state], (err, results) => {
                if (err) {
                    logErr(`LINEステート[${state}]を登録できませんでした。エラー内容:${err} - model_user_insertLineState`);
                    reject(err);
                }

                resolve(results);
            });
        });
    } catch (error) {
        console.error("Error inserting line state:", error);
    }
};

/* ---------------------------------------------------------------------
line_login_stateテーブルからステートを削除
-----------------------------------------------------------------------*/
exports.deleteLineState = async (state) => {
    // line_login_stateテーブルからステートを削除するクエリ
    const deleteLineStateQuery = "DELETE FROM line_login_state WHERE state = ?";

    try {
        return new Promise((resolve, reject) => {
            db.query(deleteLineStateQuery, [state], (err, results) => {
                if (err) {
                    logErr(`LINEステート[${state}]を削除できませんでした。エラー内容:${err} - model_user_deleteLineState`);
                    reject(err);
                }

                resolve(results);
            });
        });
    } catch (error) {
        console.error("Error deleting line state:", error);
        console.log("Error deleting line state:", error);
    }
};
