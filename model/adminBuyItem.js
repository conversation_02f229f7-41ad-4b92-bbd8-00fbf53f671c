const db = require("../db/db");
const { logAdminPut, logErr } = require("../logger");

/* --------------------------------------------------------------------- 
課金アイテムを追加
-----------------------------------------------------------------------*/
exports.addBuyItem = async (point, image) => {
  // buy_itemテーブルに、pointとimageをインサートするプリペアードステートメントを用いたクエリ
  const query = "INSERT INTO buy_items (point, image) VALUES (?, ?)";

  return new Promise((resolve, reject) => {
    db.query(query, [point, image], (err, result) => {
      if (err) {
        logErr(`課金アイテムを追加できませんでした。エラー内容:${err} - model_adminBuyItem_addBuyItem`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
課金アイテムを取得
-----------------------------------------------------------------------*/
exports.getBuyItem = async () => {
  // buy_itemテーブルのデータを全て取得するクエリ
  const query = "SELECT * FROM buy_items";

  return new Promise((resolve, reject) => {
    db.query(query, (err, result) => {
      if (err) {
        logErr(`課金アイテムを取得できませんでした。エラー内容:${err} - model_adminBuyItem_getBuyItem`);
        console.log("Error fetching buy items:", err);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当IDの課金アイテムを取得
-----------------------------------------------------------------------*/
exports.getIdBuyItem = async (id) => {
  // buy_itemテーブルから、指定したidのデータを取得するクエリ
  const query = "SELECT * FROM buy_items WHERE id = ?";

  return new Promise((resolve, reject) => {
    db.query(query, [id], (err, result) => {
      if (err) {
        logErr(`課金アイテムを取得できませんでした。エラー内容:${err} - model_adminBuyItem_getIdBuyItem`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
該当IDの課金アイテムを削除
-----------------------------------------------------------------------*/
exports.removeBuyItem = async (id) => {
  // buy_itemテーブルから、指定したidのデータを削除するクエリ
  const query = "DELETE FROM buy_items WHERE id = ?";

  return new Promise((resolve, reject) => {
    db.query(query, [id], (err, result) => {
      if (err) {
        logErr(`課金アイテムを削除できませんでした。エラー内容:${err} - model_adminBuyItem_removeBuyItem`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
課金アイテムの更新
-----------------------------------------------------------------------*/
exports.updateBuyItem = async (id, point, image) => {
  // buy_itemテーブルの指定したidのデータを更新するクエリ
  const query = "UPDATE buy_items SET point = ?, image = ? WHERE id = ?";

  return new Promise((resolve, reject) => {
    db.query(query, [point, image, id], (err, result) => {
      if (err) {
        logErr(`課金アイテムを更新できませんでした。エラー内容:${err} - model_adminBuyItem_updateBuyItem`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};

/* --------------------------------------------------------------------- 
画像がない場合の課金アイテムの更新
-----------------------------------------------------------------------*/
exports.updateBuyItemNoImage = async (id, point) => {
  // buy_itemテーブルの指定したidのデータを更新するクエリ
  const query = "UPDATE buy_items SET point = ? WHERE id = ?";

  return new Promise((resolve, reject) => {
    db.query(query, [point, id], (err, result) => {
      if (err) {
        logErr(`課金アイテムを更新できませんでした。エラー内容:${err} - model_adminBuyItem_updateBuyItemNoImage`);
        reject(err);
      } else {
        resolve(result);
      }
    });
  });
};
